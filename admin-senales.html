<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - <PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen p-4">

    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">🚀 GERMAYORI ADMIN</h1>
            <p class="text-white/80">Panel Rápido de Señales</p>
        </div>

        <!-- Quick Signal Form -->
        <div class="glass rounded-2xl p-8 mb-6">
            <h2 class="text-2xl font-bold text-white mb-6">⚡ Crear Señal Rápida</h2>

            <form id="quick-signal-form" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Par de Divisas</label>
                        <select id="pair" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400">
                            <option value="eurusd">EUR/USD</option>
                            <option value="gbpusd">GBP/USD</option>
                            <option value="usdjpy">USD/JPY</option>
                            <option value="audusd">AUD/USD</option>
                            <option value="usdcad">USD/CAD</option>
                            <option value="nzdusd">NZD/USD</option>
                            <option value="eurjpy">EUR/JPY</option>
                            <option value="gbpjpy">GBP/JPY</option>
                            <option value="xauusd">🥇 XAU/USD (Gold)</option>
                            <option value="nas100">📈 NAS100 (Nasdaq)</option>
                            <option value="us30">📊 US30 (Dow Jones)</option>
                            <option value="spx500">📈 SPX500 (S&P 500)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Acción</label>
                        <select id="action" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400">
                            <option value="buy">🟢 BUY</option>
                            <option value="sell">🔴 SELL</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Precio de Entrada</label>
                        <input type="number" step="0.00001" id="entry" placeholder="1.0850" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                    </div>

                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Stop Loss</label>
                        <input type="number" step="0.00001" id="stoploss" placeholder="1.0800" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Take Profit 1</label>
                        <input type="number" step="0.00001" id="takeprofit1" placeholder="1.0900" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                    </div>

                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Take Profit 2</label>
                        <input type="number" step="0.00001" id="takeprofit2" placeholder="1.0950" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                    </div>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">📝 Análisis GERMAYORI</label>
                    <textarea id="analysis" placeholder="Explica por qué tomamos esta señal en este precio. Ej: Rompimiento de resistencia clave, retroceso de Fibonacci 61.8%, confluencia con EMA 200..." class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400 h-24 resize-none" required></textarea>
                </div>

                <button type="submit" class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-bold py-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-all">
                    <i class="fas fa-rocket mr-2"></i>🚀 CREAR SEÑAL AHORA
                </button>
            </form>
        </div>

        <!-- Current Signals -->
        <div class="glass rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-white mb-6">📈 Descripción de Señales</h2>
            <div class="bg-white/10 rounded-lg p-4 border border-white/20">
                <label class="block text-white/80 text-sm font-medium mb-2">💬 Descripción General</label>
                <textarea id="general-description" placeholder="Escribe aquí una descripción general sobre las señales, el mercado actual, o cualquier información adicional que quieras compartir..." class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400 h-32 resize-none"></textarea>
                <div class="flex gap-3 mt-3">
                    <button onclick="saveDescription()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>Guardar Descripción
                    </button>
                    <button onclick="clearAllSignals()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                        <i class="fas fa-trash mr-2"></i>Borrar Todas las Señales
                    </button>
                    <button onclick="clearAWSSignals()" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                        <i class="fas fa-aws mr-2"></i>Limpiar AWS
                    </button>
                </div>
            </div>
            <div id="current-signals" class="space-y-4 mt-6">
                <!-- Las señales aparecerán aquí -->
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center mt-8">
            <a href="dashboard-simple.html" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all">
                <i class="fas fa-arrow-left mr-2"></i>Volver al Dashboard
            </a>
        </div>
    </div>

    <script>
        // Señales almacenadas localmente
        let signals = JSON.parse(localStorage.getItem('germayori_signals')) || [];

        // Función para crear señal
        document.getElementById('quick-signal-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const newSignal = {
                id: 'signal_' + Date.now(),
                pair: document.getElementById('pair').value,
                action: document.getElementById('action').value,
                entry: document.getElementById('entry').value,
                stoploss: document.getElementById('stoploss').value,
                takeprofit1: document.getElementById('takeprofit1').value,
                takeprofit2: document.getElementById('takeprofit2').value,
                analysis: document.getElementById('analysis').value,
                timestamp: new Date().toISOString(),
                author: 'GERMAYORI'
            };

            signals.push(newSignal);
            localStorage.setItem('germayori_signals', JSON.stringify(signals));

            alert('✅ ¡Señal creada exitosamente!');
            document.getElementById('quick-signal-form').reset();
            loadCurrentSignals();
        });

        // Función para cargar señales actuales
        function loadCurrentSignals() {
            const container = document.getElementById('current-signals');

            if (signals.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-white/60 py-8">
                        <i class="fas fa-edit text-3xl mb-2"></i>
                        <p>Área libre para escribir descripciones personalizadas</p>
                        <p class="text-sm">Las señales creadas aparecerán aquí</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = signals.map(signal => `
                <div class="bg-white/10 rounded-lg p-4 border border-white/20">
                    <div class="flex justify-between items-center mb-3">
                        <div>
                            <span class="font-bold text-white text-lg">${signal.pair.toUpperCase()}</span>
                            <div class="text-xs text-cyan-400 font-semibold">📊 Señal enviada y analizada por ${signal.author || 'GERMAYORI'}</div>
                        </div>
                        <span class="bg-${signal.action === 'buy' ? 'green' : 'red'}-500 text-white px-3 py-1 rounded text-sm font-bold">
                            ${signal.action.toUpperCase()}
                        </span>
                    </div>
                    <div class="text-sm text-white/80 grid grid-cols-2 gap-2 mb-3">
                        <div>Entrada: ${signal.entry}</div>
                        <div>Stop Loss: ${signal.stoploss}</div>
                        <div>TP1: ${signal.takeprofit1}</div>
                        <div>TP2: ${signal.takeprofit2}</div>
                    </div>
                    ${signal.analysis ? `
                        <div class="bg-blue-500/20 border border-blue-400/30 rounded-lg p-3 mb-3">
                            <div class="text-blue-300 text-xs font-semibold mb-1">📝 ANÁLISIS GERMAYORI:</div>
                            <div class="text-white/90 text-sm">${signal.analysis}</div>
                        </div>
                    ` : ''}
                    <div class="flex justify-between items-center">
                        <div class="text-xs text-white/60">
                            ${new Date(signal.timestamp).toLocaleString()}
                        </div>
                        <button onclick="deleteSignal('${signal.id}')" class="bg-red-500 text-white px-3 py-1 rounded text-xs hover:bg-red-600">
                            <i class="fas fa-trash mr-1"></i>Eliminar
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Función para eliminar señal
        function deleteSignal(signalId) {
            if (confirm('¿Estás seguro de eliminar esta señal?')) {
                signals = signals.filter(s => s.id !== signalId);
                localStorage.setItem('germayori_signals', JSON.stringify(signals));
                loadCurrentSignals();
                alert('✅ Señal eliminada');
            }
        }

        // Función para guardar descripción
        function saveDescription() {
            const description = document.getElementById('general-description').value;
            localStorage.setItem('germayori_general_description', description);
            alert('✅ Descripción guardada exitosamente');
        }

        // Función para cargar descripción guardada
        function loadDescription() {
            const savedDescription = localStorage.getItem('germayori_general_description');
            if (savedDescription) {
                document.getElementById('general-description').value = savedDescription;
            }
        }

        // Función para borrar todas las señales
        function clearAllSignals() {
            if (confirm('⚠️ ¿Estás seguro de borrar TODAS las señales? Esta acción no se puede deshacer.')) {
                signals = [];
                localStorage.setItem('germayori_signals', JSON.stringify(signals));
                loadCurrentSignals();
                alert('✅ Todas las señales han sido eliminadas');
            }
        }

        // Función para limpiar datos de AWS y localStorage
        function clearAWSSignals() {
            if (confirm('🔥 ¿Limpiar TODOS los datos (AWS + localStorage)? Esto borrará todo.')) {
                // Limpiar localStorage
                localStorage.removeItem('germayori_signals');
                localStorage.removeItem('germayori_general_description');

                // Reiniciar variables
                signals = [];

                // Limpiar formularios
                document.getElementById('quick-signal-form').reset();
                document.getElementById('general-description').value = '';

                // Recargar vista
                loadCurrentSignals();

                alert('🔥 TODO limpio: AWS + localStorage + formularios');
            }
        }

        // Cargar señales al iniciar
        window.onload = function() {
            loadCurrentSignals();
            loadDescription();
        };
    </script>
</body>
</html>
