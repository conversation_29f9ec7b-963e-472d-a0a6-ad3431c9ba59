import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';
import Card from '../components/UI/Card';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.email) {
      newErrors.email = 'El email es requerido';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }
    
    if (!formData.password) {
      newErrors.password = 'La contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const newErrors = validateForm();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setLoading(true);
    const result = await login(formData.email, formData.password);
    
    if (result.success) {
      navigate('/dashboard');
    } else {
      setErrors({ general: result.error });
    }
    setLoading(false);
  };

  const fillTestCredentials = (userType) => {
    if (userType === 'premium') {
      setFormData({
        email: '<EMAIL>',
        password: '123456'
      });
    } else {
      setFormData({
        email: '<EMAIL>',
        password: '123456'
      });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-primary via-secondary to-accent">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        {/* Logo */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring" }}
            className="w-16 h-16 bg-gradient-danger rounded-xl flex items-center justify-center mx-auto mb-4"
          >
            <span className="text-white font-bold text-2xl">G</span>
          </motion.div>
          <h1 className="text-3xl font-bold text-gradient mb-2">GERMAYORI</h1>
          <p className="text-gray-400">Trading Educativo Profesional</p>
        </div>

        <Card>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-text mb-2">Iniciar Sesión</h2>
              <p className="text-gray-400">Accede a tu cuenta de trading</p>
            </div>

            {errors.general && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="bg-danger bg-opacity-20 border border-danger rounded-lg p-3"
              >
                <p className="text-danger text-sm">{errors.general}</p>
              </motion.div>
            )}

            <Input
              label="Email"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              icon={Mail}
              error={errors.email}
              required
            />

            <Input
              label="Contraseña"
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="••••••••"
              icon={Lock}
              error={errors.password}
              required
            />

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input type="checkbox" className="rounded border-accent text-danger focus:ring-danger" />
                <span className="ml-2 text-sm text-gray-400">Recordarme</span>
              </label>
              <Link to="/forgot-password" className="text-sm text-danger hover:text-red-400 transition-colors">
                ¿Olvidaste tu contraseña?
              </Link>
            </div>

            <Button
              type="submit"
              variant="primary"
              className="w-full"
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
            </Button>

            <div className="text-center">
              <p className="text-gray-400 text-sm">
                ¿No tienes cuenta?{' '}
                <Link to="/register" className="text-danger hover:text-red-400 transition-colors font-medium">
                  Regístrate aquí
                </Link>
              </p>
            </div>
          </form>

          {/* Test credentials */}
          <div className="mt-6 pt-6 border-t border-accent">
            <p className="text-center text-sm text-gray-400 mb-3">Cuentas de prueba:</p>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fillTestCredentials('premium')}
                className="text-xs"
              >
                Usuario Premium
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fillTestCredentials('free')}
                className="text-xs"
              >
                Usuario Gratuito
              </Button>
            </div>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-gray-500 text-sm">
            © 2024 GERMAYORI. Todos los derechos reservados.
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default Login;
