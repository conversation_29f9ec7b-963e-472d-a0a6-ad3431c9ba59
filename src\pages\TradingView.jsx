import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { BarChart3, TrendingUp, TrendingDown, RefreshCw, Settings, Maximize2 } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';

const TradingView = () => {
  const chartRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');
  const [timeframe, setTimeframe] = useState('1H');

  const symbols = [
    { symbol: 'EURUSD', name: 'EUR/USD', price: '1.0845', change: '+0.12%', trend: 'up' },
    { symbol: 'GBPUSD', name: 'GBP/USD', price: '1.2634', change: '-0.08%', trend: 'down' },
    { symbol: 'USDJPY', name: 'USD/JPY', price: '149.85', change: '+0.25%', trend: 'up' },
    { symbol: 'USDCAD', name: 'USD/CAD', price: '1.3567', change: '+0.05%', trend: 'up' },
    { symbol: 'AUDUSD', name: 'AUD/USD', price: '0.6523', change: '-0.15%', trend: 'down' },
    { symbol: 'NZDUSD', name: 'NZD/USD', price: '0.5987', change: '+0.03%', trend: 'up' }
  ];

  const timeframes = ['1M', '5M', '15M', '30M', '1H', '4H', '1D', '1W'];

  useEffect(() => {
    // Verificar si TradingView está disponible
    const loadTradingView = () => {
      if (window.TradingView && chartRef.current) {
        try {
          new window.TradingView.widget({
            autosize: true,
            symbol: `FX_IDC:${selectedSymbol}`,
            interval: timeframe,
            timezone: "Etc/UTC",
            theme: "dark",
            style: "1",
            locale: "es",
            toolbar_bg: "#1a1a2e",
            enable_publishing: false,
            allow_symbol_change: true,
            container_id: "tradingview-chart",
            backgroundColor: "#16213e",
            gridColor: "#0f3460",
            hide_top_toolbar: false,
            hide_legend: false,
            save_image: false,
            studies: [
              "MASimple@tv-basicstudies",
              "RSI@tv-basicstudies"
            ]
          });
          setIsLoading(false);
        } catch (error) {
          console.error('Error loading TradingView:', error);
          setIsLoading(false);
        }
      } else {
        // Intentar cargar el script si no está disponible
        if (!document.querySelector('script[src*="tradingview"]')) {
          const script = document.createElement('script');
          script.src = 'https://s3.tradingview.com/tv.js';
          script.async = true;
          script.onload = () => {
            setTimeout(loadTradingView, 1000);
          };
          script.onerror = () => {
            console.error('Failed to load TradingView script');
            setIsLoading(false);
          };
          document.head.appendChild(script);
        } else {
          setTimeout(loadTradingView, 1000);
        }
      }
    };

    const timer = setTimeout(loadTradingView, 500);
    return () => clearTimeout(timer);
  }, [selectedSymbol, timeframe]);

  const handleSymbolChange = (symbol) => {
    setSelectedSymbol(symbol);
    setIsLoading(true);
  };

  const handleTimeframeChange = (tf) => {
    setTimeframe(tf);
    setIsLoading(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <BarChart3 className="mr-3 text-danger" size={32} />
            TradingView
          </h1>
          <p className="text-gray-400">
            Análisis técnico profesional en tiempo real
          </p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <Button variant="outline" size="sm">
            <RefreshCw size={16} className="mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" size="sm">
            <Settings size={16} className="mr-2" />
            Configurar
          </Button>
        </div>
      </motion.div>

      {/* Symbol Selection */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <h3 className="text-lg font-semibold text-text mb-4">Pares de Divisas</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {symbols.map((item) => (
              <motion.div
                key={item.symbol}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                  selectedSymbol === item.symbol
                    ? 'border-danger bg-danger bg-opacity-10'
                    : 'border-accent hover:border-gray-500'
                }`}
                onClick={() => handleSymbolChange(item.symbol)}
              >
                <div className="text-center">
                  <h4 className="font-semibold text-text text-sm">{item.name}</h4>
                  <p className="text-lg font-bold text-text mt-1">{item.price}</p>
                  <div className={`flex items-center justify-center mt-1 text-xs ${
                    item.trend === 'up' ? 'text-success' : 'text-danger'
                  }`}>
                    {item.trend === 'up' ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
                    <span className="ml-1">{item.change}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      </motion.div>

      {/* Chart Container */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="relative"
      >
        <Card className="p-0 overflow-hidden">
          {/* Chart Header */}
          <div className="p-4 border-b border-accent flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-semibold text-text">
                {symbols.find(s => s.symbol === selectedSymbol)?.name || selectedSymbol}
              </h3>
              <div className="flex items-center space-x-2">
                {timeframes.map((tf) => (
                  <button
                    key={tf}
                    onClick={() => handleTimeframeChange(tf)}
                    className={`px-3 py-1 rounded text-sm transition-colors ${
                      timeframe === tf
                        ? 'bg-danger text-white'
                        : 'text-gray-400 hover:text-white hover:bg-accent'
                    }`}
                  >
                    {tf}
                  </button>
                ))}
              </div>
            </div>
            <Button variant="ghost" size="sm">
              <Maximize2 size={16} />
            </Button>
          </div>

          {/* Chart */}
          <div className="relative" style={{ height: '600px' }}>
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-secondary">
                <div className="text-center">
                  <div className="spinner mx-auto mb-4"></div>
                  <p className="text-gray-400">Cargando gráfico...</p>
                </div>
              </div>
            )}

            <div
              ref={chartRef}
              id="tradingview-chart"
              className="w-full h-full"
            />

            {/* Fallback si TradingView no está disponible */}
            {!isLoading && !window.TradingView && (
              <div className="absolute inset-0 flex items-center justify-center bg-secondary">
                <div className="text-center max-w-md">
                  <BarChart3 className="mx-auto mb-4 text-gray-500" size={64} />
                  <h3 className="text-xl font-semibold text-text mb-2">
                    Gráfico No Disponible
                  </h3>
                  <p className="text-gray-400 mb-4">
                    El widget de TradingView no se pudo cargar. Esto puede deberse a bloqueadores de anuncios o problemas de conectividad.
                  </p>
                  <Button variant="primary" onClick={() => window.location.reload()}>
                    Reintentar
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>
      </motion.div>

      {/* Market Info */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <h3 className="text-lg font-semibold text-text mb-4">Información del Mercado</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-success bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="text-success" size={24} />
              </div>
              <h4 className="font-semibold text-text mb-1">Tendencia</h4>
              <p className="text-success">Alcista</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-warning bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <BarChart3 className="text-warning" size={24} />
              </div>
              <h4 className="font-semibold text-text mb-1">Volatilidad</h4>
              <p className="text-warning">Media</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-danger bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <TrendingDown className="text-danger" size={24} />
              </div>
              <h4 className="font-semibold text-text mb-1">Resistencia</h4>
              <p className="text-danger">1.0890</p>
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default TradingView;
