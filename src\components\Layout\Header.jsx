import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Menu, X, Bell, Settings, LogOut, User, Crown, Shield } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import Button from '../UI/Button';

const Header = ({ sidebarOpen, setSidebarOpen }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);

  const notifications = [
    { id: 1, message: 'Nueva señal de trading disponible', time: '5 min', type: 'info' },
    { id: 2, message: 'Mercado volátil detectado', time: '15 min', type: 'warning' },
    { id: 3, message: 'Sesión de trading completada', time: '1 hora', type: 'success' }
  ];

  return (
    <motion.header
      className="bg-secondary border-b border-accent px-4 py-3 flex items-center justify-between relative z-50"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Left side - Menu button and Logo */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="lg:hidden"
        >
          {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>

        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-danger rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">G</span>
          </div>
          <span className="text-xl font-bold text-gradient hidden sm:block">
            GERMAYORI
          </span>
        </div>
      </div>

      {/* Center - Search bar (hidden on mobile) */}
      <div className="hidden md:flex flex-1 max-w-md mx-8">
        <div className="relative w-full">
          <input
            type="text"
            placeholder="Buscar..."
            className="w-full bg-primary border border-accent rounded-lg px-4 py-2 text-text placeholder-gray-400 focus:outline-none focus:border-danger focus:ring-2 focus:ring-danger focus:ring-opacity-50"
          />
        </div>
      </div>

      {/* Right side - Notifications and User menu */}
      <div className="flex items-center space-x-4">
        {/* Admin Panel Button */}
        {user?.email === '<EMAIL>' && (
          <Button
            variant="primary"
            size="sm"
            onClick={() => navigate('/admin')}
            className="hidden sm:flex items-center space-x-2"
          >
            <Shield size={16} />
            <span>Panel de Administrador</span>
          </Button>
        )}

        {/* Plan indicator */}
        {user?.isPaid && (
          <div className="hidden sm:flex items-center space-x-1 bg-warning bg-opacity-20 text-warning px-3 py-1 rounded-full text-sm">
            <Crown size={16} />
            <span>Premium</span>
          </div>
        )}

        {/* Notifications */}
        <div className="relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setNotificationsOpen(!notificationsOpen)}
            className="relative"
          >
            <Bell size={20} />
            {notifications.length > 0 && (
              <span className="absolute -top-1 -right-1 bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {notifications.length}
              </span>
            )}
          </Button>

          {/* Notifications dropdown */}
          {notificationsOpen && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="absolute right-0 mt-2 w-80 bg-secondary border border-accent rounded-lg shadow-xl z-50"
            >
              <div className="p-4 border-b border-accent">
                <h3 className="font-semibold text-text">Notificaciones</h3>
              </div>
              <div className="max-h-64 overflow-y-auto">
                {notifications.map((notification) => (
                  <div key={notification.id} className="p-4 border-b border-accent hover:bg-accent hover:bg-opacity-20">
                    <p className="text-sm text-text">{notification.message}</p>
                    <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                  </div>
                ))}
              </div>
              <div className="p-4">
                <Button variant="outline" size="sm" className="w-full">
                  Ver todas
                </Button>
              </div>
            </motion.div>
          )}
        </div>

        {/* User menu */}
        <div className="relative">
          <button
            onClick={() => setDropdownOpen(!dropdownOpen)}
            className="flex items-center space-x-2 hover:bg-accent hover:bg-opacity-20 rounded-lg p-2 transition-colors"
          >
            <img
              src={user?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'}
              alt={user?.name}
              className="w-8 h-8 rounded-full object-cover"
            />
            <span className="hidden sm:block text-text font-medium">{user?.name}</span>
          </button>

          {/* User dropdown */}
          {dropdownOpen && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="absolute right-0 mt-2 w-48 bg-secondary border border-accent rounded-lg shadow-xl z-50"
            >
              <div className="p-4 border-b border-accent">
                <p className="font-semibold text-text">{user?.name}</p>
                <p className="text-sm text-gray-400">{user?.email}</p>
                <p className="text-xs text-warning mt-1">Plan: {user?.plan}</p>
              </div>

              <div className="py-2">
                {/* Admin Panel for mobile */}
                {user?.email === '<EMAIL>' && (
                  <button
                    onClick={() => {
                      navigate('/admin');
                      setDropdownOpen(false);
                    }}
                    className="w-full text-left px-4 py-2 text-danger hover:bg-danger hover:bg-opacity-20 flex items-center space-x-2"
                  >
                    <Shield size={16} />
                    <span>Panel de Administrador</span>
                  </button>
                )}

                <button className="w-full text-left px-4 py-2 text-text hover:bg-accent hover:bg-opacity-20 flex items-center space-x-2">
                  <User size={16} />
                  <span>Perfil</span>
                </button>
                <button className="w-full text-left px-4 py-2 text-text hover:bg-accent hover:bg-opacity-20 flex items-center space-x-2">
                  <Settings size={16} />
                  <span>Configuración</span>
                </button>
                <hr className="my-2 border-accent" />
                <button
                  onClick={logout}
                  className="w-full text-left px-4 py-2 text-danger hover:bg-danger hover:bg-opacity-20 flex items-center space-x-2"
                >
                  <LogOut size={16} />
                  <span>Cerrar Sesión</span>
                </button>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Click outside to close dropdowns */}
      {(dropdownOpen || notificationsOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setDropdownOpen(false);
            setNotificationsOpen(false);
          }}
        />
      )}
    </motion.header>
  );
};

export default Header;
