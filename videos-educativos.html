<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Videos Educativos</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .course-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        .price-tag {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            font-weight: bold;
        }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="videos-educativos.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>

                <a href="mentoria.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-user-graduate mr-3 text-violet-400"></i>
                    <span>Mentoría Directa</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="tradingview.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-area mr-3 text-red-400"></i>
                    <span>TradingView</span>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- Videos Content -->
        <div class="flex-1 main-content m-4 rounded-lg overflow-y-auto">

            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-t-lg">
                <h2 class="text-3xl font-bold mb-2">🎓 Videos Educativos GERMAYORI</h2>
                <p class="text-blue-100">Aprende la estrategia institucional Fair Value Gap paso a paso</p>
            </div>

            <!-- Courses Grid -->
            <div class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                <!-- Curso Básico -->
                <div class="course-card rounded-lg p-6">
                    <div class="text-center mb-4">
                        <div class="text-5xl mb-3">📚</div>
                        <h3 class="text-2xl font-bold text-white mb-2">Curso Básico</h3>
                        <div class="price-tag px-4 py-2 rounded-full text-lg font-bold mb-4">
                            $45/mes
                        </div>
                    </div>

                    <div class="space-y-3 mb-6">
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📈 SEÑALES DE TRADING INCLUIDAS</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📊 Trading en Vivo</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Fundamentos de Price Action</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Introducción a Fair Value Gap</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Conceptos básicos de liquidez</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Gestión básica de riesgo</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>10 videos explicativos</span>
                        </div>
                    </div>

                    <button onclick="subscribeToCourse('basico')" class="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-lg font-bold hover:from-green-600 hover:to-blue-700 transition-all">
                        <i class="fas fa-play mr-2"></i>Suscribirse
                    </button>
                </div>

                <!-- Curso Intermedio -->
                <div class="course-card rounded-lg p-6">
                    <div class="text-center mb-4">
                        <div class="text-5xl mb-3">🎯</div>
                        <h3 class="text-2xl font-bold text-white mb-2">Curso Intermedio</h3>
                        <div class="price-tag px-4 py-2 rounded-full text-lg font-bold mb-4">
                            $70/mes
                        </div>
                    </div>

                    <div class="space-y-3 mb-6">
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📈 SEÑALES DE TRADING INCLUIDAS</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📊 Trading en Vivo</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📚 Videos Básicos + Intermedios</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Order Blocks avanzados</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Break of Structure (BOS)</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Identificación de liquidez</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Entradas institucionales</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>25 videos + casos prácticos</span>
                        </div>
                    </div>

                    <button onclick="subscribeToCourse('intermedio')" class="w-full bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-lg font-bold hover:from-orange-600 hover:to-red-700 transition-all">
                        <i class="fas fa-play mr-2"></i>Suscribirse
                    </button>
                </div>

                <!-- Curso Avanzado -->
                <div class="course-card rounded-lg p-6">
                    <div class="text-center mb-4">
                        <div class="text-5xl mb-3">🏆</div>
                        <h3 class="text-2xl font-bold text-white mb-2">Curso Avanzado</h3>
                        <div class="price-tag px-4 py-2 rounded-full text-lg font-bold mb-4">
                            $95/mes
                        </div>
                    </div>

                    <div class="space-y-3 mb-6">
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📈 SEÑALES DE TRADING INCLUIDAS</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📊 Trading en Vivo</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>📚 TODOS los Videos (Básico + Intermedio + Avanzado)</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Estrategia GERMAYORI completa</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Multi-timeframe analysis</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Psicología del trading</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>Backtesting avanzado</span>
                        </div>
                        <div class="flex items-center text-white">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span>45+ videos + trading en vivo</span>
                        </div>
                    </div>

                    <button onclick="subscribeToCourse('avanzado')" class="w-full bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 rounded-lg font-bold hover:from-purple-600 hover:to-pink-700 transition-all">
                        <i class="fas fa-play mr-2"></i>Suscribirse
                    </button>
                </div>

            </div>

        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }
            }
        }

        // Función para suscribirse a curso
        function subscribeToCourse(courseType) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');

            // Si es ADMIN, acceso completo sin pagar
            if (user.type === 'admin') {
                alert(`🔑 ACCESO ADMIN COMPLETO\n\n✅ Tienes acceso total como administrador.\n\n📚 Curso: ${courseType.charAt(0).toUpperCase() + courseType.slice(1)}\n🎯 Sin restricciones de pago.`);

                // Registrar acceso admin
                registerCourseStart(courseType);
                setTimeout(() => {
                    completeCourse(courseType);
                }, 1000);
                return;
            }

            const prices = {
                'basico': 45,
                'intermedio': 70,
                'avanzado': 95
            };

            const courseNames = {
                'basico': 'Básico',
                'intermedio': 'Intermedio',
                'avanzado': 'Avanzado'
            };

            const confirmed = confirm(`¿Deseas suscribirte al Curso ${courseNames[courseType]} por $${prices[courseType]}/mes?`);

            if (confirmed) {
                // Registrar inicio del curso
                registerCourseStart(courseType);

                alert(`🎉 ¡Suscripción exitosa!\n\nCurso: ${courseNames[courseType]}\nPrecio: $${prices[courseType]}/mes\n\nYa puedes acceder a todos los videos del curso.\n\n📊 Tu progreso será registrado automáticamente.`);

                // Simular completar curso después de 3 segundos (en producción sería cuando termine los videos)
                setTimeout(() => {
                    completeCourse(courseType);
                }, 3000);
            }
        }

        // Registrar inicio de curso
        function registerCourseStart(courseType) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (!user.courses) user.courses = {};

            user.courses[courseType] = {
                started: new Date().toISOString(),
                completed: false,
                progress: 0
            };

            localStorage.setItem('germayori_user', JSON.stringify(user));

            // Enviar a servidor (simulado)
            console.log(`📚 Usuario ${user.email} inició curso ${courseType}`);
        }

        // Completar curso
        function completeCourse(courseType) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (!user.courses) user.courses = {};

            user.courses[courseType] = {
                ...user.courses[courseType],
                completed: true,
                completedDate: new Date().toISOString(),
                progress: 100
            };

            localStorage.setItem('germayori_user', JSON.stringify(user));

            alert(`🎉 ¡Felicidades!\n\nHas completado el Curso ${courseType.charAt(0).toUpperCase() + courseType.slice(1)}.\n\n✅ Tu progreso ha sido registrado.\n\n${checkAllCoursesCompleted() ? '🎯 ¡Ya puedes acceder a la Mentoría VIP!' : '📚 Continúa con los demás cursos para acceder a la mentoría.'}`);

            // Enviar notificación al servidor
            notifyServerCourseCompleted(courseType);
        }

        // Verificar si todos los cursos están completos
        function checkAllCoursesCompleted() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const courses = user.courses || {};

            return courses.basico?.completed &&
                   courses.intermedio?.completed &&
                   courses.avanzado?.completed;
        }

        // Notificar al servidor que completó curso
        function notifyServerCourseCompleted(courseType) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');

            // Simular envío al servidor
            console.log(`🎓 NOTIFICACIÓN AL SERVIDOR:`);
            console.log(`Usuario: ${user.email}`);
            console.log(`Curso completado: ${courseType}`);
            console.log(`Fecha: ${new Date().toLocaleString()}`);
            console.log(`Todos los cursos completos: ${checkAllCoursesCompleted()}`);

            // En producción esto sería una llamada a AWS/API
            // fetch('/api/course-completed', { ... })
        }

        // Inicializar
        window.onload = function() {
            checkAuth();
            loadUserInfo();
        };
    </script>
</body>
</html>
