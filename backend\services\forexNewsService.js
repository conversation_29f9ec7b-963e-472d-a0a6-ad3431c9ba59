const axios = require('axios');
const awsServices = require('./awsServices');
require('dotenv').config();

class ForexNewsService {
    constructor() {
        this.forexFactoryURL = process.env.FOREX_FACTORY_URL || 'https://www.forexfactory.com/news';
        this.updateInterval = 5 * 60 * 1000; // 5 minutos
        this.isRunning = false;
    }

    // Obtener noticias en tiempo real
    async getRealTimeNews() {
        try {
            // Simulamos noticias reales hasta tener acceso directo a la API
            const news = [
                {
                    title: 'Fed mantiene tasas de interés sin cambios',
                    content: 'La Reserva Federal de Estados Unidos decidió mantener las tasas de interés en el rango actual de 5.25%-5.50%, según el comunicado oficial.',
                    category: 'Central Bank',
                    impact: 'high',
                    currency: 'USD',
                    source: 'Federal Reserve',
                    time: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 min ago
                    url: 'https://www.forexfactory.com/news'
                },
                {
                    title: 'BCE considera recorte de tasas en próxima reunión',
                    content: 'Fuentes cercanas al Banco Central Europeo sugieren que podría haber un recorte de 0.25% en la próxima reunión de política monetaria.',
                    category: 'Central Bank',
                    impact: 'high',
                    currency: 'EUR',
                    source: 'European Central Bank',
                    time: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 min ago
                    url: 'https://www.forexfactory.com/news'
                },
                {
                    title: 'Datos de empleo de Reino Unido superan expectativas',
                    content: 'La tasa de desempleo en Reino Unido bajó al 3.8%, mejor que el 4.0% esperado, fortaleciendo la libra esterlina.',
                    category: 'Employment',
                    impact: 'medium',
                    currency: 'GBP',
                    source: 'ONS',
                    time: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1 hour ago
                    url: 'https://www.forexfactory.com/news'
                },
                {
                    title: 'Banco de Japón mantiene política ultra-acomodaticia',
                    content: 'El BoJ reafirma su compromiso con la política monetaria ultra-acomodaticia, manteniendo las tasas en territorio negativo.',
                    category: 'Central Bank',
                    impact: 'medium',
                    currency: 'JPY',
                    source: 'Bank of Japan',
                    time: new Date(Date.now() - 90 * 60 * 1000).toISOString(), // 1.5 hours ago
                    url: 'https://www.forexfactory.com/news'
                },
                {
                    title: 'Inflación de Australia se mantiene estable',
                    content: 'Los datos de inflación trimestral de Australia muestran estabilidad en el 3.2%, dentro del rango objetivo del RBA.',
                    category: 'Inflation',
                    impact: 'medium',
                    currency: 'AUD',
                    source: 'Australian Bureau of Statistics',
                    time: new Date(Date.now() - 120 * 60 * 1000).toISOString(), // 2 hours ago
                    url: 'https://www.forexfactory.com/news'
                }
            ];

            return {
                success: true,
                data: news,
                lastUpdate: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error obteniendo noticias:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    // Obtener calendario económico
    async getEconomicCalendar() {
        try {
            const today = new Date();
            const events = [
                {
                    id: 'event_1',
                    time: new Date(today.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
                    currency: 'USD',
                    event: 'Non-Farm Payrolls',
                    impact: 'high',
                    forecast: '180K',
                    previous: '175K',
                    actual: null
                },
                {
                    id: 'event_2',
                    time: new Date(today.getTime() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours from now
                    currency: 'EUR',
                    event: 'ECB Interest Rate Decision',
                    impact: 'high',
                    forecast: '4.50%',
                    previous: '4.50%',
                    actual: null
                },
                {
                    id: 'event_3',
                    time: new Date(today.getTime() + 6 * 60 * 60 * 1000).toISOString(), // 6 hours from now
                    currency: 'GBP',
                    event: 'GDP Growth Rate',
                    impact: 'medium',
                    forecast: '0.2%',
                    previous: '0.1%',
                    actual: null
                },
                {
                    id: 'event_4',
                    time: new Date(today.getTime() + 8 * 60 * 60 * 1000).toISOString(), // 8 hours from now
                    currency: 'JPY',
                    event: 'Core CPI',
                    impact: 'medium',
                    forecast: '2.8%',
                    previous: '2.9%',
                    actual: null
                },
                {
                    id: 'event_5',
                    time: new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
                    currency: 'AUD',
                    event: 'RBA Interest Rate Decision',
                    impact: 'high',
                    forecast: '4.35%',
                    previous: '4.35%',
                    actual: null
                }
            ];

            return {
                success: true,
                data: events,
                lastUpdate: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error obteniendo calendario económico:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    // Obtener alertas de mercado
    async getMarketAlerts() {
        try {
            const alerts = [
                {
                    id: 'alert_1',
                    type: 'price',
                    symbol: 'EURUSD',
                    message: 'EUR/USD rompe resistencia clave en 1.0850',
                    level: 'high',
                    price: 1.0852,
                    time: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
                    status: 'active'
                },
                {
                    id: 'alert_2',
                    type: 'news',
                    symbol: 'GBPUSD',
                    message: 'Datos de empleo UK mejor de lo esperado - GBP fortalecido',
                    level: 'medium',
                    price: 1.2745,
                    time: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                    status: 'active'
                },
                {
                    id: 'alert_3',
                    type: 'technical',
                    symbol: 'USDJPY',
                    message: 'USD/JPY en soporte crítico - posible rebote',
                    level: 'medium',
                    price: 149.85,
                    time: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
                    status: 'active'
                }
            ];

            return {
                success: true,
                data: alerts,
                lastUpdate: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error obteniendo alertas de mercado:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    // Iniciar monitoreo automático de noticias
    startNewsMonitoring() {
        if (this.isRunning) {
            console.log('⚠️ El monitoreo de noticias ya está en ejecución');
            return;
        }

        this.isRunning = true;
        console.log('🔄 Iniciando monitoreo automático de noticias...');

        this.monitoringInterval = setInterval(async () => {
            try {
                const news = await this.getRealTimeNews();
                if (news.success && news.data.length > 0) {
                    // Guardar noticias en AWS DynamoDB
                    for (const newsItem of news.data) {
                        await awsServices.createNews(newsItem);
                    }
                    console.log(`📰 ${news.data.length} noticias actualizadas`);
                }

                const alerts = await this.getMarketAlerts();
                if (alerts.success && alerts.data.length > 0) {
                    // Guardar alertas en AWS DynamoDB
                    for (const alert of alerts.data) {
                        await awsServices.createAlert({
                            user_id: 'system',
                            type: alert.type,
                            symbol: alert.symbol,
                            message: alert.message,
                            level: alert.level
                        });
                    }
                    console.log(`🚨 ${alerts.data.length} alertas actualizadas`);
                }
            } catch (error) {
                console.error('❌ Error en monitoreo de noticias:', error);
            }
        }, this.updateInterval);
    }

    // Detener monitoreo automático
    stopNewsMonitoring() {
        if (!this.isRunning) {
            console.log('⚠️ El monitoreo de noticias no está en ejecución');
            return;
        }

        clearInterval(this.monitoringInterval);
        this.isRunning = false;
        console.log('⏹️ Monitoreo de noticias detenido');
    }

    // Obtener análisis de sentimiento del mercado
    async getMarketSentiment() {
        try {
            const sentiment = {
                overall: 'bullish', // bullish, bearish, neutral
                confidence: 72,
                currencies: {
                    USD: { sentiment: 'bullish', strength: 85 },
                    EUR: { sentiment: 'neutral', strength: 45 },
                    GBP: { sentiment: 'bullish', strength: 68 },
                    JPY: { sentiment: 'bearish', strength: 35 },
                    AUD: { sentiment: 'neutral', strength: 52 },
                    CAD: { sentiment: 'bullish', strength: 61 }
                },
                lastUpdate: new Date().toISOString()
            };

            return {
                success: true,
                data: sentiment
            };
        } catch (error) {
            console.error('Error obteniendo sentimiento del mercado:', error);
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
}

module.exports = new ForexNewsService();
