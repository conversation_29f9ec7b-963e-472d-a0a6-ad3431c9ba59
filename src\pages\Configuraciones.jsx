import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Settings, User, Bell, Shield, Palette } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';

const Configuraciones = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');

  const tabs = [
    { id: 'profile', name: 'Perfil', icon: User },
    { id: 'notifications', name: 'Notificaciones', icon: Bell },
    { id: 'security', name: 'Seguridad', icon: Shield },
    { id: 'appearance', name: 'Apariencia', icon: Palette },
  ];

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
          <Settings className="mr-3 text-danger" size={32} />
          Configuraciones
        </h1>
        <p className="text-gray-400">
          Personaliza tu experiencia en GERMAYORI
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <Card className="lg:col-span-1">
          <nav className="space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-danger text-white'
                    : 'text-gray-400 hover:text-white hover:bg-accent hover:bg-opacity-20'
                }`}
              >
                <tab.icon size={20} />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </Card>

        {/* Content */}
        <div className="lg:col-span-3">
          {activeTab === 'profile' && (
            <Card>
              <h3 className="text-xl font-semibold text-text mb-6">Información del Perfil</h3>
              <div className="space-y-6">
                <div className="flex items-center space-x-6">
                  <img
                    src={user?.avatar}
                    alt={user?.name}
                    className="w-20 h-20 rounded-full object-cover"
                  />
                  <Button variant="outline">Cambiar Foto</Button>
                </div>
                <Input label="Nombre" value={user?.name} />
                <Input label="Email" value={user?.email} />
                <Input label="Plan Actual" value={user?.plan} disabled />
                <Button variant="primary">Guardar Cambios</Button>
              </div>
            </Card>
          )}

          {activeTab === 'notifications' && (
            <Card>
              <h3 className="text-xl font-semibold text-text mb-6">Notificaciones</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-text">Alertas de Trading</h4>
                    <p className="text-sm text-gray-400">Recibir notificaciones sobre oportunidades</p>
                  </div>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-text">Actualizaciones del Sistema</h4>
                    <p className="text-sm text-gray-400">Notificaciones sobre nuevas funciones</p>
                  </div>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
              </div>
            </Card>
          )}

          {activeTab === 'security' && (
            <Card>
              <h3 className="text-xl font-semibold text-text mb-6">Seguridad</h3>
              <div className="space-y-6">
                <Input label="Contraseña Actual" type="password" />
                <Input label="Nueva Contraseña" type="password" />
                <Input label="Confirmar Contraseña" type="password" />
                <Button variant="primary">Cambiar Contraseña</Button>
              </div>
            </Card>
          )}

          {activeTab === 'appearance' && (
            <Card>
              <h3 className="text-xl font-semibold text-text mb-6">Apariencia</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-text mb-2">Tema</h4>
                  <select className="bg-secondary border border-accent rounded-lg px-4 py-2 text-text">
                    <option>Oscuro (Actual)</option>
                    <option>Claro</option>
                    <option>Automático</option>
                  </select>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Configuraciones;
