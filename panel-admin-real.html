<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Panel Administrativo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #FFD700;
            margin-bottom: 10px;
        }

        .sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
        }

        .section h2 {
            color: #FFD700;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #FFD700;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #fff;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1rem;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .data-list {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .data-item {
            background: rgba(255, 255, 255, 0.1);
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #FFD700;
        }

        .data-item h4 {
            color: #FFD700;
            margin-bottom: 5px;
        }

        .data-item p {
            margin: 3px 0;
            font-size: 0.9rem;
        }

        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-top: 5px;
        }

        .status-success {
            background: #28a745;
            color: #fff;
        }

        .file-input {
            border: 2px dashed #FFD700;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .file-input input[type="file"] {
            display: none;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .nav-link:hover {
            background: rgba(255, 215, 0, 0.3);
            color: #FFD700;
        }

        @media (max-width: 768px) {
            .sections {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GERMAYORI PANEL ADMINISTRATIVO</h1>
            <p>Gestión completa de usuarios, señales y videos educativos</p>
        </div>

        <!-- Navegación -->
        <div class="nav-links">
            <a href="dashboard-simple.html" class="nav-link">🏠 Dashboard</a>
            <a href="canal-videos-educativos.html" class="nav-link">📹 Canal Videos</a>
            <a href="videos-educativos.html" class="nav-link">📚 Videos Antiguos</a>
            <a href="senales.html" class="nav-link">📈 Señales</a>
            <a href="chat.html" class="nav-link">💬 Chat</a>
            <a href="trading-vivo.html" class="nav-link">📊 Trading en Vivo</a>
        </div>

        <div class="sections">
            <!-- SECCIÓN: SUBIR SEÑALES -->
            <div class="section">
                <h2>📈 SUBIR SEÑAL DE TRADING</h2>
                <form id="signalForm">
                    <div class="form-group">
                        <label>Par de Divisas:</label>
                        <select id="signalPair" required>
                            <option value="">Seleccionar par</option>
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="USDCAD">USD/CAD</option>
                            <option value="USDCHF">USD/CHF</option>
                            <option value="NZDUSD">NZD/USD</option>
                            <option value="XAUUSD">XAU/USD (Oro)</option>
                            <option value="NAS100">NAS100</option>
                            <option value="US30">US30</option>
                            <option value="SPX500">SPX500</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tipo:</label>
                        <select id="signalType" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="BUY">COMPRA (BUY)</option>
                            <option value="SELL">VENTA (SELL)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Precio de Entrada:</label>
                        <input type="number" id="signalEntry" step="0.00001" required placeholder="1.08500">
                    </div>
                    <div class="form-group">
                        <label>Stop Loss:</label>
                        <input type="number" id="signalSL" step="0.00001" required placeholder="1.08000">
                    </div>
                    <div class="form-group">
                        <label>Take Profit:</label>
                        <input type="number" id="signalTP" step="0.00001" required placeholder="1.09000">
                    </div>
                    <div class="form-group">
                        <label>Análisis:</label>
                        <textarea id="signalAnalysis" placeholder="Descripción del análisis técnico..."></textarea>
                    </div>
                    <button type="submit" class="btn">📈 GUARDAR SEÑAL</button>
                </form>
            </div>

            <!-- SECCIÓN: AGREGAR USUARIO -->
            <div class="section">
                <h2>👤 AGREGAR USUARIO</h2>
                <form id="userForm">
                    <div class="form-group">
                        <label>Nombre Completo:</label>
                        <input type="text" id="userName" required placeholder="Juan Pérez">
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" id="userEmail" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Contraseña:</label>
                        <input type="password" id="userPassword" required placeholder="Mínimo 6 caracteres">
                    </div>
                    <div class="form-group">
                        <label>Tipo de Usuario:</label>
                        <select id="userType">
                            <option value="user">Usuario Normal</option>
                            <option value="premium">Usuario Premium</option>
                            <option value="admin">Administrador</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">👤 AGREGAR USUARIO</button>
                </form>
            </div>

            <!-- SECCIÓN: SUBIR VIDEO -->
            <div class="section">
                <h2>📹 SUBIR VIDEO EDUCATIVO</h2>
                <form id="videoForm">
                    <div class="form-group">
                        <label>Título del Video:</label>
                        <input type="text" id="videoTitle" required placeholder="Estrategia FVG Institucional">
                    </div>
                    <div class="form-group">
                        <label>Descripción:</label>
                        <textarea id="videoDescription" placeholder="Descripción del contenido del video..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Categoría:</label>
                        <select id="videoCategory">
                            <option value="basico">Básico ($45/mes)</option>
                            <option value="intermedio">Intermedio ($70/mes)</option>
                            <option value="avanzado">Avanzado ($95/mes)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>URL del Video (YouTube/Vimeo):</label>
                        <input type="url" id="videoUrl" placeholder="https://youtube.com/watch?v=...">
                    </div>
                    <div class="form-group">
                        <label>O subir archivo de video:</label>
                        <div class="file-input" onclick="document.getElementById('videoFile').click()">
                            <input type="file" id="videoFile" accept="video/*">
                            <p id="fileText">📹 Clic para seleccionar video</p>
                            <small>Formatos: MP4, AVI, MOV, WMV, WEBM (Max: 500MB)</small>
                        </div>
                        <div id="uploadProgress" style="display: none; margin-top: 10px;">
                            <div style="background: rgba(255,255,255,0.2); border-radius: 10px; height: 20px; overflow: hidden;">
                                <div id="progressBar" style="background: linear-gradient(45deg, #FFD700, #FFA500); height: 100%; width: 0%; transition: width 0.3s ease; display: flex; align-items: center; justify-content: center; color: #000; font-weight: bold; font-size: 12px;"></div>
                            </div>
                            <p id="progressText" style="text-align: center; margin-top: 5px; color: #FFD700;">Procesando...</p>
                        </div>
                    </div>
                    <button type="submit" class="btn" id="submitVideoBtn">📹 SUBIR VIDEO</button>
                    <button type="button" class="btn" onclick="window.open('canal-videos-educativos.html', '_blank')" style="background: linear-gradient(45deg, #4CAF50, #45a049); margin-top: 10px;">👀 VER CANAL DE VIDEOS</button>
                </form>
            </div>

            <!-- SECCIÓN: DATOS GUARDADOS -->
            <div class="section">
                <h2>📊 DATOS GUARDADOS</h2>

                <h3 style="color: #FFD700; margin: 20px 0 10px 0;">👥 Usuarios:</h3>
                <div class="data-list" id="usersList">
                    <!-- Los usuarios se mostrarán aquí -->
                </div>

                <h3 style="color: #FFD700; margin: 20px 0 10px 0;">📈 Señales:</h3>
                <div class="data-list" id="signalsList">
                    <!-- Las señales se mostrarán aquí -->
                </div>

                <h3 style="color: #FFD700; margin: 20px 0 10px 0;">📹 Videos:</h3>
                <div class="data-list" id="videosList">
                    <!-- Los videos se mostrarán aquí -->
                </div>

                <button class="btn" onclick="clearAllData()">🗑️ LIMPIAR TODOS LOS DATOS</button>
                <button class="btn" onclick="addTestVideo()" style="background: linear-gradient(45deg, #ff9800, #f57c00); margin-top: 10px;">🧪 AGREGAR VIDEO DE PRUEBA</button>
            </div>
        </div>
    </div>

    <script>
        // Almacenamiento local para datos
        let users = JSON.parse(localStorage.getItem('germayori_users') || '[]');
        let signals = JSON.parse(localStorage.getItem('germayori_signals') || '[]');
        let videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');

        // IndexedDB para videos grandes
        let db;
        const dbName = 'GermayoriVideos';
        const dbVersion = 1;

        // Inicializar IndexedDB
        function initDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(dbName, dbVersion);

                request.onerror = () => reject(request.error);
                request.onsuccess = () => {
                    db = request.result;
                    resolve(db);
                };

                request.onupgradeneeded = (event) => {
                    db = event.target.result;
                    if (!db.objectStoreNames.contains('videos')) {
                        const store = db.createObjectStore('videos', { keyPath: 'id' });
                        store.createIndex('category', 'category', { unique: false });
                    }
                };
            });
        }

        // Guardar video en IndexedDB
        function saveVideoToDB(videoData) {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction(['videos'], 'readwrite');
                const store = transaction.objectStore('videos');
                const request = store.put(videoData);

                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }

        // Obtener videos de IndexedDB
        function getVideosFromDB() {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction(['videos'], 'readonly');
                const store = transaction.objectStore('videos');
                const request = store.getAll();

                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }

        // Eliminar video de IndexedDB
        function deleteVideoFromDB(videoId) {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction(['videos'], 'readwrite');
                const store = transaction.objectStore('videos');
                const request = store.delete(videoId);

                request.onsuccess = () => resolve();
                request.onerror = () => reject(request.error);
            });
        }

        // Función para agregar señal
        document.getElementById('signalForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const signal = {
                id: Date.now(),
                pair: document.getElementById('signalPair').value,
                type: document.getElementById('signalType').value,
                entry: document.getElementById('signalEntry').value,
                sl: document.getElementById('signalSL').value,
                tp: document.getElementById('signalTP').value,
                analysis: document.getElementById('signalAnalysis').value,
                date: new Date().toLocaleString()
            };

            signals.push(signal);
            localStorage.setItem('germayori_signals', JSON.stringify(signals));

            alert('✅ Señal guardada exitosamente');
            this.reset();
            displayData();
        });

        // Función para agregar usuario
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const user = {
                id: Date.now(),
                name: document.getElementById('userName').value,
                email: document.getElementById('userEmail').value,
                password: document.getElementById('userPassword').value,
                type: document.getElementById('userType').value,
                date: new Date().toLocaleString()
            };

            users.push(user);
            localStorage.setItem('germayori_users', JSON.stringify(users));

            alert('✅ Usuario agregado exitosamente');
            this.reset();
            displayData();
        });

        // Función para agregar video
        document.getElementById('videoForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const videoTitle = document.getElementById('videoTitle').value;
            const videoDescription = document.getElementById('videoDescription').value;
            const videoCategory = document.getElementById('videoCategory').value;
            const videoUrl = document.getElementById('videoUrl').value;
            const videoFile = document.getElementById('videoFile').files[0];

            if (!videoTitle) {
                alert('❌ El título del video es obligatorio');
                return;
            }

            if (!videoUrl && !videoFile) {
                alert('❌ Debes proporcionar una URL o subir un archivo de video');
                return;
            }

            // Si es archivo MP4, procesarlo con FileReader
            if (videoFile) {
                processVideoFile(videoFile, videoTitle, videoDescription, videoCategory);
            } else {
                // Si es URL, guardar directamente
                saveVideoData(videoTitle, videoDescription, videoCategory, videoUrl, null);
            }
        });

        // Función para procesar archivo de video
        function processVideoFile(file, title, description, category) {
            // Validar tipo de archivo
            const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
            if (!allowedTypes.includes(file.type)) {
                alert('❌ Tipo de archivo no soportado. Use: MP4, AVI, MOV, WMV, WEBM');
                return;
            }

            // Validar tamaño (máximo 500MB para videos 1080p)
            const maxSize = 500 * 1024 * 1024; // 500MB
            if (file.size > maxSize) {
                alert(`❌ El archivo es muy grande. Máximo 500MB permitido.\nTamaño actual: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
                return;
            }

            console.log(`📹 Procesando video: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            console.log(`📊 Tipo: ${file.type}`);

            // Mostrar barra de progreso
            const progressDiv = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const submitBtn = document.getElementById('submitVideoBtn');

            progressDiv.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.textContent = '📤 PROCESANDO...';

            // Simular progreso
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                progressBar.style.width = progress + '%';
                progressBar.textContent = Math.round(progress) + '%';
                progressText.textContent = `Procesando video... ${Math.round(progress)}%`;
            }, 200);

            // Crear FileReader para convertir a base64
            const reader = new FileReader();

            reader.onloadstart = function() {
                console.log('📤 Iniciando lectura del archivo...');
            };

            reader.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percentLoaded = Math.round((e.loaded / e.total) * 100);
                    console.log(`📊 Progreso de lectura: ${percentLoaded}%`);
                }
            };

            reader.onload = function(e) {
                console.log('✅ Archivo leído exitosamente');
                clearInterval(progressInterval);

                // Completar progreso
                progressBar.style.width = '100%';
                progressBar.textContent = '100%';
                progressText.textContent = '✅ Video procesado exitosamente';

                const base64Data = e.target.result;
                console.log(`📊 Tamaño base64: ${(base64Data.length / 1024 / 1024).toFixed(2)} MB`);

                // Guardar video con datos base64
                setTimeout(() => {
                    try {
                        saveVideoData(title, description, category, null, {
                            filename: file.name,
                            size: file.size,
                            type: file.type,
                            data: base64Data
                        });

                        console.log('✅ Video guardado exitosamente');

                        // Resetear UI
                        progressDiv.style.display = 'none';
                        submitBtn.disabled = false;
                        submitBtn.textContent = '📹 SUBIR VIDEO';
                        progressBar.style.width = '0%';
                    } catch (error) {
                        console.error('❌ Error al guardar video:', error);
                        alert('❌ Error al guardar el video: ' + error.message);

                        // Resetear UI en caso de error
                        progressDiv.style.display = 'none';
                        submitBtn.disabled = false;
                        submitBtn.textContent = '📹 SUBIR VIDEO';
                        progressBar.style.width = '0%';
                    }
                }, 1000);
            };

            reader.onerror = function(e) {
                console.error('❌ Error al leer archivo:', e);
                clearInterval(progressInterval);
                progressDiv.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.textContent = '📹 SUBIR VIDEO';
                alert('❌ Error al leer el archivo de video. El archivo puede estar corrupto o ser muy grande.');
            };

            reader.onabort = function() {
                console.log('⚠️ Lectura de archivo cancelada');
                clearInterval(progressInterval);
                progressDiv.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.textContent = '📹 SUBIR VIDEO';
            };

            try {
                reader.readAsDataURL(file);
            } catch (error) {
                console.error('❌ Error al iniciar lectura:', error);
                clearInterval(progressInterval);
                progressDiv.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.textContent = '📹 SUBIR VIDEO';
                alert('❌ Error al procesar el archivo: ' + error.message);
            }
        }

        // Función para guardar datos del video
        async function saveVideoData(title, description, category, url, fileData) {
            const video = {
                id: 'video_' + Date.now(),
                title: title,
                description: description,
                category: category,
                url: url,
                filename: fileData ? fileData.filename : '',
                size: fileData ? fileData.size : 0,
                type: fileData ? fileData.type : '',
                data: fileData ? fileData.data : null, // Base64 data
                duration: '15:30', // Duración por defecto
                date: new Date().toLocaleDateString()
            };

            try {
                if (fileData && fileData.data) {
                    // Si es archivo grande, usar IndexedDB
                    console.log('💾 Guardando video en IndexedDB...');
                    await saveVideoToDB(video);

                    // Guardar solo metadatos en localStorage
                    const videoMeta = { ...video };
                    delete videoMeta.data; // Remover datos base64
                    videoMeta.storedInDB = true; // Marcar que está en IndexedDB

                    videos.push(videoMeta);
                    localStorage.setItem('germayori_videos', JSON.stringify(videos));
                } else {
                    // Si es URL, guardar en localStorage normal
                    videos.push(video);
                    localStorage.setItem('germayori_videos', JSON.stringify(videos));
                }

                // Debug: Verificar que se guardó
                console.log('✅ Video guardado exitosamente:', video.id);
                console.log('📊 Total videos ahora:', videos.length);

                const sizeText = fileData ? `📁 Tamaño: ${(fileData.size / 1024 / 1024).toFixed(2)} MB` : '';
                const storageText = fileData && fileData.data ? '\n💾 Almacenado en IndexedDB' : '';

                alert(`✅ Video "${title}" agregado exitosamente\n📂 Categoría: ${category}\n${url ? '🔗 URL: ' + url : '📁 Archivo: ' + video.filename}\n${sizeText}${storageText}\n\n📊 Total videos: ${videos.length}\n🆔 ID: ${video.id}`);

                document.getElementById('videoForm').reset();
                await displayData();

            } catch (error) {
                console.error('❌ Error al guardar video:', error);
                throw error; // Re-lanzar el error para que lo maneje el código que llama
            }
        }

        // Función para mostrar datos
        async function displayData() {
            // Mostrar usuarios
            const usersList = document.getElementById('usersList');
            usersList.innerHTML = users.map(user => `
                <div class="data-item">
                    <h4>${user.name}</h4>
                    <p>📧 ${user.email}</p>
                    <p>🏷️ ${user.type}</p>
                    <p>📅 ${user.date}</p>
                </div>
            `).join('') || '<p>No hay usuarios registrados</p>';

            // Mostrar señales
            const signalsList = document.getElementById('signalsList');
            signalsList.innerHTML = signals.map(signal => `
                <div class="data-item">
                    <h4>${signal.pair} - ${signal.type}</h4>
                    <p>📈 Entrada: ${signal.entry}</p>
                    <p>🛑 SL: ${signal.sl} | 🎯 TP: ${signal.tp}</p>
                    <p>📅 ${signal.date}</p>
                    ${signal.analysis ? `<p>📝 ${signal.analysis}</p>` : ''}
                </div>
            `).join('') || '<p>No hay señales registradas</p>';

            // Mostrar videos (combinar localStorage + IndexedDB)
            const videosList = document.getElementById('videosList');
            try {
                let allVideos = [...videos]; // Videos de localStorage

                if (db) {
                    const dbVideos = await getVideosFromDB();
                    // Agregar videos de IndexedDB que no estén ya en localStorage
                    dbVideos.forEach(dbVideo => {
                        if (!allVideos.find(v => v.id === dbVideo.id)) {
                            allVideos.push(dbVideo);
                        }
                    });
                }

                videosList.innerHTML = allVideos.map(video => `
                    <div class="data-item">
                        <h4>${video.title}</h4>
                        <p>🏷️ ${video.category}</p>
                        ${video.url ? `<p>🔗 URL: ${video.url}</p>` : ''}
                        ${video.filename ? `<p>📁 Archivo: ${video.filename}</p>` : ''}
                        ${video.size ? `<p>💾 Tamaño: ${(video.size / 1024 / 1024).toFixed(2)} MB</p>` : ''}
                        ${video.type ? `<p>🎬 Tipo: ${video.type}</p>` : ''}
                        <p>📅 ${video.date}</p>
                        ${video.description ? `<p>📝 ${video.description}</p>` : ''}
                        <p>🆔 ID: ${video.id}</p>
                        ${video.storedInDB ? '<p>💾 Almacenado en IndexedDB</p>' : ''}
                        ${video.data ? '<p>✅ Archivo cargado correctamente</p>' : ''}
                    </div>
                `).join('') || '<p>No hay videos registrados</p>';
            } catch (error) {
                console.error('Error al mostrar videos:', error);
                videosList.innerHTML = '<p>Error al cargar videos</p>';
            }
        }

        // Función para limpiar datos
        function clearAllData() {
            if (confirm('¿Estás seguro de que quieres eliminar TODOS los datos?')) {
                localStorage.removeItem('germayori_users');
                localStorage.removeItem('germayori_signals');
                localStorage.removeItem('germayori_videos');
                users = [];
                signals = [];
                videos = [];
                displayData();
                alert('✅ Todos los datos han sido eliminados');
            }
        }

        // Función para agregar video de prueba
        function addTestVideo() {
            const testVideo = {
                id: 'test_' + Date.now(),
                title: 'Video de Prueba - Estrategia FVG',
                description: 'Video de demostración para probar el reproductor',
                category: 'basico',
                url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                filename: '',
                size: 0,
                type: '',
                duration: '3:32',
                date: new Date().toLocaleDateString()
            };

            videos.push(testVideo);
            localStorage.setItem('germayori_videos', JSON.stringify(videos));

            const totalVideos = JSON.parse(localStorage.getItem('germayori_videos') || '[]').length;

            alert(`🧪 Video de prueba agregado\n📹 Título: ${testVideo.title}\n📂 Categoría: ${testVideo.category}\n🆔 ID: ${testVideo.id}\n📊 Total videos: ${totalVideos}`);

            displayData();
        }

        // Evento para mostrar archivo seleccionado
        document.getElementById('videoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileText = document.getElementById('fileText');

            if (file) {
                const sizeMB = (file.size / 1024 / 1024).toFixed(2);
                fileText.innerHTML = `📹 ${file.name}<br><small>Tamaño: ${sizeMB} MB</small>`;
            } else {
                fileText.textContent = '📹 Clic para seleccionar video';
            }
        });

        // Evento para mostrar archivo seleccionado
        document.getElementById('videoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileText = document.getElementById('fileText');

            if (file) {
                const sizeMB = (file.size / 1024 / 1024).toFixed(2);
                fileText.innerHTML = `📹 ${file.name}<br><small>Tamaño: ${sizeMB} MB | Tipo: ${file.type}</small>`;

                // Debug info
                console.log('Archivo seleccionado:', {
                    name: file.name,
                    size: file.size,
                    sizeMB: sizeMB,
                    type: file.type
                });

                // Verificar si es muy grande
                if (file.size > 500 * 1024 * 1024) {
                    fileText.innerHTML += '<br><span style="color: #ff4444;">⚠️ Archivo muy grande (>500MB)</span>';
                } else if (file.size > 100 * 1024 * 1024) {
                    fileText.innerHTML += '<br><span style="color: #FFA500;">⚠️ Archivo grande - puede tardar en procesar</span>';
                }
            } else {
                fileText.textContent = '📹 Clic para seleccionar video';
            }
        });

        // Inicializar aplicación
        async function initApp() {
            try {
                console.log('🚀 Inicializando aplicación...');
                await initDB();
                console.log('✅ IndexedDB inicializado');
                await displayData();
                console.log('✅ Datos cargados');
            } catch (error) {
                console.error('❌ Error al inicializar:', error);
                // Continuar sin IndexedDB si hay error
                displayData();
            }
        }

        // Cargar datos al iniciar
        initApp();
    </script>
</body>
</html>
