<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Panel de Administración</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
        }

        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .admin-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .admin-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 10px;
            flex-wrap: wrap;
        }

        .tab-button {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 25px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }

        .tab-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .tab-button.active {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #ccc;
            font-size: 0.9rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .data-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
            color: #FFD700;
        }

        .data-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            font-weight: bold;
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: #fff;
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: #fff;
        }

        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #3498db);
            color: #fff;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
            color: #FFD700;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: #28a745;
            color: #fff;
        }

        .status-inactive {
            background: #6c757d;
            color: #fff;
        }

        .status-pending {
            background: #ffc107;
            color: #000;
        }

        .search-box {
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .search-box::placeholder {
            color: #ccc;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: bold;
            z-index: 1000;
        }

        .connection-online {
            background: #28a745;
            color: #fff;
        }

        .connection-offline {
            background: #dc3545;
            color: #fff;
        }

        @media (max-width: 768px) {
            .admin-container {
                padding: 10px;
            }
            
            .admin-header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .data-table {
                font-size: 0.8rem;
            }
            
            .data-table th,
            .data-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <i class="fas fa-circle"></i> Conectando...
    </div>

    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-crown"></i> GERMAYORI Admin Panel</h1>
            <p>Panel de administración y gestión de datos</p>
        </div>

        <div class="admin-tabs">
            <button class="tab-button active" onclick="showTab('users')">
                <i class="fas fa-users"></i> Usuarios
            </button>
            <button class="tab-button" onclick="showTab('signals')">
                <i class="fas fa-chart-line"></i> Señales
            </button>
            <button class="tab-button" onclick="showTab('news')">
                <i class="fas fa-newspaper"></i> Noticias
            </button>
            <button class="tab-button" onclick="showTab('alerts')">
                <i class="fas fa-bell"></i> Alertas
            </button>
            <button class="tab-button" onclick="showTab('stats')">
                <i class="fas fa-chart-bar"></i> Estadísticas
            </button>
        </div>

        <!-- Tab de Usuarios -->
        <div id="users" class="tab-content active">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshUsers()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestUser()">
                    <i class="fas fa-user-plus"></i> Agregar Usuario de Prueba
                </button>
                <button class="btn btn-info" onclick="exportUsers()">
                    <i class="fas fa-download"></i> Exportar
                </button>
            </div>

            <input type="text" class="search-box" placeholder="Buscar usuarios..." id="userSearch" onkeyup="filterUsers()">

            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-users"></i>
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">Total Usuarios</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-user-check"></i>
                    <div class="stat-number" id="activeUsers">0</div>
                    <div class="stat-label">Usuarios Activos</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-user-clock"></i>
                    <div class="stat-number" id="newUsers">0</div>
                    <div class="stat-label">Nuevos (7 días)</div>
                </div>
            </div>

            <div id="usersLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando usuarios...
            </div>

            <table class="data-table" id="usersTable" style="display: none;">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Email</th>
                        <th>Tipo</th>
                        <th>Estado</th>
                        <th>Fecha Registro</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Señales -->
        <div id="signals" class="tab-content">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshSignals()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestSignal()">
                    <i class="fas fa-plus"></i> Agregar Señal de Prueba
                </button>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-chart-line"></i>
                    <div class="stat-number" id="totalSignals">0</div>
                    <div class="stat-label">Total Señales</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-check-circle"></i>
                    <div class="stat-number" id="activeSignals">0</div>
                    <div class="stat-label">Señales Activas</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-trophy"></i>
                    <div class="stat-number" id="profitableSignals">0</div>
                    <div class="stat-label">Rentables</div>
                </div>
            </div>

            <div id="signalsLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando señales...
            </div>

            <table class="data-table" id="signalsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Par</th>
                        <th>Tipo</th>
                        <th>Precio Entrada</th>
                        <th>Stop Loss</th>
                        <th>Take Profit</th>
                        <th>Estado</th>
                        <th>Pips</th>
                        <th>Fecha</th>
                    </tr>
                </thead>
                <tbody id="signalsTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Noticias -->
        <div id="news" class="tab-content">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshNews()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestNews()">
                    <i class="fas fa-plus"></i> Agregar Noticia de Prueba
                </button>
            </div>

            <div id="newsLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando noticias...
            </div>

            <table class="data-table" id="newsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Título</th>
                        <th>Categoría</th>
                        <th>Impacto</th>
                        <th>Moneda</th>
                        <th>Fuente</th>
                        <th>Fecha</th>
                    </tr>
                </thead>
                <tbody id="newsTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Alertas -->
        <div id="alerts" class="tab-content">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshAlerts()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestAlert()">
                    <i class="fas fa-plus"></i> Agregar Alerta de Prueba
                </button>
            </div>

            <div id="alertsLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando alertas...
            </div>

            <table class="data-table" id="alertsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Tipo</th>
                        <th>Símbolo</th>
                        <th>Mensaje</th>
                        <th>Nivel</th>
                        <th>Estado</th>
                        <th>Fecha</th>
                    </tr>
                </thead>
                <tbody id="alertsTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Estadísticas -->
        <div id="stats" class="tab-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-database"></i>
                    <div class="stat-number" id="totalRecords">0</div>
                    <div class="stat-label">Total Registros</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-server"></i>
                    <div class="stat-number" id="serverStatus">OK</div>
                    <div class="stat-label">Estado Servidor</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-cloud"></i>
                    <div class="stat-number" id="awsStatus">OK</div>
                    <div class="stat-label">Estado AWS</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-clock"></i>
                    <div class="stat-number" id="lastUpdate">--</div>
                    <div class="stat-label">Última Actualización</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-info" onclick="testAWSConnection()">
                    <i class="fas fa-plug"></i> Probar Conexión AWS
                </button>
                <button class="btn btn-success" onclick="initializeAWS()">
                    <i class="fas fa-rocket"></i> Inicializar AWS
                </button>
                <button class="btn btn-primary" onclick="refreshAllData()">
                    <i class="fas fa-sync-alt"></i> Actualizar Todo
                </button>
            </div>
        </div>
    </div>

    <script src="admin-panel.js"></script>
</body>
</html>
