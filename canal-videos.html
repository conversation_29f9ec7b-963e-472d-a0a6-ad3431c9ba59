<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Canal de Videos Educativos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #FFD700;
        }

        .header h1 {
            color: #FFD700;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: 1px solid #FFD700;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 215, 0, 0.3);
            color: #FFD700;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .tab-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: 2px solid #FFD700;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #FFD700;
            color: #000;
        }

        .tab-btn:hover {
            background: rgba(255, 215, 0, 0.3);
        }

        .video-section {
            display: none;
        }

        .video-section.active {
            display: block;
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .video-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }

        .video-thumbnail {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            border-radius: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .play-btn {
            background: rgba(255, 215, 0, 0.9);
            color: #000;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-btn:hover {
            background: #FFD700;
            transform: scale(1.1);
        }

        .video-info h3 {
            color: #FFD700;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .video-info p {
            color: #ccc;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .video-duration {
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 0.8rem;
            position: absolute;
            bottom: 10px;
            right: 10px;
        }

        .price-tag {
            background: #FFD700;
            color: #000;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }

        .no-videos {
            text-align: center;
            padding: 50px;
            color: #ccc;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 25px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            color: #FFD700;
            font-size: 2rem;
            font-weight: bold;
        }

        .stat-label {
            color: #ccc;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-tabs {
                flex-direction: column;
                align-items: center;
            }
            
            .video-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📺 CANAL DE VIDEOS EDUCATIVOS</h1>
        <p>Contenido premium de trading forex - GERMAYORI</p>
    </div>

    <div class="container">
        <a href="dashboard-simple.html" class="back-btn">← Volver al Dashboard</a>

        <!-- Estadísticas -->
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalVideos">0</div>
                <div class="stat-label">Videos Totales</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="videosBasicos">0</div>
                <div class="stat-label">Básicos</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="videosIntermedios">0</div>
                <div class="stat-label">Intermedios</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="videosAvanzados">0</div>
                <div class="stat-label">Avanzados</div>
            </div>
        </div>

        <!-- Pestañas de navegación -->
        <div class="nav-tabs">
            <button class="tab-btn active" onclick="showSection('todos')">📺 TODOS LOS VIDEOS</button>
            <button class="tab-btn" onclick="showSection('basico')">📚 BÁSICO ($45/mes)</button>
            <button class="tab-btn" onclick="showSection('intermedio')">📈 INTERMEDIO ($70/mes)</button>
            <button class="tab-btn" onclick="showSection('avanzado')">🚀 AVANZADO ($95/mes)</button>
        </div>

        <!-- Sección Todos los Videos -->
        <div id="todos" class="video-section active">
            <h2 style="color: #FFD700; margin-bottom: 20px;">📺 Todos los Videos Educativos</h2>
            <div class="video-grid" id="videos-todos"></div>
        </div>

        <!-- Sección Videos Básicos -->
        <div id="basico" class="video-section">
            <h2 style="color: #FFD700; margin-bottom: 20px;">📚 Videos Básicos - Fundamentos del Trading</h2>
            <div class="video-grid" id="videos-basico"></div>
        </div>

        <!-- Sección Videos Intermedios -->
        <div id="intermedio" class="video-section">
            <h2 style="color: #FFD700; margin-bottom: 20px;">📈 Videos Intermedios - Estrategias Avanzadas</h2>
            <div class="video-grid" id="videos-intermedio"></div>
        </div>

        <!-- Sección Videos Avanzados -->
        <div id="avanzado" class="video-section">
            <h2 style="color: #FFD700; margin-bottom: 20px;">🚀 Videos Avanzados - Trading Institucional</h2>
            <div class="video-grid" id="videos-avanzado"></div>
        </div>
    </div>

    <script>
        // Función para cambiar entre secciones
        function showSection(section) {
            // Ocultar todas las secciones
            document.querySelectorAll('.video-section').forEach(sec => {
                sec.classList.remove('active');
            });
            
            // Remover clase active de todos los botones
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Mostrar sección seleccionada
            document.getElementById(section).classList.add('active');
            
            // Activar botón correspondiente
            event.target.classList.add('active');
            
            // Cargar videos de la sección
            loadVideos(section);
        }

        // Función para cargar videos
        function loadVideos(category) {
            const videos = getVideosByCategory(category);
            const container = document.getElementById(`videos-${category}`);
            
            if (videos.length === 0) {
                container.innerHTML = `
                    <div class="no-videos">
                        <h3>📹 No hay videos disponibles</h3>
                        <p>Los videos de esta categoría se agregarán pronto.</p>
                        <p style="margin-top: 20px;">
                            <a href="test-videos.html" style="color: #FFD700;">🧪 Ir al panel de pruebas</a>
                        </p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = videos.map(video => `
                <div class="video-card">
                    <div class="video-thumbnail">
                        <button class="play-btn" onclick="playVideo('${video.id}')">▶</button>
                        <div class="video-duration">${video.duration}</div>
                    </div>
                    <div class="video-info">
                        <h3>${video.title}</h3>
                        <p>${video.description}</p>
                        <p><strong>Instructor:</strong> jhon0608</p>
                        <p><strong>Fecha:</strong> ${video.date}</p>
                        <span class="price-tag">${getPriceByCategory(video.category)}</span>
                    </div>
                </div>
            `).join('');
        }

        // Función para obtener videos por categoría
        function getVideosByCategory(category) {
            const allVideos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
            
            if (category === 'todos') {
                return allVideos;
            }
            
            return allVideos.filter(video => video.category === category);
        }

        // Función para obtener precio por categoría
        function getPriceByCategory(category) {
            const prices = {
                'basico': 'BÁSICO $45/mes',
                'intermedio': 'INTERMEDIO $70/mes',
                'avanzado': 'AVANZADO $95/mes'
            };
            return prices[category] || 'PREMIUM';
        }

        // Función para reproducir video
        function playVideo(videoId) {
            const videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
            const video = videos.find(v => v.id === videoId);
            
            if (!video) {
                alert('❌ Video no encontrado');
                return;
            }
            
            // Abrir video en nueva ventana
            if (video.url) {
                window.open(video.url, '_blank');
            } else {
                alert('❌ URL del video no disponible');
            }
        }

        // Función para actualizar estadísticas
        function updateStats() {
            const videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
            
            document.getElementById('totalVideos').textContent = videos.length;
            document.getElementById('videosBasicos').textContent = videos.filter(v => v.category === 'basico').length;
            document.getElementById('videosIntermedios').textContent = videos.filter(v => v.category === 'intermedio').length;
            document.getElementById('videosAvanzados').textContent = videos.filter(v => v.category === 'avanzado').length;
        }

        // Cargar videos al iniciar
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            loadVideos('todos');
        });
    </script>
</body>
</html>
