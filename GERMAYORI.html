<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Trading Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        .admin-panel {
            background: linear-gradient(180deg, #0f3460 0%, #1a1a2e 100%);
            border-left: 2px solid #00d4ff;
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
    </style>
</head>
<body class="h-screen overflow-hidden">
    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <div onclick="showChannel('chat')" id="channel-chat" class="channel-item channel-active p-3 rounded cursor-pointer">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </div>

                <div onclick="showChannel('calculadora')" id="channel-calculadora" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-calculator mr-3 text-green-400"></i>
                    <span>Calculadora Pips</span>
                </div>

                <div onclick="showChannel('noticias')" id="channel-noticias" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i>
                    <span>Noticias Forex</span>
                </div>

                <div onclick="showChannel('senales')" id="channel-senales" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales Trading</span>
                </div>

                <div onclick="showChannel('academia')" id="channel-academia" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-graduation-cap mr-3 text-blue-400"></i>
                    <span>Academia</span>
                </div>

                <div onclick="showChannel('recursos')" id="channel-recursos" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-link mr-3 text-orange-400"></i>
                    <span>MyFXBook</span>
                </div>
            </div>

            <!-- Estado -->
            <div class="mt-8 p-3 bg-gray-800 rounded">
                <div class="text-xs text-gray-400 mb-2">Estado del Sistema</div>
                <div class="flex items-center text-green-400 text-sm">
                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    Online
                </div>
            </div>
        </div>

    <!-- Main Content -->
    <main class="container mx-auto p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Panel - Main Content -->
            <div class="lg:col-span-2">

                <!-- Chat Tab -->
                <div id="content-chat" class="tab-content">
                    <div class="bg-white rounded-lg shadow-lg">
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-t-lg">
                            <h3 class="font-bold text-lg">🤖 GERMAYORI AI - Chat Educativo</h3>
                        </div>

                        <div id="chat-messages" class="h-80 p-4 overflow-y-auto space-y-3">
                            <div class="flex justify-start">
                                <div class="max-w-xs p-3 rounded-lg bg-gray-200 text-gray-800">
                                    ¡Hola! Soy GERMAYORI AI. ¿En qué puedo ayudarte a aprender sobre forex?
                                </div>
                            </div>
                        </div>

                        <div class="p-4 border-t">
                            <div class="flex gap-2">
                                <input
                                    type="text"
                                    id="chat-input"
                                    placeholder="Pregunta sobre forex, pips, trading..."
                                    class="flex-1 p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    onkeypress="if(event.key==='Enter') sendMessage()"
                                />
                                <button
                                    onclick="sendMessage()"
                                    id="send-button"
                                    class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700"
                                >
                                    Enviar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calculadora Tab -->
                <div id="content-calculadora" class="tab-content hidden">
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h3 class="text-xl font-bold mb-4">🧮 Calculadora de Pips</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Tamaño del Lote</label>
                                <input type="number" id="lot-size" placeholder="0.01" class="w-full p-3 border rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Pips</label>
                                <input type="number" id="pips" placeholder="10" class="w-full p-3 border rounded-lg">
                            </div>
                            <button onclick="calculatePips()" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700">
                                Calcular Ganancia/Pérdida
                            </button>
                            <div id="calc-result" class="hidden mt-4 p-4 bg-green-100 rounded-lg">
                                <p id="result-text" class="text-green-800 font-bold"></p>
                            </div>
                            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                                <p class="text-sm text-gray-600 mb-2">💡 <strong>Recursos Recomendados:</strong></p>
                                <div class="space-y-1">
                                    <a href="https://www.myfxbook.com/forex-calculators/profit-calculator" target="_blank" class="block text-blue-600 hover:underline text-sm">
                                        🔗 MyFXBook Calculadora Avanzada
                                    </a>
                                    <a href="https://www.myfxbook.com/forex-economic-calendar" target="_blank" class="block text-blue-600 hover:underline text-sm">
                                        📅 MyFXBook Calendario Económico
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recursos Tab -->
                <div id="content-recursos" class="tab-content hidden">
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h3 class="text-xl font-bold mb-4">📚 Recursos Educativos</h3>
                        <div class="space-y-4">
                            <div class="border-l-4 border-blue-500 pl-4">
                                <h4 class="font-semibold">🧮 Calculadora de Profit</h4>
                                <p class="text-gray-600 mb-2">Herramienta profesional para calcular ganancias y pérdidas</p>
                                <a href="https://www.myfxbook.com/forex-calculators/profit-calculator" target="_blank" class="text-blue-600 hover:underline">
                                    🔗 Ir a MyFXBook Calculadora →
                                </a>
                            </div>
                            <div class="border-l-4 border-green-500 pl-4">
                                <h4 class="font-semibold">📅 Calendario Económico</h4>
                                <p class="text-gray-600 mb-2">Eventos económicos que impactan los mercados</p>
                                <a href="https://www.myfxbook.com/forex-economic-calendar" target="_blank" class="text-blue-600 hover:underline">
                                    🔗 Ir a MyFXBook Calendario →
                                </a>
                            </div>
                            <div class="border-l-4 border-yellow-500 pl-4">
                                <h4 class="font-semibold">💡 Consejos de Trading</h4>
                                <ul class="text-gray-600 space-y-1 mt-2">
                                    <li>✓ Nunca arriesgues más del 2% de tu capital</li>
                                    <li>✓ Usa siempre stop loss</li>
                                    <li>✓ Practica con cuenta demo primero</li>
                                    <li>✓ Estudia análisis técnico y fundamental</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold mb-4">📊 Estado del Sistema</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>Frontend</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">✅ Activo</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Chat AI</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">✅ Online</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Calculadora</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">✅ Disponible</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">🎯 Bienvenido a GERMAYORI</h4>
                        <p class="text-blue-700 text-sm">
                            Tu plataforma educativa de trading forex. Aprende, practica y mejora tus habilidades.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-6 mt-12">
        <div class="container mx-auto text-center">
            <p class="font-medium">&copy; 2024 GERMAYORI - Trading Educativo</p>
            <p class="text-gray-400 text-sm mt-2">
                ⚠️ Recuerda: El trading conlleva riesgos. Nunca inviertas más de lo que puedes permitirte perder.
            </p>
        </div>
    </footer>

    <script>
        // Variables globales
        let isLoading = false;

        // Función para cambiar tabs
        function showTab(tabName) {
            // Ocultar todos los contenidos
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Mostrar el contenido seleccionado
            document.getElementById('content-' + tabName).classList.remove('hidden');

            // Actualizar estilos de tabs
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.className = tab.className.replace('tab-active', 'tab-inactive');
            });

            document.getElementById('tab-' + tabName).className =
                document.getElementById('tab-' + tabName).className.replace('tab-inactive', 'tab-active');
        }

        // Función para enviar mensaje
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message || isLoading) return;

            addMessage(message, 'user');
            input.value = '';

            isLoading = true;
            document.getElementById('send-button').disabled = true;

            // Simular respuesta del AI
            setTimeout(() => {
                let response = getAIResponse(message);
                addMessage(response, 'bot');
                isLoading = false;
                document.getElementById('send-button').disabled = false;
            }, 1000);
        }

        // Función para obtener respuesta del AI
        function getAIResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('hola')) {
                return '¡Hola! Soy GERMAYORI AI. ¿En qué puedo ayudarte a aprender sobre forex?';
            } else if (lowerMessage.includes('forex')) {
                return 'Forex es el mercado de divisas más grande del mundo. Te recomiendo usar la calculadora de MyFXBook: https://www.myfxbook.com/forex-calculators/profit-calculator';
            } else if (lowerMessage.includes('pip')) {
                return 'Los pips son la unidad mínima de cambio en forex. Para EUR/USD, 1 pip = 0.0001. Usa nuestra calculadora para practicar.';
            } else if (lowerMessage.includes('noticia') || lowerMessage.includes('calendario')) {
                return 'Para noticias económicas importantes, revisa el calendario de MyFXBook: https://www.myfxbook.com/forex-economic-calendar';
            } else {
                return 'Gracias por tu pregunta. Te recomiendo revisar los recursos de MyFXBook para más información sobre trading forex.';
            }
        }

        // Función para agregar mensaje al chat
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex ' + (sender === 'user' ? 'justify-end' : 'justify-start');

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'max-w-xs p-3 rounded-lg ' +
                (sender === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800');
            bubbleDiv.textContent = text;

            messageDiv.appendChild(bubbleDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Función para calcular pips
        function calculatePips() {
            const lotSize = parseFloat(document.getElementById('lot-size').value);
            const pips = parseFloat(document.getElementById('pips').value);

            if (lotSize && pips) {
                const profit = lotSize * pips * 10;
                document.getElementById('result-text').textContent = '💰 Resultado: $' + profit.toFixed(2) + ' USD';
                document.getElementById('calc-result').classList.remove('hidden');
            } else {
                alert('Por favor ingresa valores válidos para el lote y los pips.');
            }
        }

        // Inicializar
        console.log('GERMAYORI Trading Educativo - Aplicación iniciada');
    </script>
</body>
</html>
