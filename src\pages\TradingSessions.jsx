import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Crown, TrendingUp, TrendingDown } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import PaywallModal from '../components/Paywall/PaywallModal';

const TradingSessions = () => {
  const { user } = useAuth();
  const [showPaywall, setShowPaywall] = useState(!user?.isPaid);

  const mockSessions = [
    { id: 1, date: '2024-01-15', trades: 5, profit: 125.50, winRate: 80 },
    { id: 2, date: '2024-01-14', trades: 3, profit: -45.20, winRate: 33 },
    { id: 3, date: '2024-01-13', trades: 7, profit: 234.75, winRate: 71 },
  ];

  if (!user?.isPaid) {
    return (
      <>
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="w-24 h-24 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="text-warning" size={48} />
            </div>
            <h1 className="text-3xl font-bold text-text mb-4">Sesiones de Trading Premium</h1>
            <p className="text-gray-400 max-w-2xl mx-auto mb-8">
              Registra y analiza tus sesiones de trading para mejorar tu rendimiento 
              y identificar patrones en tu estrategia.
            </p>
            <Button variant="primary" onClick={() => setShowPaywall(true)}>
              Desbloquear Sesiones
            </Button>
          </motion.div>
        </div>
        <PaywallModal 
          isOpen={showPaywall} 
          onClose={() => setShowPaywall(false)} 
          feature="Sesiones de Trading"
        />
      </>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <Calendar className="mr-3 text-danger" size={32} />
            Sesiones de Trading
            <Crown className="ml-2 text-warning" size={24} />
          </h1>
          <p className="text-gray-400">
            Historial y análisis de tus sesiones de trading
          </p>
        </div>
        <Button variant="primary">
          Nueva Sesión
        </Button>
      </motion.div>

      <div className="grid gap-4">
        {mockSessions.map((session) => (
          <Card key={session.id} className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-text">{session.date}</h3>
              <p className="text-gray-400">{session.trades} operaciones</p>
            </div>
            <div className="text-right">
              <p className={`font-bold ${session.profit > 0 ? 'text-success' : 'text-danger'}`}>
                {session.profit > 0 ? '+' : ''}${session.profit}
              </p>
              <p className="text-gray-400">{session.winRate}% éxito</p>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default TradingSessions;
