<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Señales</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- AWS SDK -->
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.1563.0.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>
                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i><span>Chat Educativo</span>
                </a>
                <a href="calculadora.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-calculator mr-3 text-green-400"></i><span>Calculadora</span>
                </a>
                <a href="noticias.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i><span>Noticias</span>
                </a>
                <a href="senales.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i><span>Señales</span>
                </a>
                <a href="notificaciones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-bell mr-3 text-pink-400"></i><span>Notificaciones</span>
                </a>
                <a href="alertas.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i><span>Alertas Mercado</span>
                </a>
            </div>

            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- Signals Content -->
        <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-white mb-2">📈 Señales de Trading</h1>
                <p class="text-gray-200">Señales educativas para aprender análisis técnico</p>
            </div>



            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Active Signals -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-purple-600">🔥 Señales Activas</h2>
                        <button onclick="loadTestSignals()" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                            <i class="fas fa-sync-alt mr-1"></i>Actualizar
                        </button>
                    </div>
                    <div id="loading" class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-purple-600 text-2xl mb-2"></i>
                        <p class="text-gray-600">Cargando señales desde AWS...</p>
                    </div>
                    <div id="signals-container" class="space-y-4 hidden">
                        <!-- Las señales se cargarán aquí dinámicamente -->
                    </div>
                    <div id="no-signals" class="text-center py-8 hidden">
                        <i class="fas fa-chart-line text-gray-400 text-3xl mb-2"></i>
                        <p class="text-gray-600">No hay señales disponibles</p>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-purple-600">📊 Estadísticas</h2>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-green-50 rounded">
                            <span class="text-green-700">Señales Ganadoras</span>
                            <span class="font-bold text-green-700">7</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-red-50 rounded">
                            <span class="text-red-700">Señales Perdedoras</span>
                            <span class="font-bold text-red-700">2</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-blue-50 rounded">
                            <span class="text-blue-700">Tasa de Éxito</span>
                            <span class="font-bold text-blue-700">78%</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                            <span class="font-semibold text-yellow-800">Aviso Educativo</span>
                        </div>
                        <p class="text-sm text-yellow-700">
                            Estas señales son solo para fines educativos. Siempre haz tu propio análisis.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuración AWS
        AWS.config.update({
            region: 'us-east-1',
            accessKeyId: 'AKIAZI6IXQHQHQHQHQHQ', // Reemplaza con tu Access Key
            secretAccessKey: 'tu-secret-key-aqui' // Reemplaza con tu Secret Key
        });

        const dynamodb = new AWS.DynamoDB.DocumentClient();

        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        function loadUserInfo() {
            const user = checkAuth();
            if (user) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type.toUpperCase();
                document.getElementById('user-avatar').src = user.avatar;
            }
        }

        // Función para cargar señales desde AWS DynamoDB
        async function loadSignals() {
            try {
                // Primero intentamos AWS
                const params = {
                    TableName: 'germayori-signals'
                };

                const result = await dynamodb.scan(params).promise();
                const signals = result.Items;

                displaySignals(signals);
            } catch (error) {
                console.error('Error cargando señales desde AWS:', error);

                // Si falla AWS, usamos datos de prueba
                console.log('Cargando señales de prueba...');
                loadTestSignals();
            }
        }

        // Función para cargar señales de prueba
        function loadTestSignals() {
            // Cargar señales desde localStorage
            const localSignals = JSON.parse(localStorage.getItem('germayori_signals')) || [];

            if (localSignals.length > 0) {
                console.log('Señales encontradas:', localSignals);
                displaySignals(localSignals);
            } else {
                console.log('No hay señales en localStorage');
                displaySignals([]);
            }
        }

        // Función para mostrar las señales
        function displaySignals(signals) {
            const loadingDiv = document.getElementById('loading');
            const signalsContainer = document.getElementById('signals-container');
            const noSignalsDiv = document.getElementById('no-signals');

            loadingDiv.classList.add('hidden');

            if (signals.length === 0) {
                noSignalsDiv.classList.remove('hidden');
                return;
            }

            signalsContainer.classList.remove('hidden');
            signalsContainer.innerHTML = '';

            signals.forEach(signal => {
                const signalElement = createSignalElement(signal);
                signalsContainer.appendChild(signalElement);
            });
        }

        // Función para crear elemento de señal
        function createSignalElement(signal) {
            const div = document.createElement('div');
            const actionColor = signal.action === 'buy' ? 'green' : signal.action === 'sell' ? 'red' : 'blue';

            div.innerHTML = `
                <div class="border-l-4 border-${actionColor}-500 pl-4 bg-${actionColor}-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center mb-3">
                        <div>
                            <span class="font-bold text-${actionColor}-700 text-lg">${signal.pair.toUpperCase()}</span>
                            <div class="text-xs text-blue-600 font-semibold">📊 Señal enviada y analizada por ${signal.author || 'GERMAYORI'}</div>
                        </div>
                        <span class="bg-${actionColor}-500 text-white px-3 py-1 rounded text-sm font-bold">${signal.action.toUpperCase()}</span>
                    </div>
                    <div class="text-sm text-gray-600 grid grid-cols-2 gap-2 mb-3">
                        <div><strong>Entrada:</strong> ${signal.entry}</div>
                        <div><strong>Stop Loss:</strong> ${signal.stoploss}</div>
                        <div><strong>Take Profit 1:</strong> ${signal.takeprofit1}</div>
                        <div><strong>Take Profit 2:</strong> ${signal.takeprofit2}</div>
                    </div>
                    ${signal.analysis ? `
                        <div class="bg-blue-100 border border-blue-300 rounded-lg p-3 mb-3">
                            <div class="text-blue-700 text-xs font-semibold mb-1">📝 ANÁLISIS GERMAYORI:</div>
                            <div class="text-gray-700 text-sm">${signal.analysis}</div>
                        </div>
                    ` : ''}
                    <div class="flex justify-between items-center">
                        <div class="text-${actionColor}-600 font-semibold">🟢 Activa</div>
                        <div class="text-xs text-gray-500">${new Date(signal.timestamp).toLocaleString()}</div>
                    </div>
                </div>
            `;

            return div;
        }

        // Función para mostrar error
        function showError() {
            const loadingDiv = document.getElementById('loading');
            const signalsContainer = document.getElementById('signals-container');

            loadingDiv.classList.add('hidden');
            signalsContainer.classList.remove('hidden');
            signalsContainer.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-2"></i>
                    <p class="text-red-600">Error cargando señales desde AWS</p>
                    <button onclick="loadSignals()" class="mt-2 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                        Reintentar
                    </button>
                </div>
            `;
        }

        window.onload = function() {
            loadUserInfo();
            // Cargar señales desde localStorage
            loadTestSignals();
        };
    </script>
</body>
</html>
