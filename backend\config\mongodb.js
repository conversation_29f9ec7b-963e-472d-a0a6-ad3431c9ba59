const mongoose = require('mongoose');
require('dotenv').config();

// Connection string de MongoDB Atlas
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://macleanjhon8:<EMAIL>/germayori?retryWrites=true&w=majority&appName=Cluster0';

// Configuración de conexión
const connectDB = async () => {
    try {
        console.log('🔄 Conectando a MongoDB Atlas...');

        const conn = await mongoose.connect(MONGODB_URI, {
            serverSelectionTimeoutMS: 5000, // Timeout después de 5s en lugar de 30s
            socketTimeoutMS: 45000, // Cerrar sockets después de 45s de inactividad
        });

        console.log(`✅ MongoDB conectado: ${conn.connection.host}`);
        console.log(`📊 Base de datos: ${conn.connection.name}`);

        return conn;
    } catch (error) {
        console.error('❌ Error conectando a MongoDB:', error.message);

        // Si falla MongoDB, usar SQLite como fallback
        console.log('🔄 Fallback a SQLite...');
        return null;
    }
};

// Cerrar conexión
const closeDB = async () => {
    try {
        await mongoose.connection.close();
        console.log('✅ Conexión MongoDB cerrada');
    } catch (error) {
        console.error('❌ Error cerrando MongoDB:', error.message);
    }
};

// Verificar estado de conexión
const isConnected = () => {
    return mongoose.connection.readyState === 1;
};

module.exports = {
    connectDB,
    closeDB,
    isConnected,
    MONGODB_URI
};
