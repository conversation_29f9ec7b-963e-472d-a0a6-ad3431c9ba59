<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗄️ GERMAYORI - Panel de Base de Datos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .section-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            font-size: 1.2rem;
            font-weight: bold;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-content {
            padding: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
            color: #FFD700;
        }

        .table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .refresh-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px 5px;
            transition: transform 0.2s;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
        }

        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }

        .type-admin {
            color: #FFD700;
            font-weight: bold;
        }

        .type-user {
            color: #17a2b8;
        }

        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            opacity: 0.7;
        }

        .aws-section {
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            color: white;
        }

        .aws-section .section-header {
            background: rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ GERMAYORI - Panel de Base de Datos</h1>
            <p>Gestión completa de usuarios, señales y datos AWS</p>
            <button class="refresh-btn" onclick="loadAllData()">🔄 Actualizar Todo</button>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">-</div>
                <div class="stat-label">👥 Total Usuarios</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSignals">-</div>
                <div class="stat-label">📈 Total Señales</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeSignals">-</div>
                <div class="stat-label">🟢 Señales Activas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="adminUsers">-</div>
                <div class="stat-label">👑 Administradores</div>
            </div>
        </div>

        <!-- USUARIOS -->
        <div class="section">
            <div class="section-header">
                👥 USUARIOS REGISTRADOS
                <button class="refresh-btn" onclick="loadUsers()">🔄 Actualizar</button>
            </div>
            <div class="section-content">
                <div id="usersContent" class="loading">Cargando usuarios...</div>
            </div>
        </div>

        <!-- SEÑALES -->
        <div class="section">
            <div class="section-header">
                📈 SEÑALES DE TRADING
                <button class="refresh-btn" onclick="loadSignals()">🔄 Actualizar</button>
            </div>
            <div class="section-content">
                <div id="signalsContent" class="loading">Cargando señales...</div>
            </div>
        </div>

        <!-- AWS MANAGEMENT -->
        <div class="section aws-section">
            <div class="section-header">
                ☁️ GESTIÓN AWS - ENVIAR SEÑALES
                <button class="refresh-btn" onclick="showAWSPanel()">📤 Panel AWS</button>
            </div>
            <div class="section-content">
                <h3>🚀 Enviar Señal desde AWS a Canal</h3>
                <p><strong>Usuario AWS:</strong> germayori08</p>
                <p><strong>URL:</strong> https://082565699654.signin.aws.amazon.com/console</p>
                
                <div style="margin-top: 20px;">
                    <h4>📤 Crear Nueva Señal:</h4>
                    <form id="awsSignalForm" style="margin-top: 15px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <input type="text" id="signalPair" placeholder="Par (ej: EURUSD)" required style="padding: 10px; border-radius: 5px; border: none;">
                            <select id="signalType" required style="padding: 10px; border-radius: 5px; border: none;">
                                <option value="">Tipo de Señal</option>
                                <option value="BUY">BUY</option>
                                <option value="SELL">SELL</option>
                            </select>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <input type="number" id="entryPrice" placeholder="Precio Entrada" step="0.00001" required style="padding: 10px; border-radius: 5px; border: none;">
                            <input type="number" id="stopLoss" placeholder="Stop Loss" step="0.00001" required style="padding: 10px; border-radius: 5px; border: none;">
                            <input type="number" id="takeProfit" placeholder="Take Profit" step="0.00001" required style="padding: 10px; border-radius: 5px; border: none;">
                        </div>
                        <textarea id="analysis" placeholder="Análisis de la señal..." rows="3" required style="width: 100%; padding: 10px; border-radius: 5px; border: none; margin-bottom: 15px;"></textarea>
                        <button type="submit" class="refresh-btn" style="background: linear-gradient(45deg, #007bff, #0056b3);">📤 Enviar Señal a Canal</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Cargar todos los datos al inicio
        window.onload = function() {
            loadAllData();
        };

        async function loadAllData() {
            await Promise.all([
                loadUsers(),
                loadSignals(),
                updateStats()
            ]);
        }

        async function loadUsers() {
            try {
                const response = await fetch('/api/users');
                const users = await response.json();
                
                let html = '<table class="table"><thead><tr><th>ID</th><th>Nombre</th><th>Email</th><th>Tipo</th><th>Fecha Registro</th></tr></thead><tbody>';
                
                users.forEach(user => {
                    const typeClass = user.type === 'admin' ? 'type-admin' : 'type-user';
                    const date = new Date(user.created_at).toLocaleString('es-ES');
                    html += `
                        <tr>
                            <td>${user.id}</td>
                            <td>${user.name}</td>
                            <td>${user.email}</td>
                            <td class="${typeClass}">${user.type.toUpperCase()}</td>
                            <td>${date}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table>';
                document.getElementById('usersContent').innerHTML = html;
            } catch (error) {
                document.getElementById('usersContent').innerHTML = '<p>Error cargando usuarios: ' + error.message + '</p>';
            }
        }

        async function loadSignals() {
            try {
                const response = await fetch('/api/signals');
                const signals = await response.json();
                
                let html = '<table class="table"><thead><tr><th>ID</th><th>Par</th><th>Tipo</th><th>Entrada</th><th>SL</th><th>TP</th><th>Estado</th><th>Usuario</th><th>Fecha</th></tr></thead><tbody>';
                
                signals.forEach(signal => {
                    const statusClass = signal.status === 'active' ? 'status-active' : 'status-inactive';
                    const date = new Date(signal.created_at).toLocaleString('es-ES');
                    html += `
                        <tr>
                            <td>${signal.id}</td>
                            <td><strong>${signal.pair}</strong></td>
                            <td>${signal.type}</td>
                            <td>${signal.entry_price}</td>
                            <td>${signal.stop_loss}</td>
                            <td>${signal.take_profit}</td>
                            <td class="${statusClass}">${signal.status.toUpperCase()}</td>
                            <td>${signal.user_name || 'N/A'}</td>
                            <td>${date}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table>';
                document.getElementById('signalsContent').innerHTML = html;
            } catch (error) {
                document.getElementById('signalsContent').innerHTML = '<p>Error cargando señales: ' + error.message + '</p>';
            }
        }

        async function updateStats() {
            try {
                const [usersResponse, signalsResponse] = await Promise.all([
                    fetch('/api/users'),
                    fetch('/api/signals')
                ]);
                
                const users = await usersResponse.json();
                const signals = await signalsResponse.json();
                
                document.getElementById('totalUsers').textContent = users.length;
                document.getElementById('totalSignals').textContent = signals.length;
                document.getElementById('activeSignals').textContent = signals.filter(s => s.status === 'active').length;
                document.getElementById('adminUsers').textContent = users.filter(u => u.type === 'admin').length;
            } catch (error) {
                console.error('Error actualizando estadísticas:', error);
            }
        }

        // Manejar envío de señal desde AWS
        document.getElementById('awsSignalForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const signalData = {
                user_id: 1, // Admin user
                pair: document.getElementById('signalPair').value,
                type: document.getElementById('signalType').value,
                entry_price: parseFloat(document.getElementById('entryPrice').value),
                stop_loss: parseFloat(document.getElementById('stopLoss').value),
                take_profit: parseFloat(document.getElementById('takeProfit').value),
                analysis: document.getElementById('analysis').value,
                risk_level: 'medium',
                timeframe: 'H4'
            };
            
            try {
                const response = await fetch('/api/signals', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(signalData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ Señal enviada exitosamente al canal!');
                    document.getElementById('awsSignalForm').reset();
                    loadSignals();
                    updateStats();
                } else {
                    alert('❌ Error enviando señal: ' + result.error);
                }
            } catch (error) {
                alert('❌ Error: ' + error.message);
            }
        });

        function showAWSPanel() {
            window.open('https://082565699654.signin.aws.amazon.com/console', '_blank');
        }
    </script>
</body>
</html>
