import axios from 'axios';

const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';

export const sendMessage = async (req, res) => {
  try {
    const { message } = req.body;
    const { isPaid } = req.user;

    // Verificar que el usuario tenga acceso premium
    if (!isPaid) {
      return res.status(403).json({
        success: false,
        message: 'Acceso denegado. Se requiere plan Premium para usar el chat educativo.'
      });
    }

    // Validación básica
    if (!message || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'El mensaje es requerido'
      });
    }

    if (message.length > 1000) {
      return res.status(400).json({
        success: false,
        message: 'El mensaje es demasiado largo (máximo 1000 caracteres)'
      });
    }

    // Sistema de prompt educativo
    const systemPrompt = `Eres un asistente educativo especializado en trading y análisis técnico. Tu objetivo es enseñar conceptos de trading de manera clara y educativa.

REGLAS IMPORTANTES:
1. NUNCA proporciones señales de trading específicas (como "compra EUR/USD ahora")
2. NUNCA des consejos de inversión directos
3. SIEMPRE enfócate en educar sobre conceptos, estrategias y análisis técnico
4. Explica los conceptos de manera clara y didáctica
5. Incluye ejemplos educativos cuando sea apropiado
6. Recuerda al usuario que debe hacer su propia investigación
7. Responde en español

Puedes ayudar con:
- Conceptos de análisis técnico
- Explicación de indicadores
- Gestión de riesgo
- Psicología del trading
- Patrones de velas japonesas
- Estrategias educativas generales
- Fundamentos del mercado forex

Recuerda: Tu propósito es educar, no dar señales de trading.`;

    try {
      // Intentar usar OpenAI API
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: message }
          ],
          max_tokens: 500,
          temperature: 0.7,
        },
        {
          headers: {
            'Authorization': `Bearer ${OPENAI_API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000 // 30 segundos
        }
      );

      const aiResponse = response.data.choices[0].message.content;

      res.json({
        success: true,
        response: aiResponse,
        timestamp: new Date().toISOString()
      });

    } catch (openaiError) {
      console.error('Error con OpenAI API:', openaiError.message);
      
      // Fallback con respuestas predefinidas
      const fallbackResponse = generateFallbackResponse(message);
      
      res.json({
        success: true,
        response: fallbackResponse,
        timestamp: new Date().toISOString(),
        note: 'Respuesta generada localmente (OpenAI no disponible)'
      });
    }

  } catch (error) {
    console.error('Error en chat:', error);
    res.status(500).json({
      success: false,
      message: 'Error interno del servidor'
    });
  }
};

// Función de respuestas de fallback
function generateFallbackResponse(message) {
  const lowerMessage = message.toLowerCase();
  
  const responses = {
    'análisis técnico': `El análisis técnico es el estudio de los movimientos de precios históricos para predecir futuros movimientos del mercado. Se basa en tres principios fundamentales:

1. **Los precios descuentan todo**: Toda la información disponible ya está reflejada en el precio
2. **Los precios se mueven en tendencias**: Los mercados tienden a moverse en direcciones específicas
3. **La historia se repite**: Los patrones de comportamiento del mercado tienden a repetirse

Para aplicar análisis técnico, estudia gráficos, patrones e indicadores técnicos. Recuerda siempre hacer tu propia investigación antes de tomar decisiones de trading.`,

    'soportes y resistencias': `Los soportes y resistencias son niveles clave en el análisis técnico:

**Soporte**: Nivel de precio donde la demanda es lo suficientemente fuerte para detener una caída. Es como un "piso" que sostiene el precio.

**Resistencia**: Nivel donde la oferta es fuerte para detener una subida. Actúa como un "techo" que limita el precio.

**Cómo identificarlos**:
- Busca niveles donde el precio ha rebotado múltiples veces
- Usa máximos y mínimos anteriores significativos
- Observa niveles psicológicos redondos (1.0000, 1.2000, etc.)
- Considera líneas de tendencia

Estos niveles son fundamentales para planificar entradas y salidas en el trading.`,

    'gestión de riesgo': `La gestión de riesgo es el aspecto más importante del trading exitoso:

**Reglas fundamentales**:
1. **Regla del 1-2%**: Nunca arriesgues más del 1-2% de tu capital por operación
2. **Stop Loss obligatorio**: Siempre define tu pérdida máxima antes de entrar
3. **Relación Riesgo/Beneficio**: Busca al menos 1:2 (arriesgar $1 para ganar $2)
4. **Diversificación**: No pongas todos los huevos en una canasta
5. **Control emocional**: No hagas trading basado en emociones

**Fórmula básica**:
Tamaño de posición = (Capital × % Riesgo) ÷ Distancia al Stop Loss

La gestión de riesgo te permite sobrevivir a las pérdidas inevitables y mantenerte en el juego a largo plazo.`,

    'rsi': `El RSI (Relative Strength Index) es un oscilador momentum que mide la velocidad y magnitud de los cambios de precio:

**Características**:
- Oscila entre 0 y 100
- Período estándar: 14 períodos
- Creado por J. Welles Wilder

**Interpretación básica**:
- **RSI > 70**: Posible sobrecompra (el precio podría bajar)
- **RSI < 30**: Posible sobreventa (el precio podría subir)
- **RSI = 50**: Punto neutral

**Señales avanzadas**:
- **Divergencias**: Cuando el precio y RSI van en direcciones opuestas
- **Fallo de swing**: RSI no confirma nuevos máximos/mínimos del precio

Recuerda que ningún indicador es infalible. Siempre combina el RSI con otros análisis y confirma las señales.`,

    'velas japonesas': `Las velas japonesas son una forma de representar el precio que muestra 4 valores: apertura, cierre, máximo y mínimo.

**Anatomía de una vela**:
- **Cuerpo**: Diferencia entre apertura y cierre
- **Mechas/Sombras**: Máximos y mínimos del período
- **Verde/Blanca**: Cierre > Apertura (alcista)
- **Roja/Negra**: Cierre < Apertura (bajista)

**Patrones importantes**:
- **Doji**: Apertura ≈ Cierre (indecisión del mercado)
- **Martillo**: Cuerpo pequeño arriba, mecha larga abajo (posible reversión alcista)
- **Estrella fugaz**: Cuerpo pequeño abajo, mecha larga arriba (posible reversión bajista)
- **Envolvente**: Una vela "envuelve" completamente a la anterior

Cada patrón debe confirmarse con el contexto del mercado y otros indicadores.`,

    'default': `Esa es una excelente pregunta sobre trading. Como asistente educativo, mi objetivo es ayudarte a entender los conceptos fundamentales del trading y análisis técnico.

Algunos temas que puedo explicarte:
- Análisis técnico y fundamental
- Indicadores técnicos (RSI, MACD, Medias móviles)
- Gestión de riesgo y money management
- Patrones de velas japonesas
- Soportes y resistencias
- Psicología del trading
- Tipos de órdenes
- Timeframes y su importancia

**Recuerda**: Siempre haz tu propia investigación y nunca inviertas más de lo que puedes permitirte perder. El trading conlleva riesgos significativos.

¿Hay algún concepto específico que te gustaría que explique en detalle?`
  };

  // Buscar respuesta relevante
  for (const [key, response] of Object.entries(responses)) {
    if (key !== 'default' && lowerMessage.includes(key)) {
      return response;
    }
  }

  return responses.default;
}
