<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Panel de Mentoría (jhon0608)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .admin-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .notification-badge {
            background: linear-gradient(45deg, #ff4444, #ff6666);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .student-card {
            background: rgba(255, 255, 255, 0.05);
            border-left: 4px solid #00d4ff;
        }
    </style>
</head>
<body class="min-h-screen p-6">

    <!-- Header -->
    <div class="admin-card rounded-lg p-6 mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">
                    <i class="fas fa-user-tie mr-3 text-cyan-400"></i>Panel de Mentoría - jhon0608
                </h1>
                <p class="text-gray-300">Gestión de estudiantes y sesiones de mentoría</p>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold text-cyan-400" id="total-students">0</div>
                <div class="text-sm text-gray-300">Estudiantes Activos</div>
            </div>
        </div>
    </div>

    <!-- Notificaciones Urgentes -->
    <div id="urgent-notifications" class="mb-6">
        <!-- Se llenarán dinámicamente -->
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="admin-card rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-green-400" id="completed-courses">0</div>
            <div class="text-sm text-gray-300">Cursos Completados</div>
        </div>
        
        <div class="admin-card rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-yellow-400" id="pending-mentorias">0</div>
            <div class="text-sm text-gray-300">Mentorías Pendientes</div>
        </div>
        
        <div class="admin-card rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-blue-400" id="active-mentorias">0</div>
            <div class="text-sm text-gray-300">Mentorías Activas</div>
        </div>
        
        <div class="admin-card rounded-lg p-6 text-center">
            <div class="text-3xl font-bold text-purple-400" id="total-revenue">$0</div>
            <div class="text-sm text-gray-300">Ingresos Mentoría</div>
        </div>
    </div>

    <!-- Cola de Mentoría -->
    <div class="admin-card rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-white mb-4">
            <i class="fas fa-users mr-3 text-orange-400"></i>Cola de Mentoría
        </h2>
        <div id="mentoria-queue" class="space-y-4">
            <!-- Se llenará dinámicamente -->
        </div>
        
        <div class="mt-6 text-center">
            <button onclick="scheduleGroupSession()" id="schedule-group-btn" class="bg-gradient-to-r from-green-500 to-blue-600 text-white px-8 py-3 rounded-lg font-bold hover:from-green-600 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fas fa-calendar-plus mr-2"></i>Agendar Sesión Grupal
            </button>
        </div>
    </div>

    <!-- Estudiantes Activos -->
    <div class="admin-card rounded-lg p-6">
        <h2 class="text-2xl font-bold text-white mb-4">
            <i class="fas fa-graduation-cap mr-3 text-cyan-400"></i>Estudiantes con Mentoría Activa
        </h2>
        <div id="active-students" class="space-y-4">
            <!-- Se llenará dinámicamente -->
        </div>
    </div>

    <script>
        // Cargar datos del panel
        function loadAdminData() {
            const mentoriaQueue = JSON.parse(localStorage.getItem('mentoria_queue') || '[]');
            const allUsers = getAllUsers();
            
            // Actualizar estadísticas
            updateStats(allUsers, mentoriaQueue);
            
            // Mostrar notificaciones urgentes
            showUrgentNotifications(mentoriaQueue);
            
            // Mostrar cola de mentoría
            displayMentoriaQueue(mentoriaQueue);
            
            // Mostrar estudiantes activos
            displayActiveStudents(allUsers);
        }

        // Obtener todos los usuarios (simulado)
        function getAllUsers() {
            // En producción esto vendría de la base de datos
            const currentUser = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            return [currentUser]; // Simulamos que solo hay un usuario por ahora
        }

        // Actualizar estadísticas
        function updateStats(users, queue) {
            let completedCourses = 0;
            let activeMentorias = 0;
            let totalRevenue = 0;

            users.forEach(user => {
                const courses = user.courses || {};
                if (courses.basico?.completed) completedCourses++;
                if (courses.intermedio?.completed) completedCourses++;
                if (courses.avanzado?.completed) completedCourses++;
                
                if (user.mentoria?.hasAccess) {
                    activeMentorias++;
                    totalRevenue += 400;
                }
            });

            document.getElementById('total-students').textContent = users.length;
            document.getElementById('completed-courses').textContent = completedCourses;
            document.getElementById('pending-mentorias').textContent = queue.length;
            document.getElementById('active-mentorias').textContent = activeMentorias;
            document.getElementById('total-revenue').textContent = `$${totalRevenue}`;
        }

        // Mostrar notificaciones urgentes
        function showUrgentNotifications(queue) {
            const container = document.getElementById('urgent-notifications');
            
            if (queue.length >= 5) {
                container.innerHTML = `
                    <div class="notification-badge rounded-lg p-6 text-center">
                        <div class="text-white text-xl font-bold mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            ¡ALERTA! ${queue.length} estudiantes esperando mentoría grupal
                        </div>
                        <p class="text-red-100">Es momento de agendar una sesión de Zoom grupal</p>
                    </div>
                `;
            } else {
                container.innerHTML = '';
            }
        }

        // Mostrar cola de mentoría
        function displayMentoriaQueue(queue) {
            const container = document.getElementById('mentoria-queue');
            const scheduleBtn = document.getElementById('schedule-group-btn');
            
            if (queue.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <i class="fas fa-users text-4xl mb-4"></i>
                        <p>No hay estudiantes en cola para mentoría</p>
                    </div>
                `;
                scheduleBtn.disabled = true;
                return;
            }

            scheduleBtn.disabled = queue.length < 5;
            
            container.innerHTML = queue.map((student, index) => `
                <div class="student-card rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-cyan-500 rounded-full flex items-center justify-center mr-4">
                                <span class="text-white font-bold">${index + 1}</span>
                            </div>
                            <div>
                                <div class="text-white font-semibold">${student.userName}</div>
                                <div class="text-gray-400 text-sm">${student.userId}</div>
                                <div class="text-gray-400 text-xs">Solicitó: ${new Date(student.requestDate).toLocaleDateString()}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-yellow-400 font-bold">Esperando</div>
                            <div class="text-gray-400 text-sm">Sesiones: ${student.sessionsCompleted}/3</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Mostrar estudiantes activos
        function displayActiveStudents(users) {
            const container = document.getElementById('active-students');
            const activeStudents = users.filter(user => user.mentoria?.hasAccess);
            
            if (activeStudents.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <i class="fas fa-graduation-cap text-4xl mb-4"></i>
                        <p>No hay estudiantes con mentoría activa</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = activeStudents.map(student => {
                const mentoria = student.mentoria;
                const sessionsLeft = 3 - (mentoria.sessionsCompleted || 0);
                
                return `
                    <div class="student-card rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="${student.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(student.name)}" 
                                     alt="Avatar" class="w-12 h-12 rounded-full mr-4">
                                <div>
                                    <div class="text-white font-semibold">${student.name}</div>
                                    <div class="text-gray-400 text-sm">${student.email}</div>
                                    <div class="text-gray-400 text-xs">Inició: ${new Date(mentoria.purchaseDate).toLocaleDateString()}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-green-400 font-bold">Sesiones: ${mentoria.sessionsCompleted || 0}/3</div>
                                <div class="text-gray-400 text-sm">Restantes: ${sessionsLeft}</div>
                                ${mentoria.nextSession ? `<div class="text-blue-400 text-xs">Próxima: ${mentoria.nextSession}</div>` : ''}
                                <button onclick="completeSessionForStudent('${student.email}')" 
                                        class="mt-2 bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700">
                                    Marcar Sesión Completada
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Agendar sesión grupal
        function scheduleGroupSession() {
            const queue = JSON.parse(localStorage.getItem('mentoria_queue') || '[]');
            
            if (queue.length < 5) {
                alert('Necesitas al menos 5 estudiantes para agendar una sesión grupal');
                return;
            }

            const confirmed = confirm(`¿Agendar sesión grupal con ${queue.length} estudiantes?\n\nSe enviará link de Zoom a todos los participantes.`);
            
            if (confirmed) {
                // Simular agendamiento
                const sessionDate = new Date();
                sessionDate.setDate(sessionDate.getDate() + 3); // En 3 días
                
                const zoomLink = 'https://zoom.us/j/987654321?pwd=xyz789';
                
                alert(`✅ ¡Sesión grupal agendada!\n\n📅 Fecha: ${sessionDate.toLocaleDateString()} a las 8:00 PM\n👥 Participantes: ${queue.length}\n🔗 Zoom: ${zoomLink}\n\n📧 Se enviará confirmación a todos los estudiantes.`);
                
                // Limpiar cola después del agendamiento
                localStorage.setItem('mentoria_queue', '[]');
                
                // Recargar datos
                loadAdminData();
            }
        }

        // Completar sesión para un estudiante
        function completeSessionForStudent(userEmail) {
            const confirmed = confirm(`¿Marcar sesión como completada para ${userEmail}?`);
            
            if (confirmed) {
                // En producción esto actualizaría la base de datos
                console.log(`✅ Sesión completada para ${userEmail}`);
                alert(`✅ Sesión marcada como completada para ${userEmail}`);
                
                // Recargar datos
                loadAdminData();
            }
        }

        // Inicializar panel
        window.onload = function() {
            loadAdminData();
            
            // Actualizar cada 30 segundos
            setInterval(loadAdminData, 30000);
        };
    </script>
</body>
</html>
