<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - TradingView</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>
                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i><span>Chat Educativo</span>
                </a>
                <a href="calculadora.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-calculator mr-3 text-green-400"></i><span>Calculadora</span>
                </a>
                <a href="noticias.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i><span>Noticias</span>
                </a>
                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i><span>Señales</span>
                </a>
                <a href="notificaciones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-bell mr-3 text-pink-400"></i><span>Notificaciones</span>
                </a>
                <a href="alertas.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i><span>Alertas Mercado</span>
                </a>
                <a href="tradingview.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-area mr-3 text-red-400"></i><span>TradingView</span>
                </a>
            </div>

            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- TradingView Content -->
        <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-white mb-2">📊 TradingView</h1>
                <p class="text-gray-200">Gráficos profesionales en tiempo real</p>
            </div>

            <!-- TradingView Completo -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-bold mb-4 text-red-600">📈 TradingView - Plataforma Completa</h2>

                <!-- Controles -->
                <div class="mb-4 flex flex-wrap gap-2 items-center">
                    <input type="text" id="symbol-search" placeholder="Buscar símbolo (ej: EURUSD, AAPL, BTC)"
                           class="px-3 py-2 border rounded-lg flex-1 min-w-64">
                    <button onclick="searchSymbol()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                        <i class="fas fa-search mr-2"></i>Buscar
                    </button>
                    <button onclick="openFullTradingView()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                        <i class="fas fa-expand mr-2"></i>Pantalla Completa
                    </button>
                </div>

                <!-- TradingView Chart Completo -->
                <div class="bg-gray-100 rounded-lg p-2">
                    <div id="tradingview_chart" style="height: 600px; width: 100%;"></div>
                    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
                    <script type="text/javascript">
                        let widget;
                        function initTradingView() {
                            widget = new TradingView.widget({
                                "width": "100%",
                                "height": 600,
                                "symbol": "FX:EURUSD",
                                "interval": "D",
                                "timezone": "Etc/UTC",
                                "theme": "light",
                                "style": "1",
                                "locale": "es",
                                "toolbar_bg": "#f1f3f6",
                                "enable_publishing": false,
                                "allow_symbol_change": true,
                                "container_id": "tradingview_chart",
                                "studies": [
                                    "MASimple@tv-basicstudies",
                                    "RSI@tv-basicstudies"
                                ],
                                "show_popup_button": true,
                                "popup_width": "1000",
                                "popup_height": "650"
                            });
                        }

                        // Inicializar cuando se cargue la página
                        if (typeof TradingView !== 'undefined') {
                            initTradingView();
                        } else {
                            setTimeout(initTradingView, 1000);
                        }
                    </script>
                </div>

                <!-- Accesos Rápidos -->
                <div class="mt-4">
                    <h3 class="font-bold mb-2">🚀 Accesos Rápidos:</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                        <!-- Forex -->
                        <button onclick="loadSymbol('EURUSD')" class="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600">EUR/USD</button>
                        <button onclick="loadSymbol('GBPUSD')" class="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600">GBP/USD</button>
                        <button onclick="loadSymbol('USDJPY')" class="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600">USD/JPY</button>
                        <button onclick="loadSymbol('AUDUSD')" class="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600">AUD/USD</button>

                        <!-- Criptos -->
                        <button onclick="loadSymbol('BTCUSD')" class="bg-orange-500 text-white px-3 py-2 rounded text-sm hover:bg-orange-600">BTC/USD</button>
                        <button onclick="loadSymbol('ETHUSD')" class="bg-orange-500 text-white px-3 py-2 rounded text-sm hover:bg-orange-600">ETH/USD</button>

                        <!-- Índices -->
                        <button onclick="loadSymbol('SPX')" class="bg-purple-500 text-white px-3 py-2 rounded text-sm hover:bg-purple-600">S&P 500</button>
                        <button onclick="loadSymbol('DJI')" class="bg-purple-500 text-white px-3 py-2 rounded text-sm hover:bg-purple-600">Dow Jones</button>

                        <!-- Materias Primas -->
                        <button onclick="loadSymbol('XAUUSD')" class="bg-yellow-500 text-white px-3 py-2 rounded text-sm hover:bg-yellow-600">Oro</button>
                        <button onclick="loadSymbol('XAGUSD')" class="bg-gray-400 text-white px-3 py-2 rounded text-sm hover:bg-gray-500">Plata</button>
                        <button onclick="loadSymbol('USOIL')" class="bg-black text-white px-3 py-2 rounded text-sm hover:bg-gray-800">Petróleo</button>
                        <button onclick="loadSymbol('NATGAS')" class="bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700">Gas Natural</button>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">📊 TradingView completo con todas las herramientas: líneas de tendencia, indicadores, análisis técnico y más</p>
                </div>
            </div>

            <!-- Quick Charts -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white rounded-lg shadow-lg p-4">
                    <h3 class="font-bold text-center mb-2">EUR/USD</h3>
                    <div class="tradingview-widget-container" style="height:200px;">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
                        {
                            "symbol": "FX:EURUSD",
                            "width": "100%",
                            "height": "200",
                            "locale": "es",
                            "dateRange": "12M",
                            "colorTheme": "light",
                            "isTransparent": false,
                            "autosize": true,
                            "largeChartUrl": ""
                        }
                        </script>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-4">
                    <h3 class="font-bold text-center mb-2">GBP/USD</h3>
                    <div class="tradingview-widget-container" style="height:200px;">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
                        {
                            "symbol": "FX:GBPUSD",
                            "width": "100%",
                            "height": "200",
                            "locale": "es",
                            "dateRange": "12M",
                            "colorTheme": "light",
                            "isTransparent": false,
                            "autosize": true,
                            "largeChartUrl": ""
                        }
                        </script>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-4">
                    <h3 class="font-bold text-center mb-2">USD/JPY</h3>
                    <div class="tradingview-widget-container" style="height:200px;">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
                        {
                            "symbol": "FX:USDJPY",
                            "width": "100%",
                            "height": "200",
                            "locale": "es",
                            "dateRange": "12M",
                            "colorTheme": "light",
                            "isTransparent": false,
                            "autosize": true,
                            "largeChartUrl": ""
                        }
                        </script>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-4">
                    <h3 class="font-bold text-center mb-2">AUD/USD</h3>
                    <div class="tradingview-widget-container" style="height:200px;">
                        <div class="tradingview-widget-container__widget"></div>
                        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
                        {
                            "symbol": "FX:AUDUSD",
                            "width": "100%",
                            "height": "200",
                            "locale": "es",
                            "dateRange": "12M",
                            "colorTheme": "light",
                            "isTransparent": false,
                            "autosize": true,
                            "largeChartUrl": ""
                        }
                        </script>
                    </div>
                </div>
            </div>

            <!-- Market Overview -->
            <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold mb-4 text-red-600">🌍 Resumen del Mercado</h2>
                <div class="tradingview-widget-container">
                    <div class="tradingview-widget-container__widget"></div>
                    <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-forex-cross-rates.js" async>
                    {
                        "width": "100%",
                        "height": 400,
                        "currencies": [
                            "EUR",
                            "USD",
                            "JPY",
                            "GBP",
                            "CHF",
                            "AUD",
                            "CAD",
                            "NZD"
                        ],
                        "isTransparent": false,
                        "colorTheme": "light",
                        "locale": "es"
                    }
                    </script>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        function loadUserInfo() {
            const user = checkAuth();
            if (user) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type.toUpperCase();
                document.getElementById('user-avatar').src = user.avatar;
            }
        }

        // Función para cargar símbolo
        function loadSymbol(symbol) {
            let symbolPrefix = '';

            // Determinar el prefijo según el tipo de activo
            if (['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'NZDUSD', 'USDCHF'].includes(symbol)) {
                symbolPrefix = 'FX:';
            } else if (['BTCUSD', 'ETHUSD', 'ADAUSD', 'DOTUSD'].includes(symbol)) {
                symbolPrefix = 'BINANCE:';
            } else if (['SPX', 'DJI', 'IXIC'].includes(symbol)) {
                symbolPrefix = 'TVC:';
            } else if (['XAUUSD', 'XAGUSD', 'USOIL', 'NATGAS'].includes(symbol)) {
                symbolPrefix = 'TVC:';
            }

            // Cambiar símbolo en el widget
            if (widget && widget.chart) {
                widget.chart().setSymbol(symbolPrefix + symbol);
            } else {
                // Si el widget no está listo, recrearlo con el nuevo símbolo
                document.getElementById('tradingview_chart').innerHTML = '';
                widget = new TradingView.widget({
                    "width": "100%",
                    "height": 600,
                    "symbol": symbolPrefix + symbol,
                    "interval": "D",
                    "timezone": "Etc/UTC",
                    "theme": "light",
                    "style": "1",
                    "locale": "es",
                    "toolbar_bg": "#f1f3f6",
                    "enable_publishing": false,
                    "allow_symbol_change": true,
                    "container_id": "tradingview_chart",
                    "studies": [
                        "MASimple@tv-basicstudies",
                        "RSI@tv-basicstudies"
                    ],
                    "show_popup_button": true,
                    "popup_width": "1000",
                    "popup_height": "650"
                });
            }
        }

        // Función para buscar símbolo
        function searchSymbol() {
            const searchInput = document.getElementById('symbol-search');
            const symbol = searchInput.value.trim().toUpperCase();
            if (symbol) {
                loadSymbol(symbol);
                searchInput.value = '';
            }
        }

        // Función para abrir TradingView en pantalla completa
        function openFullTradingView() {
            const iframe = document.getElementById('tradingview-chart');
            if (iframe.requestFullscreen) {
                iframe.requestFullscreen();
            } else if (iframe.webkitRequestFullscreen) {
                iframe.webkitRequestFullscreen();
            } else if (iframe.msRequestFullscreen) {
                iframe.msRequestFullscreen();
            }
        }

        // Permitir búsqueda con Enter
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('symbol-search');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchSymbol();
                    }
                });
            }
        });

        window.onload = function() {
            loadUserInfo();
        };
    </script>
</body>
</html>
