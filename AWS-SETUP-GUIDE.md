# 🚀 GUÍA DE CONFIGURACIÓN AWS PARA GERMAYORI

## ✅ ESTADO ACTUAL
- **Usuario AWS:** `germayori08`
- **Contraseña:** `djD47R&1`
- **URL:** `https://082565699654.signin.aws.amazon.com/console`
- **Servidor:** ✅ Funcionando en http://localhost:3000
- **MyFXBook:** ✅ Integrado (Portfolio ID: 11537608)

## 🔧 CONFIGURACIÓN PENDIENTE

### 1. COMPLETAR INSTALACIÓN DE DEPENDENCIAS
```bash
cd backend
npm install aws-sdk sqlite3 multer multer-s3 uuid
```

### 2. CONFIGURAR CREDENCIALES AWS
Editar `backend/.env`:
```env
# Reemplazar con credenciales reales
AWS_ACCESS_KEY_ID=tu_access_key_aqui
AWS_SECRET_ACCESS_KEY=tu_secret_key_aqui
AWS_REGION=us-east-1
```

### 3. CREAR RECURSOS AWS

#### DynamoDB Tables:
- `germayori-users`
- `germayori-signals` 
- `germayori-news`
- `germayori-alerts`

#### S3 Bucket:
- `germayori-storage`

### 4. CAMBIAR A SERVIDOR COMPLETO
```bash
# Cambiar de server-simple.js a server.js
node backend/server.js
```

## 🎯 FUNCIONALIDADES ACTUALES

### ✅ FUNCIONANDO AHORA:
- ✅ Servidor Express en puerto 3000
- ✅ API de autenticación (login/register)
- ✅ API de señales (crear/obtener)
- ✅ Integración MyFXBook simulada
- ✅ Noticias Forex simuladas
- ✅ Base de datos en memoria
- ✅ Usuario admin: <EMAIL> / admin123

### 🔄 PENDIENTE (cuando AWS esté configurado):
- 🔄 Base de datos DynamoDB
- 🔄 Almacenamiento S3
- 🔄 Monitoreo automático de noticias
- 🔄 Integración real con MyFXBook API
- 🔄 Sistema de alertas en tiempo real

## 📊 ENDPOINTS DISPONIBLES

### Autenticación:
- `POST /api/login` - Iniciar sesión
- `POST /api/register` - Registrar usuario

### Señales:
- `GET /api/signals` - Obtener señales
- `POST /api/signals` - Crear señal

### MyFXBook:
- `GET /api/myfxbook/portfolio` - Datos del portfolio
- `GET /api/myfxbook/balance` - Balance en tiempo real

### Noticias:
- `GET /api/forex/news` - Noticias de Forex

## 🚀 CÓMO USAR AHORA

### 1. Acceder a la aplicación:
```
http://localhost:3000
```

### 2. Login como admin:
- Email: `<EMAIL>`
- Password: `admin123`

### 3. Probar APIs:
```bash
# Obtener señales
curl http://localhost:3000/api/signals

# Obtener portfolio MyFXBook
curl http://localhost:3000/api/myfxbook/portfolio

# Obtener noticias
curl http://localhost:3000/api/forex/news
```

## 🔑 CREDENCIALES AWS CREADAS

### Usuario IAM: `germayori08`
- **Políticas asignadas:**
  - AmazonS3FullAccess
  - AmazonRDSFullAccess  
  - AWSLambdaExecute

### Acceso a consola:
- **URL:** https://082565699654.signin.aws.amazon.com/console
- **Usuario:** germayori08
- **Contraseña:** djD47R&1

## 📝 PRÓXIMOS PASOS

1. **Liberar espacio en disco** para instalar dependencias completas
2. **Configurar credenciales AWS** en el archivo .env
3. **Crear recursos AWS** (DynamoDB, S3)
4. **Cambiar a servidor completo** con todas las funcionalidades
5. **Configurar dominio** para producción

## 🎯 ARQUITECTURA COMPLETA PLANIFICADA

```
GERMAYORI Frontend
       ↓
Express.js Backend
       ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   DynamoDB      │      S3         │    Lambda       │
│   (Database)    │   (Storage)     │  (Functions)    │
└─────────────────┴─────────────────┴─────────────────┘
       ↓                    ↓                    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   MyFXBook      │  Forex Factory  │    OpenAI       │
│   (Trading)     │    (News)       │    (Chat)       │
└─────────────────┴─────────────────┴─────────────────┘
```

## 🔒 SEGURIDAD

- ✅ Rate limiting configurado
- ✅ Helmet para headers de seguridad
- ✅ CORS configurado
- ✅ Validación de entrada
- 🔄 JWT tokens (pendiente)
- 🔄 Encriptación de contraseñas (pendiente)

---

**¡GERMAYORI está listo para usar! 🚀**

Para cualquier problema, revisar los logs del servidor o contactar soporte.
