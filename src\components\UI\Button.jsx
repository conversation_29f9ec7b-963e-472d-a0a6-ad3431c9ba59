import React from 'react';
import { motion } from 'framer-motion';

const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  disabled = false, 
  loading = false,
  onClick,
  className = '',
  type = 'button',
  ...props 
}) => {
  const baseClasses = 'font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 transform active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none';
  
  const variants = {
    primary: 'bg-danger hover:bg-red-600 text-white shadow-lg hover:shadow-xl focus:ring-danger',
    secondary: 'bg-accent hover:bg-blue-600 text-white shadow-lg hover:shadow-xl focus:ring-accent',
    outline: 'border-2 border-danger text-danger hover:bg-danger hover:text-white focus:ring-danger',
    ghost: 'text-danger hover:bg-danger hover:bg-opacity-10 focus:ring-danger',
    success: 'bg-success hover:bg-green-600 text-white shadow-lg hover:shadow-xl focus:ring-success',
    warning: 'bg-warning hover:bg-yellow-600 text-white shadow-lg hover:shadow-xl focus:ring-warning'
  };

  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl'
  };

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;

  return (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      className={classes}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      {...props}
    >
      {loading ? (
        <div className="flex items-center justify-center">
          <div className="spinner mr-2"></div>
          Cargando...
        </div>
      ) : (
        children
      )}
    </motion.button>
  );
};

export default Button;
