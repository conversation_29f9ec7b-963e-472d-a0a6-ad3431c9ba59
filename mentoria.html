<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Mentoría Directa</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .vip-card {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            border: 2px solid #ffd700;
        }
        .locked-content {
            opacity: 0.5;
            filter: grayscale(100%);
        }
        .requirement-item {
            background: rgba(255, 255, 255, 0.1);
            border-left: 4px solid #ef4444;
        }
        .requirement-completed {
            border-left-color: #10b981;
        }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="videos-educativos.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>

                <a href="mentoria.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-user-graduate mr-3 text-violet-400"></i>
                    <span>Mentoría Directa</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="tradingview.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-area mr-3 text-red-400"></i>
                    <span>TradingView</span>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- Mentoría Content -->
        <div class="flex-1 main-content m-4 rounded-lg overflow-y-auto">

            <!-- Header -->
            <div class="bg-gradient-to-r from-violet-500 to-purple-600 text-white p-6 rounded-t-lg">
                <h2 class="text-3xl font-bold mb-2">👨‍🏫 Mentoría Directa GERMAYORI</h2>
                <p class="text-purple-100">Sesiones personalizadas con el creador de la estrategia</p>
            </div>

            <!-- VIP Package -->
            <div class="p-6">
                <div class="vip-card rounded-lg p-8 text-center mb-6">
                    <div class="text-6xl mb-4">👑</div>
                    <h3 class="text-3xl font-bold text-white mb-4">Mentoría VIP Exclusiva</h3>
                    <div class="text-4xl font-bold text-yellow-300 mb-6">$400</div>

                    <div class="bg-white bg-opacity-20 rounded-lg p-6 mb-6">
                        <h4 class="text-xl font-bold text-white mb-4">📅 Incluye:</h4>
                        <div class="space-y-3 text-left">
                            <div class="flex items-center text-white">
                                <i class="fas fa-clock text-yellow-300 mr-3"></i>
                                <span><strong>3 sesiones</strong> de 2 horas cada una</span>
                            </div>
                            <div class="flex items-center text-white">
                                <i class="fas fa-user-tie text-yellow-300 mr-3"></i>
                                <span><strong>Mentoría directa</strong> con el creador de GERMAYORI</span>
                            </div>
                            <div class="flex items-center text-white">
                                <i class="fas fa-chart-line text-yellow-300 mr-3"></i>
                                <span><strong>Análisis en vivo</strong> de tus trades</span>
                            </div>
                            <div class="flex items-center text-white">
                                <i class="fas fa-brain text-yellow-300 mr-3"></i>
                                <span><strong>Perfeccionamiento</strong> de la estrategia FVG</span>
                            </div>
                            <div class="flex items-center text-white">
                                <i class="fas fa-target text-yellow-300 mr-3"></i>
                                <span><strong>Plan personalizado</strong> de trading</span>
                            </div>
                            <div class="flex items-center text-white">
                                <i class="fas fa-whatsapp text-yellow-300 mr-3"></i>
                                <span><strong>Soporte directo</strong> por WhatsApp</span>
                            </div>
                        </div>
                    </div>

                    <div id="mentoria-button-container">
                        <!-- El botón se generará dinámicamente -->
                    </div>
                </div>

                <!-- Requisitos -->
                <div class="bg-white bg-opacity-10 rounded-lg p-6">
                    <h3 class="text-2xl font-bold text-white mb-4">
                        <i class="fas fa-graduation-cap mr-3"></i>Requisitos para Acceder
                    </h3>
                    <p class="text-gray-200 mb-6">
                        Para garantizar que aproveches al máximo la mentoría, debes completar todos los cursos educativos antes de acceder.
                    </p>

                    <div class="space-y-4">
                        <div class="requirement-item p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-times-circle text-red-400 mr-3"></i>
                                    <span class="text-white">Curso Básico ($25/mes)</span>
                                </div>
                                <span class="text-red-400 font-bold">PENDIENTE</span>
                            </div>
                        </div>

                        <div class="requirement-item p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-times-circle text-red-400 mr-3"></i>
                                    <span class="text-white">Curso Intermedio ($50/mes)</span>
                                </div>
                                <span class="text-red-400 font-bold">PENDIENTE</span>
                            </div>
                        </div>

                        <div class="requirement-item p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-times-circle text-red-400 mr-3"></i>
                                    <span class="text-white">Curso Avanzado ($75/mes)</span>
                                </div>
                                <span class="text-red-400 font-bold">PENDIENTE</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-500 bg-opacity-20 rounded-lg border border-yellow-400">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-400 mr-3"></i>
                            <div>
                                <h4 class="font-bold text-yellow-300">¿Por qué estos requisitos?</h4>
                                <p class="text-yellow-100 text-sm mt-1">
                                    Sin la base teórica completa, no podrás aprovechar la mentoría. Los cursos te preparan para entender conceptos avanzados y hacer preguntas inteligentes.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <a href="videos-educativos.html" class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-lg font-bold hover:from-blue-600 hover:to-purple-700 transition-all inline-block">
                            <i class="fas fa-play-circle mr-2"></i>Comenzar con los Cursos
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }
            }
        }

        // Verificar si puede acceder a mentoría
        function checkMentoriaAccess() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const courses = user.courses || {};
            const mentoria = user.mentoria || {};

            // Verificar cursos completados
            const allCompleted = courses.basico?.completed &&
                               courses.intermedio?.completed &&
                               courses.avanzado?.completed;

            const buttonContainer = document.getElementById('mentoria-button-container');

            // Si ya completó las 3 mentorías, bloquear acceso
            if (mentoria.sessionsCompleted >= 3) {
                buttonContainer.innerHTML = `
                    <div class="locked-content">
                        <button disabled class="bg-red-500 text-white px-8 py-4 rounded-lg font-bold text-xl cursor-not-allowed">
                            <i class="fas fa-ban mr-2"></i>Mentoría Completada
                        </button>
                        <p class="text-red-200 mt-3">Has completado tus 3 sesiones de mentoría. ¡Felicidades!</p>
                        <p class="text-red-200 text-sm">El acceso está bloqueado para dar oportunidad a otros estudiantes.</p>
                    </div>
                `;
                return;
            }

            // Si tiene mentoría activa, mostrar progreso
            if (mentoria.hasAccess) {
                const sessionsLeft = 3 - (mentoria.sessionsCompleted || 0);
                buttonContainer.innerHTML = `
                    <div class="bg-green-500 bg-opacity-20 rounded-lg p-6 mb-4">
                        <h4 class="text-green-300 font-bold text-lg mb-2">✅ Mentoría VIP Activa</h4>
                        <p class="text-green-200 mb-4">Sesiones completadas: ${mentoria.sessionsCompleted || 0}/3</p>
                        <p class="text-green-200 mb-4">Sesiones restantes: ${sessionsLeft}</p>

                        ${mentoria.nextSession ? `
                            <div class="bg-blue-500 bg-opacity-30 rounded p-3 mb-4">
                                <p class="text-blue-200"><strong>Próxima sesión:</strong></p>
                                <p class="text-blue-100">${mentoria.nextSession}</p>
                            </div>
                        ` : ''}
                    </div>

                    <button onclick="scheduleNextSession()" class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-bold text-xl hover:from-blue-600 hover:to-purple-700 transition-all">
                        <i class="fas fa-video mr-2"></i>Agendar Próxima Sesión
                    </button>
                `;
                return;
            }

            // Si completó todos los cursos, puede reservar
            if (allCompleted) {
                buttonContainer.innerHTML = `
                    <button onclick="bookMentoria()" class="bg-gradient-to-r from-yellow-500 to-orange-600 text-white px-8 py-4 rounded-lg font-bold text-xl hover:from-yellow-600 hover:to-orange-700 transition-all">
                        <i class="fas fa-calendar-plus mr-2"></i>Reservar Mentoría VIP
                    </button>
                `;
            } else {
                // Mostrar progreso de cursos
                updateCourseRequirements();
                buttonContainer.innerHTML = `
                    <div class="locked-content">
                        <button disabled class="bg-gray-500 text-gray-300 px-8 py-4 rounded-lg font-bold text-xl cursor-not-allowed">
                            <i class="fas fa-lock mr-2"></i>Completa los Cursos Primero
                        </button>
                        <p class="text-gray-300 mt-3">Debes completar todos los cursos para acceder</p>
                    </div>
                `;
            }
        }

        // Actualizar estado de requisitos de cursos
        function updateCourseRequirements() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const courses = user.courses || {};

            const requirements = [
                { id: 'basico', name: 'Curso Básico ($25/mes)', completed: courses.basico?.completed },
                { id: 'intermedio', name: 'Curso Intermedio ($50/mes)', completed: courses.intermedio?.completed },
                { id: 'avanzado', name: 'Curso Avanzado ($75/mes)', completed: courses.avanzado?.completed }
            ];

            // Actualizar UI de requisitos (esto se haría en el HTML)
            console.log('📊 Estado de cursos:', requirements);
        }

        // Función para reservar mentoría
        function bookMentoria() {
            // Mostrar pasarela de pago
            showPaymentGateway();
        }

        // Mostrar pasarela de pago
        function showPaymentGateway() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            `;

            modal.innerHTML = `
                <div style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); border-radius: 20px; padding: 30px; max-width: 600px; width: 100%; border: 3px solid #FFD700; position: relative;">
                    <button onclick="this.closest('.payment-modal').remove()" style="position: absolute; top: 15px; right: 15px; background: #ff4444; color: white; border: none; width: 35px; height: 35px; border-radius: 50%; cursor: pointer; font-size: 18px; font-weight: bold;">&times;</button>

                    <div style="text-align: center; margin-bottom: 30px;">
                        <h2 style="color: #FFD700; font-size: 2rem; margin-bottom: 10px;">💳 Pagar Mentoría VIP</h2>
                        <div style="color: white; font-size: 1.5rem; font-weight: bold; margin-bottom: 5px;">$400 USD</div>
                        <div style="color: #ccc; font-size: 0.9rem;">3 sesiones de 2 horas cada una</div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                        <!-- PayPal -->
                        <div style="text-align: center; background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; border: 2px solid #0070ba;">
                            <h3 style="color: #0070ba; margin-bottom: 15px;">💙 PayPal</h3>
                            <div style="background: white; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzAwNzBiYSIvPgogIDx0ZXh0IHg9Ijc1IiB5PSI4MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+UGF5UGFsIFFSPC90ZXh0Pgo8L3N2Zz4=" style="width: 120px; height: 120px;" alt="PayPal QR">
                            </div>
                            <div style="color: white; font-size: 0.9rem; margin-bottom: 10px;"><EMAIL></div>
                            <button onclick="confirmPayment('paypal')" style="background: #0070ba; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: bold;">✅ Pagué con PayPal</button>
                        </div>

                        <!-- Binance -->
                        <div style="text-align: center; background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; border: 2px solid #f3ba2f;">
                            <h3 style="color: #f3ba2f; margin-bottom: 15px;">₿ Binance Pay</h3>
                            <div style="background: white; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iI2YzYmEyZiIvPgogIDx0ZXh0IHg9Ijc1IiB5PSI4MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSJibGFjayIgdGV4dC1hbmNob3I9Im1pZGRsZSI+QmluYW5jZSBRUjwvdGV4dD4KPC9zdmc+" style="width: 120px; height: 120px;" alt="Binance QR">
                            </div>
                            <div style="color: white; font-size: 0.9rem; margin-bottom: 10px;">ID: jhon0608crypto</div>
                            <button onclick="confirmPayment('binance')" style="background: #f3ba2f; color: black; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: bold;">✅ Pagué con Binance</button>
                        </div>
                    </div>

                    <div style="background: rgba(255, 215, 0, 0.1); border: 2px solid #FFD700; border-radius: 15px; padding: 20px; margin-bottom: 20px;">
                        <h4 style="color: #FFD700; margin-bottom: 10px;">📋 Instrucciones de Pago:</h4>
                        <div style="color: white; font-size: 0.9rem; line-height: 1.5;">
                            1. Escanea el QR o usa los datos de contacto<br>
                            2. Envía exactamente <strong>$400 USD</strong><br>
                            3. Haz clic en "✅ Pagué" después de enviar<br>
                            4. Recibirás confirmación en 24 horas máximo
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <div style="color: #ccc; font-size: 0.8rem; margin-bottom: 15px;">
                            ⚠️ Solo haz clic en "Pagué" después de enviar el dinero
                        </div>
                        <button onclick="this.closest('.payment-modal').remove()" style="background: #666; color: white; border: none; padding: 12px 25px; border-radius: 20px; cursor: pointer;">Cancelar</button>
                    </div>
                </div>
            `;

            modal.className = 'payment-modal';
            document.body.appendChild(modal);
        }

        // Confirmar pago
        function confirmPayment(method) {
            const methodNames = {
                'paypal': 'PayPal',
                'binance': 'Binance Pay'
            };

            const confirmed = confirm(`¿Confirmas que enviaste $400 USD via ${methodNames[method]}?\n\n⚠️ Solo confirma si ya enviaste el pago.\n\nUna vez confirmado:\n• Tu acceso será activado\n• jhon0608 verificará el pago\n• Recibirás el enlace de Zoom en 24h`);

            if (confirmed) {
                // Cerrar modal
                document.querySelector('.payment-modal').remove();

                // Activar mentoría para el usuario
                activateMentoria(method);

                // Notificar al mentor (jhon0608)
                notifyMentorNewStudent(method);

                alert('🎉 ¡Pago confirmado!\n\n✅ Tu acceso ha sido activado\n📧 jhon0608 verificará tu pago\n📅 Recibirás el enlace de Zoom en 24 horas\n\n¡Prepárate para llevar tu trading al siguiente nivel!');

                // Recargar la página para mostrar el nuevo estado
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }

        // Activar mentoría para el usuario
        function activateMentoria(paymentMethod) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');

            user.mentoria = {
                hasAccess: true,
                purchaseDate: new Date().toISOString(),
                sessionsCompleted: 0,
                sessionsRemaining: 3,
                nextSession: null,
                zoomLinks: [],
                paymentMethod: paymentMethod,
                paymentAmount: 400,
                paymentStatus: 'pending_verification'
            };

            localStorage.setItem('germayori_user', JSON.stringify(user));
        }

        // Notificar al mentor sobre nuevo estudiante
        function notifyMentorNewStudent(paymentMethod) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const methodNames = {
                'paypal': 'PayPal (<EMAIL>)',
                'binance': 'Binance Pay (ID: jhon0608crypto)'
            };

            // Simular notificación al mentor
            console.log('🔔 NOTIFICACIÓN URGENTE PARA jhon0608:');
            console.log(`💰 NUEVO PAGO DE MENTORÍA VIP - $400 USD`);
            console.log(`👤 Estudiante: ${user.name}`);
            console.log(`📧 Email: ${user.email}`);
            console.log(`💳 Método de pago: ${methodNames[paymentMethod]}`);
            console.log(`📅 Fecha: ${new Date().toLocaleString()}`);
            console.log(`🎓 Cursos completados: ✅ Básico, ✅ Intermedio, ✅ Avanzado`);
            console.log(`⚠️ VERIFICAR PAGO Y ACTIVAR ACCESO`);

            // Agregar a lista de espera para agendamiento grupal
            addToMentoriaQueue();

            // Guardar notificación para jhon0608
            savePaymentNotification(user, paymentMethod);

            // En producción esto enviaría email/WhatsApp a jhon0608
            // sendNotificationToMentor(user);
        }

        // Guardar notificación de pago para jhon0608
        function savePaymentNotification(user, paymentMethod) {
            let notifications = JSON.parse(localStorage.getItem('jhon0608_notifications') || '[]');

            notifications.unshift({
                id: 'payment_' + Date.now(),
                type: 'mentoria_payment',
                user: user,
                amount: 400,
                method: paymentMethod,
                date: new Date().toISOString(),
                status: 'pending_verification',
                urgent: true
            });

            localStorage.setItem('jhon0608_notifications', JSON.stringify(notifications));
            console.log('📱 Notificación guardada para jhon0608');
        }

        // Agregar a cola de mentoría para agendamiento grupal
        function addToMentoriaQueue() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            let mentoriaQueue = JSON.parse(localStorage.getItem('mentoria_queue') || '[]');

            mentoriaQueue.push({
                userId: user.email,
                userName: user.name,
                requestDate: new Date().toISOString(),
                sessionsCompleted: 0,
                status: 'waiting_for_schedule'
            });

            localStorage.setItem('mentoria_queue', JSON.stringify(mentoriaQueue));

            console.log(`📋 Usuario agregado a cola de mentoría. Total en espera: ${mentoriaQueue.length}`);

            // Si hay 5 o más personas, notificar para agendar sesión grupal
            if (mentoriaQueue.length >= 5) {
                notifyGroupSessionReady();
            }
        }

        // Notificar que hay suficientes personas para sesión grupal
        function notifyGroupSessionReady() {
            const mentoriaQueue = JSON.parse(localStorage.getItem('mentoria_queue') || '[]');

            console.log('🎯 ALERTA PARA jhon0608:');
            console.log(`👥 HAY ${mentoriaQueue.length} PERSONAS LISTAS PARA MENTORÍA GRUPAL`);
            console.log('📅 Es momento de agendar una sesión de Zoom grupal');
            console.log('👤 Estudiantes en espera:');

            mentoriaQueue.forEach((student, index) => {
                console.log(`${index + 1}. ${student.userName} (${student.userId}) - ${new Date(student.requestDate).toLocaleDateString()}`);
            });

            // En producción esto enviaría notificación urgente a jhon0608
            // sendGroupSessionAlert(mentoriaQueue);
        }

        // Agendar próxima sesión
        function scheduleNextSession() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const mentoria = user.mentoria || {};

            // Simular agendamiento
            const nextDate = new Date();
            nextDate.setDate(nextDate.getDate() + 7); // Próxima semana

            const zoomLink = `https://zoom.us/j/123456789?pwd=abc123`; // Link simulado

            mentoria.nextSession = `${nextDate.toLocaleDateString()} a las 8:00 PM`;
            mentoria.zoomLinks.push({
                date: nextDate.toISOString(),
                link: zoomLink,
                sessionNumber: (mentoria.sessionsCompleted || 0) + 1
            });

            user.mentoria = mentoria;
            localStorage.setItem('germayori_user', JSON.stringify(user));

            alert(`📅 ¡Sesión agendada!\n\n🗓️ Fecha: ${mentoria.nextSession}\n🔗 Link de Zoom: ${zoomLink}\n\n📧 También recibirás un email de confirmación.`);

            // Notificar al mentor
            notifyMentorSessionScheduled();

            location.reload();
        }

        // Notificar al mentor sobre sesión agendada
        function notifyMentorSessionScheduled() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');

            console.log('📅 NOTIFICACIÓN PARA jhon0608:');
            console.log(`✅ Sesión agendada con ${user.name}`);
            console.log(`📧 Email: ${user.email}`);
            console.log(`🗓️ Fecha: ${user.mentoria.nextSession}`);
            console.log(`🔢 Sesión número: ${(user.mentoria.sessionsCompleted || 0) + 1}/3`);
        }

        // Simular completar sesión (esto lo haría jhon0608 después de cada mentoría)
        function completeSession() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const mentoria = user.mentoria || {};

            mentoria.sessionsCompleted = (mentoria.sessionsCompleted || 0) + 1;
            mentoria.sessionsRemaining = 3 - mentoria.sessionsCompleted;
            mentoria.nextSession = null;

            // Si completó las 3 sesiones, bloquear acceso
            if (mentoria.sessionsCompleted >= 3) {
                mentoria.hasAccess = false;
                mentoria.completedDate = new Date().toISOString();
            }

            user.mentoria = mentoria;
            localStorage.setItem('germayori_user', JSON.stringify(user));

            console.log(`✅ Sesión ${mentoria.sessionsCompleted} completada para ${user.name}`);
        }

        // Inicializar
        window.onload = function() {
            checkAuth();
            loadUserInfo();
            checkMentoriaAccess();
        };
    </script>
</body>
</html>
