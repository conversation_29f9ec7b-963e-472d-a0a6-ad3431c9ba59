# 📚 MANUAL COMPLETO - GERMAYORI AWS & BASE DE DATOS

## 🎯 **ACCESOS DIRECTOS**

### 🗄️ **VER BASE DE DATOS:**
```
http://localhost:3000/admin-database.html
```

### ☁️ **GESTIÓN AWS (Enviar Señales):**
```
http://localhost:3000/aws-management.html
```

### 🌐 **CONSOLA AWS:**
```
https://082565699654.signin.aws.amazon.com/console
Usuario: germayori08
Contraseña: djD47R&1
```

---

## 🗄️ **1. GESTIÓN DE BASE DE DATOS**

### **Ver Todos los Usuarios Registrados:**
- **URL:** http://localhost:3000/admin-database.html
- **Funciones:**
  - ✅ Ver todos los usuarios registrados
  - ✅ Ver tipo de usuario (admin/user)
  - ✅ Ver fecha de registro
  - ✅ Estadísticas en tiempo real

### **API para Usuarios:**
```bash
# Obtener todos los usuarios
curl http://localhost:3000/api/users

# Registrar nuevo usuario
curl -X POST http://localhost:3000/api/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Nuevo Usuario","email":"<EMAIL>","password":"123456"}'
```

### **Ver Todas las Señales:**
- **Señales activas e historial completo**
- **Filtros por estado, usuario, fecha**
- **Estadísticas de rendimiento**

---

## ☁️ **2. GESTIÓN AWS - ENVÍO DE SEÑALES**

### **Panel de Control AWS:**
- **URL:** http://localhost:3000/aws-management.html
- **Funciones:**
  - 📤 Enviar señales desde AWS al canal
  - 📊 Ver estadísticas en tiempo real
  - 📋 Ver señales recientes
  - 🔄 Monitoreo automático

### **Cómo Enviar Señales desde AWS:**

#### **Método 1: Panel Web (Recomendado)**
1. Ir a: http://localhost:3000/aws-management.html
2. Llenar el formulario:
   - **Par:** EUR/USD, GBP/USD, etc.
   - **Tipo:** BUY o SELL
   - **Precio Entrada:** 1.08500
   - **Stop Loss:** 1.08000
   - **Take Profit:** 1.09500
   - **Análisis:** Descripción técnica
3. Hacer clic en "📤 Enviar Señal a Canal"
4. ✅ La señal aparece automáticamente en el canal

#### **Método 2: API Directa**
```bash
curl -X POST http://localhost:3000/api/aws/signals \
  -H "Content-Type: application/json" \
  -d '{
    "pair": "EURUSD",
    "type": "BUY",
    "entry_price": 1.08500,
    "stop_loss": 1.08000,
    "take_profit": 1.09500,
    "analysis": "Ruptura de resistencia clave",
    "timeframe": "H4"
  }'
```

### **Verificar Estado AWS:**
```bash
curl http://localhost:3000/api/aws/status
```

---

## 🔑 **3. CREDENCIALES Y ACCESOS**

### **AWS Console:**
- **URL:** https://082565699654.signin.aws.amazon.com/console
- **Usuario:** germayori08
- **Contraseña:** djD47R&1
- **Región:** us-east-1

### **Usuario Admin Local:**
- **Email:** <EMAIL>
- **Contraseña:** admin123

### **Servidor Local:**
- **URL:** http://localhost:3000
- **API Base:** http://localhost:3000/api/

---

## 📊 **4. APIS DISPONIBLES**

### **Autenticación:**
```bash
# Login
POST /api/login
{"email": "<EMAIL>", "password": "admin123"}

# Registro
POST /api/register
{"name": "Usuario", "email": "<EMAIL>", "password": "123456"}
```

### **Usuarios:**
```bash
# Ver todos los usuarios
GET /api/users

# Respuesta:
[
  {
    "id": 1,
    "name": "GERMAYORI Admin",
    "email": "<EMAIL>",
    "type": "admin",
    "created_at": "2024-01-01T00:00:00.000Z"
  }
]
```

### **Señales:**
```bash
# Ver todas las señales
GET /api/signals

# Crear señal
POST /api/signals
{
  "user_id": 1,
  "pair": "EURUSD",
  "type": "BUY",
  "entry_price": 1.08500,
  "stop_loss": 1.08000,
  "take_profit": 1.09500,
  "analysis": "Análisis técnico..."
}

# Crear señal desde AWS
POST /api/aws/signals
{...mismos datos...}
```

### **MyFXBook:**
```bash
# Portfolio data
GET /api/myfxbook/portfolio

# Balance en tiempo real
GET /api/myfxbook/balance
```

### **Noticias Forex:**
```bash
# Noticias en tiempo real
GET /api/forex/news
```

---

## 🎯 **5. FLUJO DE TRABAJO COMPLETO**

### **Para Ver Base de Datos:**
1. Abrir: http://localhost:3000/admin-database.html
2. Ver usuarios registrados en tiempo real
3. Ver todas las señales enviadas
4. Monitorear estadísticas

### **Para Enviar Señales desde AWS:**
1. Abrir: http://localhost:3000/aws-management.html
2. Llenar formulario de señal
3. Enviar al canal
4. Verificar que aparece en la base de datos
5. Los usuarios ven la señal automáticamente

### **Para Gestionar AWS:**
1. Acceder a consola: https://082565699654.signin.aws.amazon.com/console
2. Usuario: germayori08 / djD47R&1
3. Configurar recursos (DynamoDB, S3, Lambda)
4. Usar APIs para integración

---

## 🔧 **6. CONFIGURACIÓN AVANZADA**

### **Cuando tengas más espacio en disco:**
```bash
# Instalar dependencias completas
cd backend
npm install aws-sdk sqlite3 multer multer-s3 uuid

# Cambiar a servidor completo
node server.js  # En lugar de server-simple.js
```

### **Configurar AWS Real:**
```env
# Editar backend/.env
AWS_ACCESS_KEY_ID=tu_access_key_real
AWS_SECRET_ACCESS_KEY=tu_secret_key_real
AWS_REGION=us-east-1
```

---

## 📱 **7. MONITOREO EN TIEMPO REAL**

### **Estadísticas Automáticas:**
- 👥 Total usuarios registrados
- 📈 Total señales enviadas
- 🟢 Señales activas
- ☁️ Señales desde AWS
- 📊 Rendimiento del portfolio

### **Actualizaciones Automáticas:**
- ✅ Base de datos se actualiza en tiempo real
- ✅ Estadísticas se refrescan cada 30 segundos
- ✅ Señales aparecen inmediatamente en el canal
- ✅ Usuarios ven cambios sin recargar página

---

## 🚨 **8. SOLUCIÓN DE PROBLEMAS**

### **Si no funciona algo:**
1. Verificar que el servidor esté corriendo: http://localhost:3000
2. Revisar logs en la consola del servidor
3. Verificar conexión a internet
4. Comprobar credenciales AWS

### **Reiniciar Servidor:**
```bash
# Detener servidor (Ctrl+C)
# Reiniciar
node backend/server-simple.js
```

---

## 🎉 **¡TODO ESTÁ LISTO!**

**Tienes acceso completo a:**
- ✅ Base de datos en tiempo real
- ✅ Panel de gestión AWS
- ✅ Envío de señales al canal
- ✅ Monitoreo de usuarios
- ✅ APIs completas
- ✅ Integración MyFXBook
- ✅ Sistema de noticias

**¡GERMAYORI está completamente funcional! 🚀**
