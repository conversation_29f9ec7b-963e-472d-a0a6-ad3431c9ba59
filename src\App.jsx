import React, { useState } from 'react';
import axios from 'axios';

// Componente de Chat Simple
const ChatEducativo = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage = { text: inputMessage, sender: 'user' };
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);

    try {
      const response = await axios.post('http://localhost:3001/api/chat', {
        message: inputMessage
      });

      const botMessage = {
        text: response.data.response,
        sender: 'bot',
        source: response.data.source
      };
      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      const errorMessage = {
        text: 'Lo siento, hay un problema de conexión. Intenta de nuevo.',
        sender: 'bot'
      };
      setMessages(prev => [...prev, errorMessage]);
    }

    setLoading(false);
  };

  return (
    <div className="flex flex-col h-96 border rounded-lg bg-white">
      <div className="bg-blue-600 text-white p-4 rounded-t-lg">
        <h3 className="font-bold">GERMAYORI AI - Chat Educativo</h3>
      </div>

      <div className="flex-1 p-4 overflow-y-auto space-y-2">
        {messages.map((msg, index) => (
          <div key={index} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-xs p-2 rounded-lg ${
              msg.sender === 'user'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-800'
            }`}>
              {msg.text}
            </div>
          </div>
        ))}
        {loading && (
          <div className="flex justify-start">
            <div className="bg-gray-200 text-gray-800 p-2 rounded-lg">
              Escribiendo...
            </div>
          </div>
        )}
      </div>

      <div className="p-4 border-t flex gap-2">
        <input
          type="text"
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          placeholder="Pregunta sobre forex..."
          className="flex-1 p-2 border rounded"
        />
        <button
          onClick={sendMessage}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Enviar
        </button>
      </div>
    </div>
  );
};

// Componente de Calculadora de Pips
const CalculadoraPips = () => {
  const [lotSize, setLotSize] = useState('');
  const [pips, setPips] = useState('');
  const [result, setResult] = useState(null);

  const calculatePips = () => {
    if (lotSize && pips) {
      const profit = parseFloat(lotSize) * parseFloat(pips) * 10;
      setResult(profit);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h3 className="text-xl font-bold mb-4">Calculadora de Pips</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Tamaño del Lote</label>
          <input
            type="number"
            value={lotSize}
            onChange={(e) => setLotSize(e.target.value)}
            placeholder="0.01"
            className="w-full p-2 border rounded"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Pips</label>
          <input
            type="number"
            value={pips}
            onChange={(e) => setPips(e.target.value)}
            placeholder="10"
            className="w-full p-2 border rounded"
          />
        </div>
        <button
          onClick={calculatePips}
          className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700"
        >
          Calcular
        </button>
        {result !== null && (
          <div className="mt-4 p-4 bg-green-100 rounded">
            <p className="text-green-800 font-bold">
              Ganancia/Pérdida: ${result.toFixed(2)} USD
            </p>
          </div>
        )}
        <div className="mt-4 text-sm text-gray-600">
          <p>💡 <strong>Tip:</strong> Usa la calculadora de MyFXBook para cálculos más avanzados:</p>
          <a
            href="https://www.myfxbook.com/forex-calculators/profit-calculator"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            🔗 MyFXBook Calculadora
          </a>
        </div>
      </div>
    </div>
  );
};

// Main App Component
function App() {
  const [activeTab, setActiveTab] = useState('chat');

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-blue-800 text-white p-4">
        <div className="container mx-auto">
          <h1 className="text-2xl font-bold">🚀 GERMAYORI - Trading Educativo</h1>
          <p className="text-blue-200">Tu plataforma de aprendizaje forex</p>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-blue-700 text-white p-2">
        <div className="container mx-auto">
          <div className="flex space-x-4">
            <button
              onClick={() => setActiveTab('chat')}
              className={`px-4 py-2 rounded ${activeTab === 'chat' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
            >
              💬 Chat Educativo
            </button>
            <button
              onClick={() => setActiveTab('calculadora')}
              className={`px-4 py-2 rounded ${activeTab === 'calculadora' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
            >
              🧮 Calculadora
            </button>
            <button
              onClick={() => setActiveTab('recursos')}
              className={`px-4 py-2 rounded ${activeTab === 'recursos' ? 'bg-blue-600' : 'hover:bg-blue-600'}`}
            >
              📚 Recursos
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container mx-auto p-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Panel */}
          <div>
            {activeTab === 'chat' && <ChatEducativo />}
            {activeTab === 'calculadora' && <CalculadoraPips />}
            {activeTab === 'recursos' && (
              <div className="bg-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-bold mb-4">📚 Recursos Recomendados</h3>
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-semibold">🧮 Calculadora de Profit</h4>
                    <p className="text-gray-600 mb-2">Calcula ganancias y pérdidas con precisión</p>
                    <a
                      href="https://www.myfxbook.com/forex-calculators/profit-calculator"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      🔗 Ir a MyFXBook Calculadora
                    </a>
                  </div>

                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-semibold">📅 Calendario Económico</h4>
                    <p className="text-gray-600 mb-2">Mantente al día con eventos económicos importantes</p>
                    <a
                      href="https://www.myfxbook.com/forex-economic-calendar"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      🔗 Ir a MyFXBook Calendario
                    </a>
                  </div>

                  <div className="border-l-4 border-yellow-500 pl-4">
                    <h4 className="font-semibold">💡 Consejos de Trading</h4>
                    <ul className="text-gray-600 space-y-1">
                      <li>• Nunca arriesgues más del 2% de tu capital</li>
                      <li>• Usa siempre stop loss</li>
                      <li>• Practica con cuenta demo primero</li>
                      <li>• Estudia análisis técnico y fundamental</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Panel - Status */}
          <div>
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-bold mb-4">📊 Estado del Sistema</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span>Backend API</span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">✅ Activo</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Chat Educativo</span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">✅ Funcionando</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Calculadora</span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">✅ Disponible</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded">
                <h4 className="font-semibold text-blue-800 mb-2">🎯 Bienvenido a GERMAYORI</h4>
                <p className="text-blue-700 text-sm">
                  Plataforma educativa de trading forex. Aprende, practica y mejora tus habilidades de trading.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white p-4 mt-8">
        <div className="container mx-auto text-center">
          <p>&copy; 2024 GERMAYORI - Trading Educativo. Todos los derechos reservados.</p>
          <p className="text-gray-400 text-sm mt-1">
            Recuerda: El trading conlleva riesgos. Nunca inviertas más de lo que puedes permitirte perder.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
