<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canal Análisis - Certificaciones | GERMAYORI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .main-content {
            background: rgba(30, 60, 114, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar {
            background: rgba(20, 40, 80, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }
        .channel-item {
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }
        .channel-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
            transform: translateX(5px);
        }
        .channel-active {
            background: rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.5);
        }
        .analysis-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .analysis-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: rgba(0, 212, 255, 0.5);
            background: rgba(0, 212, 255, 0.1);
        }
        .status-pending { color: #fbbf24; }
        .status-approved { color: #10b981; }
        .status-rejected { color: #ef4444; }
    </style>
</head>
<body class="min-h-screen">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-80 sidebar flex flex-col">
            <!-- User Profile -->
            <div class="p-6 border-b border-white/10">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="trading-vivo.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-green-400"></i>
                    <span>Trading en Vivo</span>
                </a>

                <a href="videos-educativos.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-signal mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="canal-analisis.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-bar mr-3 text-pink-400"></i>
                    <span>Análisis y Certificaciones</span>
                </a>

                <a href="canal-zoom-enlaces.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-video mr-3 text-orange-400"></i>
                    <span>Enlaces Zoom</span>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>

            <!-- Logout -->
            <div class="mt-4">
                <button onclick="logout()" class="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>Cerrar Sesión
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 main-content m-4 rounded-lg overflow-y-auto">
            
            <!-- Header -->
            <div class="bg-gradient-to-r from-pink-500 to-purple-600 text-white p-6 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-3xl font-bold mb-2">📊 Canal de Análisis y Certificaciones</h2>
                        <p class="text-pink-100">Sube tus análisis y recibe certificaciones de jhon0608</p>
                    </div>
                    <div class="text-right">
                        <div class="text-pink-100 text-sm">Certificaciones GERMAYORI</div>
                        <div class="text-2xl">🏆</div>
                    </div>
                </div>
            </div>

            <!-- Upload Section -->
            <div class="p-6 mb-6">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6">
                    <h3 class="text-2xl font-bold text-white mb-4">
                        <i class="fas fa-upload mr-3"></i>Subir Nuevo Análisis
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Upload Area -->
                        <div class="upload-area rounded-lg p-6 text-center cursor-pointer" onclick="document.getElementById('file-input').click()">
                            <div class="text-white">
                                <i class="fas fa-cloud-upload-alt text-4xl mb-4"></i>
                                <p class="text-lg font-semibold mb-2">Subir Imagen del Análisis</p>
                                <p class="text-sm text-gray-300">Arrastra tu imagen aquí o haz clic para seleccionar</p>
                                <p class="text-xs text-gray-400 mt-2">Formatos: JPG, PNG, GIF (Max: 5MB)</p>
                            </div>
                            <input type="file" id="file-input" accept="image/*" class="hidden" onchange="handleFileSelect(event)">
                        </div>

                        <!-- Analysis Form -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-white font-semibold mb-2">Par de Divisas</label>
                                <select id="currency-pair" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white">
                                    <option value="">Seleccionar par</option>
                                    <option value="EUR/USD">EUR/USD</option>
                                    <option value="GBP/USD">GBP/USD</option>
                                    <option value="USD/JPY">USD/JPY</option>
                                    <option value="AUD/USD">AUD/USD</option>
                                    <option value="USD/CAD">USD/CAD</option>
                                    <option value="USD/CHF">USD/CHF</option>
                                    <option value="NZD/USD">NZD/USD</option>
                                    <option value="XAU/USD">XAU/USD (Oro)</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-white font-semibold mb-2">Temporalidad</label>
                                <select id="timeframe" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white">
                                    <option value="">Seleccionar temporalidad</option>
                                    <option value="M1">M1 (1 minuto)</option>
                                    <option value="M5">M5 (5 minutos)</option>
                                    <option value="M15">M15 (15 minutos)</option>
                                    <option value="M30">M30 (30 minutos)</option>
                                    <option value="H1">H1 (1 hora)</option>
                                    <option value="H4">H4 (4 horas)</option>
                                    <option value="D1">D1 (Diario)</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-white font-semibold mb-2">Tipo de Análisis</label>
                                <select id="analysis-type" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white">
                                    <option value="">Seleccionar tipo</option>
                                    <option value="FVG">Fair Value Gap (FVG)</option>
                                    <option value="Order Block">Order Block</option>
                                    <option value="BOS">Break of Structure</option>
                                    <option value="Liquidez">Análisis de Liquidez</option>
                                    <option value="Estructura">Estructura de Mercado</option>
                                    <option value="Completo">Análisis Completo GERMAYORI</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-white font-semibold mb-2">Descripción del Análisis</label>
                                <textarea id="analysis-description" 
                                          placeholder="Describe tu análisis: ¿Qué identificaste? ¿Cuál es tu plan de entrada? ¿Dónde colocarías SL y TP?"
                                          class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 h-24"></textarea>
                            </div>

                            <button onclick="submitAnalysis()" 
                                    class="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-bold">
                                <i class="fas fa-paper-plane mr-2"></i>Enviar para Certificación
                            </button>
                        </div>
                    </div>

                    <!-- Preview Area -->
                    <div id="image-preview" class="mt-6 hidden">
                        <h4 class="text-white font-semibold mb-3">Vista Previa:</h4>
                        <div class="bg-white/10 rounded-lg p-4">
                            <img id="preview-image" src="" alt="Preview" class="max-w-full h-auto rounded-lg">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Panel (Solo para jhon0608) -->
            <div id="admin-panel" class="p-6 bg-gradient-to-r from-purple-600 to-indigo-600 mb-6 hidden">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-crown mr-3"></i>Panel de Certificación - jhon0608
                </h3>
                
                <div class="bg-white bg-opacity-20 rounded-lg p-6">
                    <h4 class="text-xl font-bold text-white mb-4">📋 Análisis Pendientes de Certificación</h4>
                    <div id="pending-analysis" class="space-y-4">
                        <!-- Se llenarán dinámicamente -->
                    </div>
                </div>
            </div>

            <!-- My Analysis Section -->
            <div class="px-6 mb-6">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-history mr-3 text-pink-400"></i>Mis Análisis
                </h3>
                <div id="my-analysis-container" class="space-y-4">
                    <!-- Se llenarán dinámicamente -->
                </div>
            </div>

            <!-- All Analysis Section -->
            <div class="px-6 mb-6">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-chart-bar mr-3 text-purple-400"></i>Análisis de la Comunidad
                </h3>
                <div id="community-analysis-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Se llenarán dinámicamente -->
                </div>
            </div>

        </div>
    </div>

    <script>
        // Variables globales
        let currentUser = null;
        let selectedFile = null;

        // Análisis almacenados (simulado - en producción sería base de datos)
        let analysisDatabase = [
            {
                id: 1,
                userId: '<EMAIL>',
                userName: 'Usuario Demo',
                pair: 'EUR/USD',
                timeframe: 'M5',
                type: 'FVG',
                description: 'Identifico FVG en 1.0850 tras BOS alcista. Entrada en retroceso.',
                imageUrl: 'https://via.placeholder.com/400x300/1e3c72/ffffff?text=EUR/USD+FVG+Analysis',
                status: 'approved',
                certification: 'Excelente identificación de FVG. Análisis correcto según estrategia GERMAYORI.',
                certifiedBy: 'jhon0608',
                submittedAt: '2024-01-15T10:30:00',
                certifiedAt: '2024-01-15T14:20:00'
            }
        ];

        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }

                // Mostrar panel admin solo para jhon0608
                if (user.name === 'Admin GERMAYORI' || user.email === '<EMAIL>') {
                    document.getElementById('admin-panel').classList.remove('hidden');
                    loadPendingAnalysis();
                }
            }
        }

        // Manejar selección de archivo
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 5 * 1024 * 1024) {
                    alert('El archivo es demasiado grande. Máximo 5MB.');
                    return;
                }

                selectedFile = file;
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-image').src = e.target.result;
                    document.getElementById('image-preview').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        // Enviar análisis
        function submitAnalysis() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            
            if (!selectedFile) {
                alert('Por favor selecciona una imagen del análisis');
                return;
            }

            const pair = document.getElementById('currency-pair').value;
            const timeframe = document.getElementById('timeframe').value;
            const type = document.getElementById('analysis-type').value;
            const description = document.getElementById('analysis-description').value;

            if (!pair || !timeframe || !type || !description) {
                alert('Por favor completa todos los campos');
                return;
            }

            // Simular subida de imagen (en producción sería upload real)
            const imageUrl = URL.createObjectURL(selectedFile);

            const newAnalysis = {
                id: Date.now(),
                userId: user.email,
                userName: user.name,
                pair,
                timeframe,
                type,
                description,
                imageUrl,
                status: 'pending',
                certification: null,
                certifiedBy: null,
                submittedAt: new Date().toISOString(),
                certifiedAt: null
            };

            analysisDatabase.push(newAnalysis);
            
            // Limpiar formulario
            document.getElementById('currency-pair').value = '';
            document.getElementById('timeframe').value = '';
            document.getElementById('analysis-type').value = '';
            document.getElementById('analysis-description').value = '';
            document.getElementById('file-input').value = '';
            document.getElementById('image-preview').classList.add('hidden');
            selectedFile = null;

            alert('Análisis enviado exitosamente. Recibirás la certificación en tu dashboard.');
            
            // Recargar análisis
            loadMyAnalysis();
            loadCommunityAnalysis();
            if (user.name === 'Admin GERMAYORI') {
                loadPendingAnalysis();
            }
        }

        // Cargar análisis pendientes (solo admin)
        function loadPendingAnalysis() {
            const container = document.getElementById('pending-analysis');
            const pending = analysisDatabase.filter(a => a.status === 'pending');

            if (pending.length === 0) {
                container.innerHTML = '<p class="text-white">No hay análisis pendientes de certificación.</p>';
                return;
            }

            container.innerHTML = pending.map(analysis => `
                <div class="analysis-card rounded-lg p-4">
                    <div class="flex items-start space-x-4">
                        <img src="${analysis.imageUrl}" alt="Análisis" class="w-24 h-24 object-cover rounded-lg">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="text-white font-bold">${analysis.userName}</h5>
                                <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full">PENDIENTE</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-2">${analysis.pair} - ${analysis.timeframe} - ${analysis.type}</p>
                            <p class="text-gray-300 text-sm mb-3">${analysis.description}</p>
                            <div class="flex space-x-2">
                                <button onclick="certifyAnalysis(${analysis.id}, 'approved')" 
                                        class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                    ✅ Aprobar
                                </button>
                                <button onclick="certifyAnalysis(${analysis.id}, 'rejected')" 
                                        class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                    ❌ Rechazar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Certificar análisis (solo admin)
        function certifyAnalysis(analysisId, status) {
            const certification = prompt(status === 'approved' ? 
                'Escribe la certificación positiva:' : 
                'Escribe los comentarios de mejora:');
            
            if (!certification) return;

            const analysis = analysisDatabase.find(a => a.id === analysisId);
            if (analysis) {
                analysis.status = status;
                analysis.certification = certification;
                analysis.certifiedBy = 'jhon0608';
                analysis.certifiedAt = new Date().toISOString();

                // Simular notificación al dashboard del usuario
                addCertificationToUserDashboard(analysis);

                alert(`Análisis ${status === 'approved' ? 'aprobado' : 'rechazado'} exitosamente`);
                
                loadPendingAnalysis();
                loadCommunityAnalysis();
            }
        }

        // Agregar certificación al dashboard del usuario
        function addCertificationToUserDashboard(analysis) {
            // En producción esto se haría via API/base de datos
            const notifications = JSON.parse(localStorage.getItem('user_notifications') || '[]');
            notifications.push({
                id: Date.now(),
                type: 'certification',
                title: `Certificación ${analysis.status === 'approved' ? 'Aprobada' : 'Rechazada'}`,
                message: `Tu análisis de ${analysis.pair} ha sido ${analysis.status === 'approved' ? 'aprobado' : 'rechazado'}: ${analysis.certification}`,
                timestamp: new Date().toISOString(),
                read: false
            });
            localStorage.setItem('user_notifications', JSON.stringify(notifications));
        }

        // Cargar mis análisis
        function loadMyAnalysis() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const container = document.getElementById('my-analysis-container');
            const myAnalysis = analysisDatabase.filter(a => a.userId === user.email);

            if (myAnalysis.length === 0) {
                container.innerHTML = `
                    <div class="analysis-card rounded-lg p-6 text-center">
                        <div class="text-gray-400">
                            <i class="fas fa-chart-bar text-4xl mb-4"></i>
                            <p>Aún no has subido ningún análisis</p>
                            <p class="text-sm mt-2">¡Sube tu primer análisis y recibe certificación de jhon0608!</p>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = myAnalysis.map(analysis => `
                <div class="analysis-card rounded-lg p-4">
                    <div class="flex items-start space-x-4">
                        <img src="${analysis.imageUrl}" alt="Análisis" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="text-white font-bold">${analysis.pair} - ${analysis.timeframe}</h5>
                                <span class="text-xs px-2 py-1 rounded-full ${
                                    analysis.status === 'pending' ? 'bg-yellow-500 text-black' :
                                    analysis.status === 'approved' ? 'bg-green-500 text-white' :
                                    'bg-red-500 text-white'
                                }">
                                    ${analysis.status === 'pending' ? 'PENDIENTE' :
                                      analysis.status === 'approved' ? 'APROBADO' : 'RECHAZADO'}
                                </span>
                            </div>
                            <p class="text-gray-300 text-sm mb-2">${analysis.type}</p>
                            <p class="text-gray-300 text-sm mb-2">${analysis.description}</p>
                            ${analysis.certification ? `
                                <div class="bg-white/10 rounded-lg p-3 mt-2">
                                    <p class="text-white text-sm font-semibold">Certificación de jhon0608:</p>
                                    <p class="text-gray-300 text-sm">${analysis.certification}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Cargar análisis de la comunidad
        function loadCommunityAnalysis() {
            const container = document.getElementById('community-analysis-container');
            const approved = analysisDatabase.filter(a => a.status === 'approved');

            if (approved.length === 0) {
                container.innerHTML = `
                    <div class="analysis-card rounded-lg p-6 text-center col-span-full">
                        <div class="text-gray-400">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p>No hay análisis certificados aún</p>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = approved.map(analysis => `
                <div class="analysis-card rounded-lg p-4">
                    <img src="${analysis.imageUrl}" alt="Análisis" class="w-full h-32 object-cover rounded-lg mb-3">
                    <div class="flex items-center justify-between mb-2">
                        <h5 class="text-white font-bold text-sm">${analysis.pair}</h5>
                        <span class="text-xs bg-green-500 text-white px-2 py-1 rounded-full">✅ CERTIFICADO</span>
                    </div>
                    <p class="text-gray-300 text-xs mb-2">Por: ${analysis.userName}</p>
                    <p class="text-gray-300 text-xs mb-2">${analysis.timeframe} - ${analysis.type}</p>
                    <div class="bg-green-500/20 rounded-lg p-2">
                        <p class="text-green-300 text-xs font-semibold">Certificación jhon0608:</p>
                        <p class="text-green-200 text-xs">${analysis.certification}</p>
                    </div>
                </div>
            `).join('');
        }

        // Función para cerrar sesión
        function logout() {
            localStorage.removeItem('germayori_user');
            localStorage.removeItem('germayori_selected_plan');
            localStorage.removeItem('germayori_token');
            alert('Sesión cerrada exitosamente');
            window.location.href = 'index.html';
        }

        // Inicializar página
        window.onload = function() {
            const user = checkAuth();
            if (user) {
                currentUser = user;
                loadUserInfo();
                loadMyAnalysis();
                loadCommunityAnalysis();
            }
        };
    </script>
</body>
</html>
