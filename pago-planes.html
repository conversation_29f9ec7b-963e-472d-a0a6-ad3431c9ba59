<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pago de Plan - GERMAYORI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
        }
        .payment-method {
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .payment-active {
            border: 2px solid #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">

    <div class="w-full max-w-4xl">
        <div class="glass rounded-2xl p-8 glow">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="floating">
                    <h1 class="text-4xl font-black text-white mb-2">
                        <i class="fas fa-rocket mr-3 text-cyan-400"></i>GERMAYORI
                    </h1>
                </div>
                <h2 class="text-2xl font-bold text-white mb-2">💳 Completar Pago</h2>
                <div id="plan-info" class="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
                    <div class="text-white">
                        <span class="text-lg font-bold" id="selected-plan-name">Plan Seleccionado</span>
                        <span class="text-cyan-400 text-2xl font-bold ml-4" id="selected-plan-price">$0/mes</span>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                
                <!-- Yappy Payment -->
                <div class="payment-method glass rounded-lg p-6 cursor-pointer" onclick="selectPaymentMethod('yappy')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">📱</div>
                        <h3 class="text-xl font-bold text-white mb-2">Pago con Yappy</h3>
                        <p class="text-gray-300 text-sm">Pago instantáneo desde tu móvil</p>
                    </div>
                </div>

                <!-- Bank Transfer -->
                <div class="payment-method glass rounded-lg p-6 cursor-pointer" onclick="selectPaymentMethod('banistmo')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">🏦</div>
                        <h3 class="text-xl font-bold text-white mb-2">Transferencia Banistmo</h3>
                        <p class="text-gray-300 text-sm">QR para transferencia bancaria</p>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div id="payment-details" class="hidden">
                
                <!-- Yappy Payment Details -->
                <div id="yappy-details" class="hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 mb-6">
                        <h3 class="text-2xl font-bold text-white mb-4 text-center">
                            📱 Pago con Yappy
                        </h3>
                        
                        <div class="flex flex-col md:flex-row items-center justify-center gap-8">
                            <!-- QR Code -->
                            <div class="text-center">
                                <div class="bg-white rounded-lg p-4 mb-4">
                                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=Omar%20Sosa%20-%20GERMAYORI%20Plan&bgcolor=FFFFFF&color=000000&margin=10"
                                         alt="QR Yappy Omar Sosa"
                                         class="w-64 h-64 mx-auto">
                                </div>
                                <div class="text-white font-bold">📱 QR YAPPY</div>
                                <div class="text-white text-sm">Usuario: Omar Sosa</div>
                            </div>

                            <!-- Instructions -->
                            <div class="text-white max-w-md">
                                <h4 class="text-lg font-bold mb-4">📋 Instrucciones:</h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex items-center">
                                        <span class="bg-cyan-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3">1</span>
                                        Abre tu app Yappy
                                    </div>
                                    <div class="flex items-center">
                                        <span class="bg-cyan-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3">2</span>
                                        Escanea este QR o busca: Omar Sosa
                                    </div>
                                    <div class="flex items-center">
                                        <span class="bg-cyan-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3">3</span>
                                        Ingresa el monto: <span id="yappy-amount" class="font-bold text-cyan-300">$0</span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="bg-cyan-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3">4</span>
                                        Confirma el pago
                                    </div>
                                    <div class="flex items-center">
                                        <span class="bg-cyan-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3">5</span>
                                        Toma captura del comprobante
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Banistmo Payment Details -->
                <div id="banistmo-details" class="hidden">
                    <div class="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-6 mb-6">
                        <h3 class="text-2xl font-bold text-white mb-4 text-center">
                            🏦 Transferencia Banistmo
                        </h3>
                        
                        <div class="flex flex-col md:flex-row items-center justify-center gap-8">
                            <!-- QR Code -->
                            <div class="text-center">
                                <div class="bg-white rounded-lg p-4 mb-4">
                                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=Banistmo%20-%20Omar%20Sosa%20-%20GERMAYORI&bgcolor=FFFFFF&color=000000&margin=10"
                                         alt="QR Banistmo Omar Sosa"
                                         class="w-64 h-64 mx-auto">
                                </div>
                                <div class="text-white font-bold">🏦 QR BANISTMO</div>
                                <div class="text-white text-sm">Cuenta: Omar Sosa</div>
                            </div>

                            <!-- Bank Details -->
                            <div class="text-white max-w-md">
                                <h4 class="text-lg font-bold mb-4">🏦 Datos Bancarios:</h4>
                                <div class="bg-white bg-opacity-20 rounded-lg p-4 space-y-2 text-sm">
                                    <div><strong>Banco:</strong> Banistmo</div>
                                    <div><strong>Titular:</strong> Omar Sosa</div>
                                    <div><strong>Tipo:</strong> Cuenta de Ahorros</div>
                                    <div><strong>Monto:</strong> <span id="banistmo-amount" class="font-bold text-green-300">$0</span></div>
                                </div>
                                
                                <div class="mt-4 text-sm">
                                    <div class="font-bold mb-2">📋 Instrucciones:</div>
                                    <div>1. Escanea el QR con tu app Banistmo</div>
                                    <div>2. O realiza transferencia manual</div>
                                    <div>3. Ingresa el monto exacto</div>
                                    <div>4. Toma captura del comprobante</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Proof Section -->
                <div class="bg-white bg-opacity-10 rounded-lg p-6 mb-6">
                    <h4 class="text-xl font-bold text-white mb-4 text-center">
                        📸 Subir Comprobante de Pago
                    </h4>
                    
                    <div class="max-w-md mx-auto">
                        <div class="border-2 border-dashed border-white border-opacity-30 rounded-lg p-6 text-center cursor-pointer hover:border-cyan-400 transition-colors"
                             onclick="document.getElementById('proof-input').click()">
                            <i class="fas fa-cloud-upload-alt text-4xl text-white mb-4"></i>
                            <p class="text-white mb-2">Subir comprobante de pago</p>
                            <p class="text-gray-300 text-sm">JPG, PNG (Max: 5MB)</p>
                            <input type="file" id="proof-input" accept="image/*" class="hidden" onchange="handleProofUpload(event)">
                        </div>
                        
                        <div id="proof-preview" class="mt-4 hidden">
                            <img id="proof-image" src="" alt="Comprobante" class="w-full rounded-lg">
                        </div>
                        
                        <button onclick="submitPayment()" 
                                class="w-full mt-4 bg-green-600 hover:bg-green-700 text-white font-bold py-3 rounded-lg transition-colors">
                            <i class="fas fa-check mr-2"></i>Confirmar Pago y Activar Plan
                        </button>
                    </div>
                </div>
            </div>

            <!-- Back to Login -->
            <div class="text-center">
                <button onclick="window.location.href='index.html'" 
                        class="text-cyan-400 hover:text-cyan-300 font-medium">
                    ← Volver al Login
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedPayment = null;
        let selectedPlan = null;
        let planPrice = 0;

        // Cargar información del plan seleccionado
        window.onload = function() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const planType = localStorage.getItem('germayori_selected_plan');
            
            if (!planType || !user.plan) {
                alert('No se encontró información del plan. Redirigiendo...');
                window.location.href = 'index.html';
                return;
            }

            const plans = {
                'basico': { name: '📊 BÁSICO', price: 45 },
                'intermedio': { name: '🚀 INTERMEDIO', price: 70 },
                'avanzado': { name: '⭐ AVANZADO', price: 95 },
                'completo': { name: '👑 COMPLETO VIP', price: 140 }
            };

            selectedPlan = planType;
            planPrice = plans[planType].price;

            document.getElementById('selected-plan-name').textContent = plans[planType].name;
            document.getElementById('selected-plan-price').textContent = `$${planPrice}/mes`;
        };

        // Seleccionar método de pago
        function selectPaymentMethod(method) {
            selectedPayment = method;
            
            // Remover clase activa de todos
            document.querySelectorAll('.payment-method').forEach(el => {
                el.classList.remove('payment-active');
            });
            
            // Agregar clase activa al seleccionado
            event.currentTarget.classList.add('payment-active');
            
            // Mostrar detalles de pago
            document.getElementById('payment-details').classList.remove('hidden');
            document.getElementById('yappy-details').classList.add('hidden');
            document.getElementById('banistmo-details').classList.add('hidden');
            
            if (method === 'yappy') {
                document.getElementById('yappy-details').classList.remove('hidden');
                document.getElementById('yappy-amount').textContent = `$${planPrice}`;
            } else if (method === 'banistmo') {
                document.getElementById('banistmo-details').classList.remove('hidden');
                document.getElementById('banistmo-amount').textContent = `$${planPrice}`;
            }
        }

        // Manejar subida de comprobante
        function handleProofUpload(event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 5 * 1024 * 1024) {
                    alert('El archivo es demasiado grande. Máximo 5MB.');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('proof-image').src = e.target.result;
                    document.getElementById('proof-preview').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        // Enviar pago
        function submitPayment() {
            if (!selectedPayment) {
                alert('Por favor selecciona un método de pago');
                return;
            }

            const proofInput = document.getElementById('proof-input');
            if (!proofInput.files[0]) {
                alert('Por favor sube el comprobante de pago');
                return;
            }

            // Simular procesamiento de pago
            alert(`¡Pago enviado exitosamente!\n\nPlan: ${document.getElementById('selected-plan-name').textContent}\nMonto: ${document.getElementById('selected-plan-price').textContent}\nMétodo: ${selectedPayment === 'yappy' ? 'Yappy' : 'Banistmo'}\n\nTu plan será activado en las próximas 24 horas.`);
            
            // Actualizar estado del usuario
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            user.paymentStatus = 'pending';
            user.paymentMethod = selectedPayment;
            user.paymentDate = new Date().toISOString();
            localStorage.setItem('germayori_user', JSON.stringify(user));
            
            // Redirigir al dashboard
            window.location.href = 'dashboard-simple.html';
        }
    </script>
</body>
</html>
