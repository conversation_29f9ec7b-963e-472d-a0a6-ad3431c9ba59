<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canal Zoom - Enlaces y Horarios | GERMAYORI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .main-content {
            background: rgba(30, 60, 114, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar {
            background: rgba(20, 40, 80, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }
        .channel-item {
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }
        .channel-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
            transform: translateX(5px);
        }
        .channel-active {
            background: rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.5);
        }
        .zoom-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .zoom-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }
        .live-indicator {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-80 sidebar flex flex-col">
            <!-- User Profile -->
            <div class="p-6 border-b border-white/10">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="trading-vivo.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-green-400"></i>
                    <span>Trading en Vivo</span>
                </a>

                <a href="videos-educativos.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-signal mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="canal-zoom-enlaces.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-video mr-3 text-orange-400"></i>
                    <span>Enlaces Zoom</span>
                    <span class="live-indicator ml-2 text-red-400">🔴</span>
                </a>
            </div>

            <!-- Canales VIP -->
            <div class="space-y-2 mt-6">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">💰 Inversiones VIP</div>

                <a href="canal-inversiones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins mr-3 text-yellow-400"></i>
                            <span>Inversiones</span>
                        </div>
                        <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-bold">VIP</span>
                    </div>
                </a>

                <a href="canal-vip-inversores.html" class="channel-item p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-crown mr-3 text-gold-400"></i>
                            <span>VIP Inversores</span>
                        </div>
                        <span class="text-xs bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full font-bold">ELITE</span>
                    </div>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>

            <!-- Logout -->
            <div class="mt-4">
                <button onclick="logout()" class="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>Cerrar Sesión
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 main-content m-4 rounded-lg overflow-y-auto">

            <!-- Header -->
            <div class="bg-gradient-to-r from-orange-500 to-red-600 text-white p-6 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-3xl font-bold mb-2">📹 Canal Zoom - Enlaces y Horarios</h2>
                        <p class="text-orange-100">Mentorías grupales y sesiones educativas en vivo</p>
                    </div>
                    <div class="text-right">
                        <div class="live-indicator text-red-400 text-2xl">🔴 LIVE</div>
                        <div class="text-orange-100 text-sm">Actualizado en tiempo real</div>
                    </div>
                </div>
            </div>

            <!-- Admin Panel (Solo para jhon0608) -->
            <div id="admin-panel" class="p-6 bg-gradient-to-r from-purple-600 to-indigo-600 mb-6 hidden">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-crown mr-3"></i>Panel de Administración - jhon0608
                </h3>

                <!-- Agregar Nuevo Enlace -->
                <div class="bg-white bg-opacity-20 rounded-lg p-6 mb-4">
                    <h4 class="text-xl font-bold text-white mb-4">➕ Agregar Nuevo Enlace Zoom</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input type="text" id="zoom-title" placeholder="Título de la sesión"
                               class="p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50">
                        <input type="datetime-local" id="zoom-datetime"
                               class="p-3 rounded-lg bg-white/10 border border-white/20 text-white">
                        <input type="url" id="zoom-link" placeholder="https://zoom.us/j/..."
                               class="p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50">
                        <select id="zoom-type" class="p-3 rounded-lg bg-white/10 border border-white/20 text-white">
                            <option value="basico">Básico ($45)</option>
                            <option value="intermedio">Intermedio ($70)</option>
                            <option value="avanzado">Avanzado ($95)</option>
                            <option value="completo">Completo VIP ($140)</option>
                        </select>
                    </div>
                    <textarea id="zoom-description" placeholder="Descripción de la sesión..."
                              class="w-full mt-4 p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 h-24"></textarea>
                    <button onclick="addZoomLink()"
                            class="mt-4 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-bold">
                        <i class="fas fa-plus mr-2"></i>Agregar Enlace
                    </button>
                </div>

                <!-- Horario Semanal -->
                <div class="bg-white bg-opacity-20 rounded-lg p-6">
                    <h4 class="text-xl font-bold text-white mb-4">📅 Actualizar Horario Semanal</h4>
                    <textarea id="weekly-schedule" placeholder="Horario de la semana..."
                              class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 h-32"></textarea>
                    <button onclick="updateSchedule()"
                            class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold">
                        <i class="fas fa-calendar mr-2"></i>Actualizar Horario
                    </button>
                </div>
            </div>

            <!-- Horario Semanal -->
            <div class="p-6 mb-6">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6">
                    <h3 class="text-2xl font-bold text-white mb-4">
                        <i class="fas fa-calendar-week mr-3"></i>Horario Semanal de Mentorías
                    </h3>
                    <div id="weekly-schedule-display" class="text-white">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4">
                                <h4 class="font-bold mb-2">🗓️ Lunes a Viernes</h4>
                                <div class="text-sm space-y-1">
                                    <div>📊 Básico ($45): 7:00 PM - 8:00 PM</div>
                                    <div>🚀 Intermedio ($70): 8:00 PM - 9:00 PM</div>
                                    <div>⭐ Avanzado ($95): 9:00 PM - 10:00 PM</div>
                                </div>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-4">
                                <h4 class="font-bold mb-2">🎯 Sábados</h4>
                                <div class="text-sm space-y-1">
                                    <div>👑 VIP Completo ($140): 2:00 PM - 4:00 PM</div>
                                    <div>📈 Análisis Semanal: 4:00 PM - 5:00 PM</div>
                                    <div>💬 Q&A Abierto: 5:00 PM - 6:00 PM</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enlaces de Zoom Activos -->
            <div class="px-6 mb-6">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-video mr-3 text-orange-400"></i>Enlaces de Zoom Activos
                </h3>
                <div id="zoom-links-container" class="space-y-4">
                    <!-- Se llenarán dinámicamente -->
                </div>
            </div>

        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }

                // Mostrar panel admin solo para jhon0608
                if (user.name === 'Admin GERMAYORI' || user.email === '<EMAIL>') {
                    document.getElementById('admin-panel').classList.remove('hidden');
                }
            }
        }

        // Enlaces de Zoom (simulados - en producción vendrían de base de datos)
        let zoomLinks = [
            {
                id: 1,
                title: "Mentoria Básica - Fundamentos FVG",
                datetime: "2024-01-15T19:00",
                link: "https://zoom.us/j/123456789",
                type: "basico",
                description: "Introducción a Fair Value Gaps y conceptos básicos",
                active: true
            },
            {
                id: 2,
                title: "Sesión Intermedia - Order Blocks",
                datetime: "2024-01-16T20:00",
                link: "https://zoom.us/j/987654321",
                type: "intermedio",
                description: "Análisis profundo de Order Blocks institucionales",
                active: true
            }
        ];

        // Función para agregar nuevo enlace (solo admin)
        function addZoomLink() {
            const title = document.getElementById('zoom-title').value;
            const datetime = document.getElementById('zoom-datetime').value;
            const link = document.getElementById('zoom-link').value;
            const type = document.getElementById('zoom-type').value;
            const description = document.getElementById('zoom-description').value;

            if (!title || !datetime || !link) {
                alert('Por favor completa todos los campos obligatorios');
                return;
            }

            const newLink = {
                id: Date.now(),
                title,
                datetime,
                link,
                type,
                description,
                active: true
            };

            zoomLinks.unshift(newLink);
            displayZoomLinks();

            // Limpiar formulario
            document.getElementById('zoom-title').value = '';
            document.getElementById('zoom-datetime').value = '';
            document.getElementById('zoom-link').value = '';
            document.getElementById('zoom-description').value = '';

            alert('Enlace de Zoom agregado exitosamente');
        }

        // Función para actualizar horario
        function updateSchedule() {
            const schedule = document.getElementById('weekly-schedule').value;
            if (schedule) {
                document.getElementById('weekly-schedule-display').innerHTML =
                    `<div class="text-white whitespace-pre-line">${schedule}</div>`;
                alert('Horario actualizado exitosamente');
            }
        }

        // Mostrar enlaces de Zoom
        function displayZoomLinks() {
            const container = document.getElementById('zoom-links-container');

            if (zoomLinks.length === 0) {
                container.innerHTML = `
                    <div class="zoom-card rounded-lg p-6 text-center">
                        <div class="text-gray-400">
                            <i class="fas fa-video text-4xl mb-4"></i>
                            <p>No hay enlaces de Zoom disponibles</p>
                            <p class="text-sm mt-2">Los enlaces aparecerán aquí cuando jhon0608 los publique</p>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = zoomLinks.map(zoom => `
                <div class="zoom-card rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-video text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-white">${zoom.title}</h4>
                                <div class="text-gray-300 text-sm">
                                    <i class="fas fa-calendar mr-2"></i>${new Date(zoom.datetime).toLocaleString('es-ES')}
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="text-xs bg-orange-500 text-white px-3 py-1 rounded-full font-bold uppercase">
                                ${zoom.type}
                            </span>
                        </div>
                    </div>

                    <div class="text-gray-300 mb-4">
                        ${zoom.description}
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="${zoom.link}" target="_blank"
                           class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold transition-all">
                            <i class="fas fa-external-link-alt mr-2"></i>Unirse al Zoom
                        </a>
                        <div class="text-green-400 text-sm">
                            <i class="fas fa-circle mr-1"></i>Activo
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Función para cerrar sesión
        function logout() {
            // Limpiar localStorage
            localStorage.removeItem('germayori_user');
            localStorage.removeItem('germayori_selected_plan');
            localStorage.removeItem('germayori_token');

            // Confirmar logout
            alert('Sesión cerrada exitosamente');

            // Redirigir al login
            window.location.href = 'index.html';
        }

        // Inicializar página
        window.onload = function() {
            const user = checkAuth();
            if (user) {
                loadUserInfo();
                displayZoomLinks();
            }
        };
    </script>
</body>
</html>
