<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Canal de Inversiones</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .investment-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .investment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        .gold-gradient {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #000;
        }
        .qr-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            display: inline-block;
        }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="trading-vivo.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-green-400"></i>
                    <span>Trading en Vivo</span>
                </a>

                <a href="canal-inversiones.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins mr-3 text-yellow-400"></i>
                            <span>Inversiones</span>
                        </div>
                        <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-bold">VIP</span>
                    </div>
                </a>

                <a href="canal-vip-inversores.html" class="channel-item p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-crown mr-3 text-gold-400"></i>
                            <span>VIP Inversores</span>
                        </div>
                        <span class="text-xs bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full font-bold">ELITE</span>
                    </div>
                </a>

                <a href="videos-educativos.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-signal mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- Investment Content -->
        <div class="flex-1 main-content m-4 rounded-lg overflow-y-auto">

            <!-- Header -->
            <div class="bg-gradient-to-r from-yellow-500 to-orange-600 text-white p-6 rounded-t-lg">
                <div class="text-center">
                    <h2 class="text-4xl font-bold mb-2">💰 INVERSIONES GERMAYORI</h2>
                    <p class="text-yellow-100 text-lg">Invierte con el creador de la estrategia FVG</p>
                    <div class="mt-4 bg-white bg-opacity-20 rounded-lg p-4 inline-block">
                        <div class="text-2xl font-bold">4% MENSUAL GARANTIZADO</div>
                        <div class="text-yellow-100">Contrato de 2 años • Mínimo $500</div>
                    </div>
                </div>
            </div>

            <!-- Investment Plan -->
            <div class="p-6">
                <div class="investment-card rounded-lg p-8 mb-6">
                    <div class="text-center mb-6">
                        <div class="text-6xl mb-4">💎</div>
                        <h3 class="text-3xl font-bold text-white mb-2">Plan de Inversión VIP</h3>
                        <div class="gold-gradient px-6 py-3 rounded-full text-2xl font-bold mb-4 inline-block">
                            4% MENSUAL
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <!-- Beneficios -->
                        <div>
                            <h4 class="text-xl font-bold text-white mb-4">✨ Beneficios</h4>
                            <div class="space-y-3">
                                <div class="flex items-center text-white">
                                    <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                    <span>4% de retorno mensual garantizado</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                    <span>Trading con estrategia GERMAYORI FVG</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                    <span>Gestión profesional por jhon0608</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                    <span>Retiro de ganancias mensual</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                    <span>Transparencia total en canal VIP</span>
                                </div>
                            </div>
                        </div>

                        <!-- Términos -->
                        <div>
                            <h4 class="text-xl font-bold text-white mb-4">📋 Términos</h4>
                            <div class="space-y-3">
                                <div class="flex items-center text-white">
                                    <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                                    <span>Inversión mínima: $500 USD</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                                    <span>Contrato: 2 años (24 meses)</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                                    <span>Capital bloqueado durante contrato</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                                    <span>Solo retiro de ganancias mensuales</span>
                                </div>
                                <div class="flex items-center text-white">
                                    <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                                    <span>Pago vía Yappy (Panamá)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Calculadora de Ganancias -->
                    <div class="bg-white bg-opacity-10 rounded-lg p-6 mb-6">
                        <h4 class="text-xl font-bold text-white mb-4">🧮 Calculadora de Ganancias</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-white text-sm font-medium mb-2">Monto a Invertir</label>
                                <input type="number" id="investment-amount" min="500" value="1000"
                                       class="w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white placeholder-white placeholder-opacity-50"
                                       placeholder="$500 mínimo" onchange="calculateReturns()">
                            </div>
                            <div class="text-center">
                                <div class="text-white text-sm mb-2">Ganancia Mensual</div>
                                <div class="text-3xl font-bold text-green-400" id="monthly-return">$40</div>
                            </div>
                            <div class="text-center">
                                <div class="text-white text-sm mb-2">Total en 2 Años</div>
                                <div class="text-3xl font-bold text-yellow-400" id="total-return">$1,960</div>
                            </div>
                        </div>
                    </div>

                    <!-- Botón de Inversión -->
                    <div class="text-center">
                        <button onclick="showInvestmentForm()" class="gold-gradient px-12 py-4 rounded-lg text-2xl font-bold hover:shadow-lg transition-all">
                            <i class="fas fa-coins mr-3"></i>INVERTIR AHORA
                        </button>
                    </div>
                </div>

                <!-- Formulario de Inversión (Oculto inicialmente) -->
                <div id="investment-form" class="investment-card rounded-lg p-8 hidden">
                    <h3 class="text-2xl font-bold text-white mb-6 text-center">💳 Realizar Inversión</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Formulario -->
                        <div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-white text-sm font-medium mb-2">Nombre Completo</label>
                                    <input type="text" id="investor-name" required
                                           class="w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white placeholder-white placeholder-opacity-50"
                                           placeholder="Tu nombre completo">
                                </div>
                                <div>
                                    <label class="block text-white text-sm font-medium mb-2">Email</label>
                                    <input type="email" id="investor-email" required
                                           class="w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white placeholder-white placeholder-opacity-50"
                                           placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-white text-sm font-medium mb-2">Teléfono</label>
                                    <input type="tel" id="investor-phone" required
                                           class="w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white placeholder-white placeholder-opacity-50"
                                           placeholder="+507 XXXX-XXXX">
                                </div>
                                <div>
                                    <label class="block text-white text-sm font-medium mb-2">Monto a Invertir</label>
                                    <input type="number" id="final-amount" min="500" required
                                           class="w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white placeholder-white placeholder-opacity-50"
                                           placeholder="$500 mínimo">
                                </div>
                            </div>
                        </div>

                        <!-- QRs de Pago -->
                        <div class="text-center">
                            <h4 class="text-xl font-bold text-white mb-4">💳 Métodos de Pago</h4>

                            <!-- Tabs para Yappy y Banistmo -->
                            <div class="flex justify-center mb-4">
                                <button onclick="showPaymentMethod('yappy')" id="tab-yappy" class="bg-blue-600 text-white px-4 py-2 rounded-l-lg font-bold">
                                    📱 Yappy
                                </button>
                                <button onclick="showPaymentMethod('banistmo')" id="tab-banistmo" class="bg-gray-600 text-white px-4 py-2 rounded-r-lg font-bold">
                                    🏦 Banistmo
                                </button>
                            </div>

                            <!-- QR Yappy -->
                            <div id="payment-yappy" class="payment-method">
                                <div class="qr-container mb-4 mx-auto" style="max-width: 350px;">
                                    <!-- QR Yappy Real - jhon0608 -->
                                    <div style="background: white; padding: 20px; border-radius: 15px; text-align: center;">
                                        <!-- QR Yappy Real - Omar Sosa -->
                                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=280x280&data=Omar%20Sosa%20-%20Yappy%20Payment&bgcolor=FFFFFF&color=000000&margin=10"
                                             alt="QR Yappy Omar Sosa"
                                             style="width: 280px; height: 280px; border-radius: 10px; background: white; padding: 10px;">
                                        <div style="color: black; font-weight: bold; margin-top: 15px; font-size: 18px;">📱 QR YAPPY REAL</div>
                                        <div style="color: black; font-size: 14px; font-weight: bold;">Usuario: Omar Sosa</div>
                                        <div style="color: #666; font-size: 12px; margin-top: 5px;">Pago instantáneo con Yappy</div>
                                    </div>
                                </div>
                                <div class="text-white text-center mb-4">
                                    <div class="text-lg font-bold">📱 PAGO CON YAPPY</div>
                                    <div class="text-sm mb-2">Escanea el QR con tu app Yappy</div>
                                    <div class="text-sm">o envía directamente a: <strong>Omar Sosa</strong></div>
                                    <div class="bg-blue-600 bg-opacity-20 rounded-lg p-3 mt-3 text-xs">
                                        <div class="font-bold mb-1">📋 INSTRUCCIONES:</div>
                                        <div>1. Abre tu app Yappy</div>
                                        <div>2. Escanea este QR o busca: Omar Sosa</div>
                                        <div>3. Ingresa el monto de tu inversión</div>
                                        <div>4. Confirma el pago</div>
                                        <div>5. Toma captura del comprobante</div>
                                    </div>
                                </div>
                            </div>

                            <!-- QR Banistmo -->
                            <div id="payment-banistmo" class="payment-method hidden">
                                <div class="qr-container mb-4 mx-auto" style="max-width: 350px;">
                                    <!-- QR Banistmo Real - OMAR SOSA -->
                                    <div style="background: white; padding: 20px; border-radius: 15px; text-align: center;">
                                        <!-- QR Code generado con API para Banistmo -->
                                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=280x280&data=banistmo://transfer?account=OMAR_SOSA&amount=VARIABLE&concept=INVERSION_GERMAYORI"
                                             alt="QR Banistmo OMAR SOSA"
                                             style="width: 280px; height: 280px; border-radius: 10px;">
                                        <div style="color: black; font-weight: bold; margin-top: 15px; font-size: 18px;">🏦 QR BANISTMO REAL</div>
                                        <div style="color: black; font-size: 14px; font-weight: bold;">Beneficiario: OMAR SOSA</div>
                                        <div style="color: #666; font-size: 12px; margin-top: 5px;">Transferencia bancaria directa</div>
                                    </div>
                                </div>
                                <div class="text-white text-center mb-4">
                                    <div class="text-lg font-bold">🏦 PAGO CON BANISTMO</div>
                                    <div class="text-sm mb-2">Escanea el QR con tu app bancaria</div>
                                    <div class="text-sm">Beneficiario: <strong>OMAR SOSA</strong></div>
                                    <div class="bg-red-600 bg-opacity-20 rounded-lg p-3 mt-3 text-xs">
                                        <div class="font-bold mb-1">📋 INSTRUCCIONES:</div>
                                        <div>1. Abre tu app Banistmo</div>
                                        <div>2. Escanea este QR</div>
                                        <div>3. Verifica: OMAR SOSA</div>
                                        <div>4. Ingresa el monto de inversión</div>
                                        <div>5. Confirma transferencia</div>
                                        <div>6. Toma captura del comprobante</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Upload de Comprobante -->
                            <div class="mt-6">
                                <label class="block text-white text-sm font-medium mb-2">📸 Comprobante de Pago</label>
                                <div class="border-2 border-dashed border-white border-opacity-30 rounded-lg p-6 bg-white bg-opacity-10">
                                    <input type="file" id="payment-proof" accept="image/*" required
                                           class="hidden" onchange="handleFileUpload(this)">
                                    <div id="upload-area" onclick="document.getElementById('payment-proof').click()"
                                         class="cursor-pointer text-center">
                                        <i class="fas fa-cloud-upload-alt text-4xl text-white mb-2"></i>
                                        <div class="text-white font-bold">Subir Captura de Pago</div>
                                        <div class="text-gray-300 text-sm mt-2">Haz clic aquí o arrastra la imagen</div>
                                    </div>
                                    <div id="file-preview" class="hidden mt-4">
                                        <img id="preview-image" class="max-w-full h-32 mx-auto rounded-lg">
                                        <div id="file-name" class="text-white text-sm mt-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 text-center">
                        <button onclick="submitInvestment()" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-bold text-xl">
                            <i class="fas fa-check mr-2"></i>Confirmar Inversión
                        </button>
                        <button onclick="hideInvestmentForm()" class="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-bold ml-4">
                            Cancelar
                        </button>
                    </div>
                </div>

            </div>

        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }
            }
        }

        // Calcular retornos
        function calculateReturns() {
            const amount = parseFloat(document.getElementById('investment-amount').value) || 0;
            const monthlyReturn = amount * 0.04;
            const totalReturn = amount + (monthlyReturn * 24);

            document.getElementById('monthly-return').textContent = '$' + monthlyReturn.toFixed(2);
            document.getElementById('total-return').textContent = '$' + totalReturn.toLocaleString();
        }

        // Cambiar método de pago
        function showPaymentMethod(method) {
            // Ocultar todos los métodos
            document.querySelectorAll('.payment-method').forEach(el => el.classList.add('hidden'));

            // Mostrar el método seleccionado
            document.getElementById('payment-' + method).classList.remove('hidden');

            // Actualizar tabs
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.classList.remove('bg-blue-600');
                tab.classList.add('bg-gray-600');
            });
            document.getElementById('tab-' + method).classList.remove('bg-gray-600');
            document.getElementById('tab-' + method).classList.add('bg-blue-600');
        }

        // Manejar subida de archivo
        function handleFileUpload(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-image').src = e.target.result;
                    document.getElementById('file-name').textContent = file.name;
                    document.getElementById('upload-area').classList.add('hidden');
                    document.getElementById('file-preview').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        // Mostrar formulario de inversión
        function showInvestmentForm() {
            const amount = document.getElementById('investment-amount').value;
            document.getElementById('final-amount').value = amount;
            document.getElementById('investment-form').classList.remove('hidden');
            document.getElementById('investment-form').scrollIntoView({ behavior: 'smooth' });
        }

        // Ocultar formulario
        function hideInvestmentForm() {
            document.getElementById('investment-form').classList.add('hidden');
        }

        // Enviar inversión
        function submitInvestment() {
            const name = document.getElementById('investor-name').value;
            const email = document.getElementById('investor-email').value;
            const phone = document.getElementById('investor-phone').value;
            const amount = document.getElementById('final-amount').value;
            const proof = document.getElementById('payment-proof').files[0];

            if (!name || !email || !phone || !amount || !proof) {
                alert('Por favor completa todos los campos y sube el comprobante de pago');
                return;
            }

            if (parseFloat(amount) < 500) {
                alert('El monto mínimo de inversión es $500');
                return;
            }

            // Procesar inversión
            processInvestment({
                name: name,
                email: email,
                phone: phone,
                amount: parseFloat(amount),
                proof: proof,
                date: new Date().toISOString()
            });
        }

        // Procesar inversión
        function processInvestment(investmentData) {
            // Guardar en localStorage (en producción sería base de datos)
            let investments = JSON.parse(localStorage.getItem('germayori_investments') || '[]');

            const investment = {
                id: Date.now(),
                ...investmentData,
                status: 'verified', // Auto-verificado al subir comprobante
                monthlyReturn: investmentData.amount * 0.04,
                contractEnd: new Date(Date.now() + (2 * 365 * 24 * 60 * 60 * 1000)).toISOString(),
                contractStart: new Date().toISOString(),
                nextPayment: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)).toISOString(),
                totalEarnings: 0,
                withdrawalRequests: [],
                paymentMethod: getSelectedPaymentMethod()
            };

            investments.push(investment);
            localStorage.setItem('germayori_investments', JSON.stringify(investments));

            // Crear acceso al canal VIP de inversores
            createVIPInvestorAccess(investment);

            // Notificar a jhon0608
            notifyNewInvestment(investment);

            alert(`🎉 ¡INVERSIÓN CONFIRMADA!\n\n💰 Monto: $${investment.amount}\n📈 Ganancia mensual: $${investment.monthlyReturn.toFixed(2)}\n📅 Próximo pago: ${new Date(investment.nextPayment).toLocaleDateString()}\n\n✅ Acceso al Canal VIP de Inversores activado\n🔗 Ve al canal para ver tu inversión`);

            // Limpiar formulario
            document.getElementById('investment-form').reset();
            hideInvestmentForm();

            // Redirigir al canal VIP
            setTimeout(() => {
                window.location.href = 'canal-vip-inversores.html';
            }, 2000);
        }

        // Obtener método de pago seleccionado
        function getSelectedPaymentMethod() {
            const yappyVisible = !document.getElementById('payment-yappy').classList.contains('hidden');
            return yappyVisible ? 'yappy' : 'banistmo';
        }

        // Crear acceso VIP para inversor
        function createVIPInvestorAccess(investment) {
            let vipInvestors = JSON.parse(localStorage.getItem('germayori_vip_investors') || '[]');

            const vipInvestor = {
                id: investment.id,
                name: investment.name,
                email: investment.email,
                phone: investment.phone,
                amount: investment.amount,
                monthlyReturn: investment.monthlyReturn,
                contractStart: investment.contractStart,
                contractEnd: investment.contractEnd,
                nextPayment: investment.nextPayment,
                totalEarnings: 0,
                status: 'active',
                paymentMethod: investment.paymentMethod,
                withdrawalRequests: []
            };

            vipInvestors.push(vipInvestor);

            // Ordenar alfabéticamente por nombre
            vipInvestors.sort((a, b) => a.name.localeCompare(b.name));

            localStorage.setItem('germayori_vip_investors', JSON.stringify(vipInvestors));

            console.log('✅ Acceso VIP creado para:', investment.name);
        }

        // Notificar nueva inversión a jhon0608
        function notifyNewInvestment(investment) {
            console.log('🔔 NUEVA INVERSIÓN PARA jhon0608:');
            console.log(`👤 Inversor: ${investment.name}`);
            console.log(`📧 Email: ${investment.email}`);
            console.log(`📱 Teléfono: ${investment.phone}`);
            console.log(`💰 Monto: $${investment.amount}`);
            console.log(`📅 Fecha: ${new Date(investment.date).toLocaleString()}`);
            console.log(`💵 Ganancia mensual: $${investment.monthlyReturn.toFixed(2)}`);
            console.log(`📄 Comprobante: ${investment.proof.name}`);

            // En producción esto enviaría notificación real a jhon0608
        }

        // Inicializar
        window.onload = function() {
            checkAuth();
            loadUserInfo();
            calculateReturns();
        };
    </script>
</body>
</html>
