import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { BarChart3, Crown, TrendingUp, TrendingDown, Target, DollarSign, Calendar, PieChart } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, PieChart as RechartsPieChart, Cell, Pie } from 'recharts';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import PaywallModal from '../components/Paywall/PaywallModal';

const Estadisticas = () => {
  const { user } = useAuth();
  const [showPaywall, setShowPaywall] = useState(!user?.isPaid);
  const [timeframe, setTimeframe] = useState('1M');

  // Datos simulados para gráficos
  const performanceData = [
    { name: 'Ene', profit: 400, trades: 24, winRate: 65 },
    { name: 'Feb', profit: 300, trades: 18, winRate: 72 },
    { name: 'Mar', profit: 600, trades: 32, winRate: 68 },
    { name: 'Abr', profit: 800, trades: 28, winRate: 75 },
    { name: 'May', profit: 500, trades: 22, winRate: 70 },
    { name: 'Jun', profit: 900, trades: 35, winRate: 78 },
  ];

  const pairDistribution = [
    { name: 'EUR/USD', value: 35, color: '#e94560' },
    { name: 'GBP/USD', value: 25, color: '#27ae60' },
    { name: 'USD/JPY', value: 20, color: '#f39c12' },
    { name: 'USD/CAD', value: 12, color: '#3498db' },
    { name: 'Otros', value: 8, color: '#9b59b6' },
  ];

  const tradingStats = {
    totalTrades: 159,
    winningTrades: 112,
    losingTrades: 47,
    winRate: 70.4,
    avgWin: 85.30,
    avgLoss: -42.15,
    profitFactor: 2.02,
    sharpeRatio: 1.85,
    maxDrawdown: -8.5,
    totalProfit: 2847.65
  };

  if (!user?.isPaid) {
    return (
      <>
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="w-24 h-24 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="text-warning" size={48} />
            </div>
            <h1 className="text-3xl font-bold text-text mb-4">Estadísticas Avanzadas Premium</h1>
            <p className="text-gray-400 max-w-2xl mx-auto mb-8">
              Analiza tu rendimiento de trading con métricas detalladas, gráficos avanzados
              y estadísticas profesionales para mejorar tu estrategia.
            </p>
            <Button variant="primary" onClick={() => setShowPaywall(true)}>
              Desbloquear Estadísticas
            </Button>
          </motion.div>
        </div>
        <PaywallModal
          isOpen={showPaywall}
          onClose={() => setShowPaywall(false)}
          feature="Estadísticas Avanzadas"
        />
      </>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <BarChart3 className="mr-3 text-danger" size={32} />
            Estadísticas Avanzadas
            <Crown className="ml-2 text-warning" size={24} />
          </h1>
          <p className="text-gray-400">
            Análisis detallado de tu rendimiento de trading
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {['1W', '1M', '3M', '6M', '1Y'].map((tf) => (
            <button
              key={tf}
              onClick={() => setTimeframe(tf)}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                timeframe === tf
                  ? 'bg-danger text-white'
                  : 'text-gray-400 hover:text-white hover:bg-accent'
              }`}
            >
              {tf}
            </button>
          ))}
        </div>
      </motion.div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-success bg-opacity-20 rounded-lg mx-auto mb-3">
            <Target className="text-success" size={24} />
          </div>
          <h3 className="text-2xl font-bold text-text mb-1">{tradingStats.winRate}%</h3>
          <p className="text-gray-400 text-sm">Tasa de Éxito</p>
        </Card>

        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-danger bg-opacity-20 rounded-lg mx-auto mb-3">
            <DollarSign className="text-danger" size={24} />
          </div>
          <h3 className="text-2xl font-bold text-text mb-1">${tradingStats.totalProfit}</h3>
          <p className="text-gray-400 text-sm">Ganancia Total</p>
        </Card>

        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-warning bg-opacity-20 rounded-lg mx-auto mb-3">
            <TrendingUp className="text-warning" size={24} />
          </div>
          <h3 className="text-2xl font-bold text-text mb-1">{tradingStats.profitFactor}</h3>
          <p className="text-gray-400 text-sm">Factor de Ganancia</p>
        </Card>

        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-accent bg-opacity-20 rounded-lg mx-auto mb-3">
            <Calendar className="text-accent" size={24} />
          </div>
          <h3 className="text-2xl font-bold text-text mb-1">{tradingStats.totalTrades}</h3>
          <p className="text-gray-400 text-sm">Total Operaciones</p>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Chart */}
        <Card>
          <h3 className="text-lg font-semibold text-text mb-6">Evolución de Rendimiento</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="name" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#16213e',
                  border: '1px solid #0f3460',
                  borderRadius: '8px'
                }}
              />
              <Area
                type="monotone"
                dataKey="profit"
                stroke="#e94560"
                fill="#e94560"
                fillOpacity={0.2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>

        {/* Pair Distribution */}
        <Card>
          <h3 className="text-lg font-semibold text-text mb-6">Distribución por Pares</h3>
          <ResponsiveContainer width="100%" height={300}>
            <RechartsPieChart>
              <Pie
                data={pairDistribution}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {pairDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </RechartsPieChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Detailed Stats */}
      <Card>
        <h3 className="text-lg font-semibold text-text mb-6">Métricas Detalladas</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center">
            <h4 className="font-semibold text-success mb-2">Operaciones Ganadoras</h4>
            <p className="text-2xl font-bold text-text">{tradingStats.winningTrades}</p>
            <p className="text-sm text-gray-400">Promedio: ${tradingStats.avgWin}</p>
          </div>
          <div className="text-center">
            <h4 className="font-semibold text-danger mb-2">Operaciones Perdedoras</h4>
            <p className="text-2xl font-bold text-text">{tradingStats.losingTrades}</p>
            <p className="text-sm text-gray-400">Promedio: ${tradingStats.avgLoss}</p>
          </div>
          <div className="text-center">
            <h4 className="font-semibold text-warning mb-2">Sharpe Ratio</h4>
            <p className="text-2xl font-bold text-text">{tradingStats.sharpeRatio}</p>
            <p className="text-sm text-gray-400">Excelente rendimiento</p>
          </div>
          <div className="text-center">
            <h4 className="font-semibold text-accent mb-2">Max Drawdown</h4>
            <p className="text-2xl font-bold text-text">{tradingStats.maxDrawdown}%</p>
            <p className="text-sm text-gray-400">Riesgo controlado</p>
          </div>
          <div className="text-center">
            <h4 className="font-semibold text-text mb-2">Factor de Ganancia</h4>
            <p className="text-2xl font-bold text-text">{tradingStats.profitFactor}</p>
            <p className="text-sm text-gray-400">Muy bueno (&gt;1.5)</p>
          </div>
          <div className="text-center">
            <h4 className="font-semibold text-text mb-2">Consistencia</h4>
            <p className="text-2xl font-bold text-success">Alta</p>
            <p className="text-sm text-gray-400">Rendimiento estable</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Estadisticas;
