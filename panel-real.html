<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Panel REAL (Sin Simulaciones)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #FFD700;
            margin-bottom: 10px;
        }

        .sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
        }

        .section h2 {
            color: #FFD700;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #FFD700;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #fff;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1rem;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: #fff;
        }

        .data-list {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .data-item {
            background: rgba(255, 255, 255, 0.1);
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #FFD700;
        }

        .data-item h4 {
            color: #FFD700;
            margin-bottom: 5px;
        }

        .data-item p {
            margin: 3px 0;
            font-size: 0.9rem;
        }

        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-top: 5px;
        }

        .status-success {
            background: #28a745;
            color: #fff;
        }

        .status-error {
            background: #dc3545;
            color: #fff;
        }

        .status-info {
            background: #17a2b8;
            color: #fff;
        }

        .file-input {
            border: 2px dashed #FFD700;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .file-input input[type="file"] {
            display: none;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FFD700;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #ccc;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 1000;
        }

        .online {
            background: #28a745;
            color: #fff;
        }

        .offline {
            background: #dc3545;
            color: #fff;
        }

        @media (max-width: 768px) {
            .sections {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔄 Conectando...</div>

    <div class="container">
        <div class="header">
            <h1>🚀 GERMAYORI PANEL REAL</h1>
            <p>Sin simulaciones - Solo datos reales de la base de datos</p>
        </div>

        <div class="sections">
            <!-- SECCIÓN: SUBIR SEÑALES REALES -->
            <div class="section">
                <h2>📈 SUBIR SEÑAL REAL</h2>
                <form id="signalForm">
                    <div class="form-group">
                        <label>Par de Divisas:</label>
                        <select id="signalPair" required>
                            <option value="">Seleccionar par</option>
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="USDCAD">USD/CAD</option>
                            <option value="USDCHF">USD/CHF</option>
                            <option value="NZDUSD">NZD/USD</option>
                            <option value="XAUUSD">XAU/USD (Oro)</option>
                            <option value="NAS100">NAS100</option>
                            <option value="US30">US30</option>
                            <option value="SPX500">SPX500</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tipo:</label>
                        <select id="signalType" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="BUY">COMPRA (BUY)</option>
                            <option value="SELL">VENTA (SELL)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Precio de Entrada:</label>
                        <input type="number" id="signalEntry" step="0.00001" required placeholder="1.08500">
                    </div>
                    <div class="form-group">
                        <label>Stop Loss:</label>
                        <input type="number" id="signalSL" step="0.00001" required placeholder="1.08000">
                    </div>
                    <div class="form-group">
                        <label>Take Profit:</label>
                        <input type="number" id="signalTP" step="0.00001" required placeholder="1.09000">
                    </div>
                    <div class="form-group">
                        <label>Análisis (Opcional):</label>
                        <textarea id="signalAnalysis" placeholder="Descripción del análisis técnico..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Timeframe:</label>
                        <select id="signalTimeframe">
                            <option value="M5">5 Minutos</option>
                            <option value="M15">15 Minutos</option>
                            <option value="M30">30 Minutos</option>
                            <option value="H1" selected>1 Hora</option>
                            <option value="H4">4 Horas</option>
                            <option value="D1">Diario</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">📈 SUBIR SEÑAL REAL</button>
                </form>
            </div>

            <!-- SECCIÓN: AGREGAR USUARIO REAL -->
            <div class="section">
                <h2>👤 AGREGAR USUARIO REAL</h2>
                <form id="userForm">
                    <div class="form-group">
                        <label>Nombre Completo:</label>
                        <input type="text" id="userName" required placeholder="Juan Pérez">
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" id="userEmail" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Contraseña:</label>
                        <input type="password" id="userPassword" required placeholder="Mínimo 6 caracteres">
                    </div>
                    <div class="form-group">
                        <label>Tipo de Usuario:</label>
                        <select id="userType">
                            <option value="user">Usuario Normal</option>
                            <option value="premium">Usuario Premium</option>
                            <option value="admin">Administrador</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">👤 AGREGAR USUARIO REAL</button>
                </form>
            </div>

            <!-- SECCIÓN: SUBIR VIDEO EDUCATIVO REAL -->
            <div class="section">
                <h2>📹 SUBIR VIDEO EDUCATIVO REAL</h2>
                <form id="videoForm">
                    <div class="form-group">
                        <label>Título del Video:</label>
                        <input type="text" id="videoTitle" required placeholder="Estrategia FVG Institucional">
                    </div>
                    <div class="form-group">
                        <label>Descripción:</label>
                        <textarea id="videoDescription" placeholder="Descripción del contenido del video..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Categoría:</label>
                        <select id="videoCategory">
                            <option value="basico">Básico ($25/mes)</option>
                            <option value="intermedio">Intermedio ($50/mes)</option>
                            <option value="avanzado">Avanzado ($75/mes)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>URL del Video (YouTube/Vimeo):</label>
                        <input type="url" id="videoUrl" placeholder="https://youtube.com/watch?v=...">
                    </div>
                    <div class="form-group">
                        <label>O subir archivo de video:</label>
                        <div class="file-input" onclick="document.getElementById('videoFile').click()">
                            <input type="file" id="videoFile" accept="video/*">
                            <p>📹 Clic para seleccionar video</p>
                            <small>Formatos: MP4, AVI, MOV (Max: 500MB)</small>
                        </div>
                    </div>
                    <button type="submit" class="btn">📹 SUBIR VIDEO REAL</button>
                </form>
            </div>

            <!-- SECCIÓN: VER DATOS REALES -->
            <div class="section">
                <h2>📊 DATOS REALES DE LA BASE</h2>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">0</div>
                        <div class="stat-label">Usuarios</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalSignals">0</div>
                        <div class="stat-label">Señales</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalVideos">0</div>
                        <div class="stat-label">Videos</div>
                    </div>
                </div>

                <button class="btn" onclick="loadAllData()">🔄 ACTUALIZAR DATOS REALES</button>
                
                <h3 style="color: #FFD700; margin: 20px 0 10px 0;">👥 Usuarios Reales:</h3>
                <div class="data-list" id="usersList">
                    <p>Cargando usuarios reales...</p>
                </div>

                <h3 style="color: #FFD700; margin: 20px 0 10px 0;">📈 Señales Reales:</h3>
                <div class="data-list" id="signalsList">
                    <p>Cargando señales reales...</p>
                </div>
            </div>

            <!-- SECCIÓN: REGISTRO AUTOMÁTICO -->
            <div class="section">
                <h2>🔄 PROBAR REGISTRO AUTOMÁTICO</h2>
                <p style="margin-bottom: 15px;">Prueba el sistema de registro automático:</p>
                
                <form id="autoRegisterForm">
                    <div class="form-group">
                        <label>Nombre:</label>
                        <input type="text" id="autoName" required placeholder="Usuario de Prueba">
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" id="autoEmail" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Contraseña:</label>
                        <input type="password" id="autoPassword" required placeholder="123456">
                    </div>
                    <button type="submit" class="btn">🔄 REGISTRAR AUTOMÁTICAMENTE</button>
                </form>

                <div style="margin-top: 20px;">
                    <button class="btn btn-danger" onclick="clearAllData()">🗑️ LIMPIAR TODA LA BASE</button>
                    <small style="display: block; margin-top: 5px; color: #ccc;">
                        ⚠️ Esto eliminará TODOS los datos reales
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="panel-real.js"></script>
</body>
</html>
