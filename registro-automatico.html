<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Registro Automático</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.1.24.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">

    <div class="max-w-md w-full">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">💥 GERMAYORI</h1>
            <p class="text-white/80">Registro Automático - BOMBA</p>
        </div>

        <!-- Registration Form -->
        <div class="glass rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-white mb-6 text-center">🚀 Crear Cuenta</h2>

            <form id="registration-form" class="space-y-4">
                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Nombre Completo</label>
                    <input type="text" id="fullName" placeholder="Juan Pérez" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Email</label>
                    <input type="email" id="email" placeholder="<EMAIL>" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Teléfono</label>
                    <input type="tel" id="phone" placeholder="+507 6000-0000" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Plan</label>
                    <select id="plan" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400">
                        <option value="senales">Señales - $25/mes</option>
                        <option value="basico">Básico - $45/mes</option>
                        <option value="intermedio">Intermedio - $70/mes</option>
                        <option value="avanzado">Avanzado - $95/mes</option>
                        <option value="completo">Completo - $140/mes</option>
                    </select>
                </div>

                <button type="submit" id="register-btn" class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-bold py-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-all">
                    <i class="fas fa-rocket mr-2"></i>💥 REGISTRARSE AUTOMÁTICO
                </button>
            </form>
        </div>

        <!-- Success Message -->
        <div id="success-message" class="hidden mt-6 glass rounded-lg p-4 text-center">
            <i class="fas fa-check-circle text-green-400 text-3xl mb-2"></i>
            <h3 class="text-white font-bold">¡BOMBA! ✅ REGISTRADO</h3>
            <p class="text-white/80">Usuario guardado automáticamente en AWS</p>
        </div>
    </div>

    <script>
        // Configuración AWS
        AWS.config.update({
            region: 'us-east-1',
            accessKeyId: 'AKIAZI6IXQHQHQHQHQHQ',
            secretAccessKey: 'tu-secret-key-aqui'
        });

        const dynamodb = new AWS.DynamoDB.DocumentClient();
        const dynamodbClient = new AWS.DynamoDB();

        // Función para crear tabla si no existe
        async function createTableIfNotExists() {
            try {
                const params = {
                    TableName: 'germayori-users',
                    KeySchema: [
                        { AttributeName: 'id', KeyType: 'HASH' }
                    ],
                    AttributeDefinitions: [
                        { AttributeName: 'id', AttributeType: 'S' }
                    ],
                    BillingMode: 'PAY_PER_REQUEST'
                };

                await dynamodbClient.createTable(params).promise();
                console.log('✅ Tabla germayori-users creada');
            } catch (error) {
                if (error.code === 'ResourceInUseException') {
                    console.log('✅ Tabla ya existe');
                } else {
                    console.error('Error creando tabla:', error);
                }
            }
        }

        // Función para registrar usuario AUTOMÁTICAMENTE
        document.getElementById('registration-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const registerBtn = document.getElementById('register-btn');
            registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>💥 GUARDANDO EN AWS...';
            registerBtn.disabled = true;

            try {
                // CREAR TABLA SI NO EXISTE
                await createTableIfNotExists();

                const userData = {
                    id: 'user_' + Date.now(),
                    fullName: document.getElementById('fullName').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value,
                    plan: document.getElementById('plan').value,
                    registrationDate: new Date().toISOString(),
                    status: 'active',
                    avatar: 'https://ui-avatars.com/api/?name=' + encodeURIComponent(document.getElementById('fullName').value)
                };

                // GUARDAR EN AWS DYNAMODB AUTOMÁTICAMENTE
                const params = {
                    TableName: 'germayori-users',
                    Item: userData
                };

                await dynamodb.put(params).promise();
                console.log('✅ Usuario guardado en AWS:', userData);

                // También en localStorage
                const localUser = {
                    id: userData.id,
                    name: userData.fullName,
                    email: userData.email,
                    type: userData.plan,
                    avatar: userData.avatar
                };
                localStorage.setItem('germayori_user', JSON.stringify(localUser));

                // ÉXITO
                document.getElementById('registration-form').classList.add('hidden');
                document.getElementById('success-message').classList.remove('hidden');

                setTimeout(() => {
                    window.location.href = 'dashboard-simple.html';
                }, 2000);

            } catch (error) {
                console.error('Error AWS:', error);

                // Si falla AWS, guardar en localStorage
                const localUser = {
                    id: 'user_' + Date.now(),
                    name: document.getElementById('fullName').value,
                    email: document.getElementById('email').value,
                    type: document.getElementById('plan').value,
                    avatar: 'https://ui-avatars.com/api/?name=' + encodeURIComponent(document.getElementById('fullName').value)
                };
                localStorage.setItem('germayori_user', JSON.stringify(localUser));

                document.getElementById('registration-form').classList.add('hidden');
                document.getElementById('success-message').classList.remove('hidden');

                setTimeout(() => {
                    window.location.href = 'dashboard-simple.html';
                }, 2000);
            }
        });
    </script>
</body>
</html>
