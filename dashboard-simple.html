<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img src="https://ui-avatars.com/api/?name=User&background=00d4ff&color=fff" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div class="font-semibold text-sm">Usuario GERMAYORI</div>
                        <div class="text-xs text-gray-400">PREMIUM</div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <div onclick="window.location.href='chat.html'" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </div>

                <div onclick="window.location.href='trading-vivo.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-chart-line mr-3 text-green-400"></i>
                            <span>Trading en Vivo</span>
                        </div>
                        <span class="text-xs bg-red-500 text-white px-2 py-1 rounded-full font-bold animate-pulse">LIVE</span>
                    </div>
                </div>

                <div onclick="window.location.href='calculadora.html'" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-calculator mr-3 text-green-400"></i>
                    <span>Calculadora</span>
                </div>

                <div onclick="window.location.href='noticias.html'" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i>
                    <span>Noticias</span>
                </div>

                <div onclick="window.location.href='senales.html'" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </div>

                <div onclick="window.location.href='notificaciones.html'" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-bell mr-3 text-pink-400"></i>
                    <span>Notificaciones</span>
                </div>

                <div onclick="window.location.href='alertas.html'" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i>
                    <span>Alertas Mercado</span>
                </div>

                <div onclick="window.location.href='tradingview.html'" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-chart-area mr-3 text-red-400"></i>
                    <span>TradingView</span>
                </div>
            </div>

            <!-- Canales Educativos -->
            <div class="space-y-2 mt-6">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">🎓 Educación GERMAYORI</div>

                <div onclick="window.location.href='canal-videos-educativos.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                            <span>Videos Educativos</span>
                        </div>
                        <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-bold">PREMIUM</span>
                    </div>
                </div>

                <div onclick="window.location.href='mentoria.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-user-graduate mr-3 text-violet-400"></i>
                            <span>Mentoría Directa</span>
                        </div>
                        <span class="text-xs bg-purple-500 text-white px-2 py-1 rounded-full font-bold">VIP</span>
                    </div>
                </div>

                <div onclick="window.location.href='canal-zoom-enlaces.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-video mr-3 text-orange-400"></i>
                            <span>Enlaces Zoom</span>
                        </div>
                        <span class="text-xs bg-orange-500 text-white px-2 py-1 rounded-full font-bold">LIVE</span>
                    </div>
                </div>

                <div onclick="window.location.href='canal-analisis.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-chart-bar mr-3 text-pink-400"></i>
                            <span>Análisis y Certificaciones</span>
                        </div>
                        <span class="text-xs bg-pink-500 text-white px-2 py-1 rounded-full font-bold">NEW</span>
                    </div>
                </div>

                <div onclick="window.location.href='dashboard-personal.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-user mr-3 text-yellow-400"></i>
                            <span>Dashboard Personal</span>
                        </div>
                        <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-bold">MI PERFIL</span>
                    </div>
                </div>
            </div>

            <!-- Canales de Inversión -->
            <div class="space-y-2 mt-6">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">💰 Inversiones GERMAYORI</div>

                <div onclick="window.location.href='canal-inversiones.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins mr-3 text-yellow-400"></i>
                            <span>Inversiones</span>
                        </div>
                        <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-bold">VIP</span>
                    </div>
                </div>

                <div onclick="window.location.href='canal-vip-inversores.html'" class="channel-item p-3 rounded cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-crown mr-3 text-gold-400"></i>
                            <span>VIP Inversores</span>
                        </div>
                        <span class="text-xs bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full font-bold">ELITE</span>
                    </div>
                </div>
            </div>

            <!-- Logout -->
            <div class="mt-8">
                <button onclick="logout()" class="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>Cerrar Sesión
                </button>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-white mb-4">Bienvenido a GERMAYORI</h1>
                <p class="text-gray-200 mb-8">Selecciona un canal para comenzar</p>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Chat Card -->
                    <div onclick="window.location.href='chat.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer">
                        <div class="text-cyan-400 text-4xl mb-4">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Chat Educativo</h3>
                        <p class="text-gray-200">Asistente IA con OpenAI</p>
                    </div>

                    <!-- Trading en Vivo Card -->
                    <div onclick="window.location.href='trading-vivo.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-green-400">
                        <div class="text-green-400 text-4xl mb-4">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Trading en Vivo</h3>
                        <p class="text-gray-200">Operaciones reales jhon0608</p>
                        <div class="mt-2">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">🔴 LIVE</span>
                        </div>
                    </div>

                    <!-- Calculadora Card -->
                    <div onclick="window.location.href='calculadora.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer">
                        <div class="text-green-400 text-4xl mb-4">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Calculadora</h3>
                        <p class="text-gray-200">Pips, profit y riesgo</p>
                    </div>

                    <!-- Noticias Card -->
                    <div onclick="window.location.href='noticias.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer">
                        <div class="text-yellow-400 text-4xl mb-4">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Noticias</h3>
                        <p class="text-gray-200">Calendario económico</p>
                    </div>

                    <!-- Señales Card -->
                    <div onclick="window.location.href='senales.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer">
                        <div class="text-purple-400 text-4xl mb-4">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Señales</h3>
                        <p class="text-gray-200">Trading signals</p>
                    </div>

                    <!-- Notificaciones Card -->
                    <div onclick="window.location.href='notificaciones.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer">
                        <div class="text-pink-400 text-4xl mb-4">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Notificaciones</h3>
                        <p class="text-gray-200">Sistema de alertas</p>
                    </div>

                    <!-- Alertas Card -->
                    <div onclick="window.location.href='alertas.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer">
                        <div class="text-amber-400 text-4xl mb-4">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Alertas</h3>
                        <p class="text-gray-200">Mercado en tiempo real</p>
                    </div>

                    <!-- Videos Educativos Card -->
                    <div onclick="window.location.href='canal-videos-educativos.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-yellow-400">
                        <div class="text-blue-400 text-4xl mb-4">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Videos Educativos</h3>
                        <p class="text-gray-200">Cursos GERMAYORI FVG</p>
                        <div class="mt-2">
                            <span class="bg-yellow-500 text-black px-3 py-1 rounded-full text-xs font-bold">PREMIUM</span>
                        </div>
                    </div>

                    <!-- Mentoría Card -->
                    <div onclick="window.location.href='mentoria.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-violet-400">
                        <div class="text-violet-400 text-4xl mb-4">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Mentoría Directa</h3>
                        <p class="text-gray-200">Con el creador de GERMAYORI</p>
                        <div class="mt-2">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-bold">VIP EXCLUSIVO</span>
                        </div>
                    </div>

                    <!-- TradingView Card -->
                    <div onclick="window.location.href='tradingview.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer">
                        <div class="text-red-400 text-4xl mb-4">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">TradingView</h3>
                        <p class="text-gray-200">Gráficos profesionales</p>
                    </div>

                    <!-- Inversiones Card -->
                    <div onclick="window.location.href='canal-inversiones.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-yellow-400">
                        <div class="text-yellow-400 text-4xl mb-4">
                            <i class="fas fa-coins"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Inversiones</h3>
                        <p class="text-gray-200">4% mensual garantizado</p>
                        <div class="mt-2">
                            <span class="bg-yellow-500 text-black px-3 py-1 rounded-full text-xs font-bold">VIP ONLY</span>
                        </div>
                    </div>

                    <!-- VIP Inversores Card -->
                    <div onclick="window.location.href='canal-vip-inversores.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-gradient-to-r from-yellow-400 to-orange-500">
                        <div class="text-yellow-400 text-4xl mb-4">
                            <i class="fas fa-crown"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">VIP Inversores</h3>
                        <p class="text-gray-200">Panel exclusivo de inversores</p>
                        <div class="mt-2">
                            <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-xs font-bold">ELITE ACCESS</span>
                        </div>
                    </div>



                    <!-- Análisis y Certificaciones Card -->
                    <div onclick="window.location.href='canal-analisis.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-pink-400">
                        <div class="text-pink-400 text-4xl mb-4">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Análisis y Certificaciones</h3>
                        <p class="text-gray-200">Sube análisis y recibe certificaciones</p>
                        <div class="mt-2">
                            <span class="bg-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold">🏆 CERTIFICACIONES</span>
                        </div>
                    </div>

                    <!-- Dashboard Personal Card -->
                    <div onclick="window.location.href='dashboard-personal.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-yellow-400">
                        <div class="text-yellow-400 text-4xl mb-4">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Dashboard Personal</h3>
                        <p class="text-gray-200">Tu progreso y estadísticas</p>
                        <div class="mt-2">
                            <span class="bg-yellow-500 text-black px-3 py-1 rounded-full text-xs font-bold">📊 MI PERFIL</span>
                        </div>
                    </div>



                    <!-- Canal de Marketing Card -->
                    <div onclick="window.location.href='canal-marketing.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-red-400">
                        <div class="text-red-400 text-4xl mb-4">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Canal de Marketing</h3>
                        <p class="text-gray-200">Videos y equipo de network marketing</p>
                        <div class="mt-2">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">📢 EQUIPO</span>
                        </div>
                    </div>

                    <!-- Canal Videos Básicos Card -->
                    <div onclick="window.location.href='canal-videos-basicos.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-green-400">
                        <div class="text-green-400 text-4xl mb-4">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Videos Básicos</h3>
                        <p class="text-gray-200">Contenido educativo para principiantes</p>
                        <div class="mt-2">
                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">🟢 BÁSICO</span>
                        </div>
                    </div>

                    <!-- Canal Videos Intermedios Card -->
                    <div onclick="window.location.href='canal-videos-intermedios.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-purple-400">
                        <div class="text-purple-400 text-4xl mb-4">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Videos Intermedios</h3>
                        <p class="text-gray-200">Contenido educativo avanzado</p>
                        <div class="mt-2">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-bold">🟣 INTERMEDIO</span>
                        </div>
                    </div>

                    <!-- Canal Videos Avanzados Card -->
                    <div onclick="window.location.href='canal-videos-avanzados.html'" class="bg-white bg-opacity-20 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer border-2 border-red-500">
                        <div class="text-red-500 text-4xl mb-4">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2 text-white">Videos Avanzados</h3>
                        <p class="text-gray-200">Contenido educativo institucional</p>
                        <div class="mt-2">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">🔴 AVANZADO</span>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <script>
        // Función para cerrar sesión
        function logout() {
            // Limpiar localStorage
            localStorage.removeItem('germayori_user');
            localStorage.removeItem('germayori_selected_plan');
            localStorage.removeItem('germayori_token');

            // Confirmar logout
            alert('Sesión cerrada exitosamente');

            // Redirigir al login
            window.location.href = 'index.html';
        }

        // Verificar autenticación y plan al cargar
        window.onload = function() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return;
            }

            const userData = JSON.parse(user);
            const userPlan = userData.plan || 'basico';

            // Verificar expiración
            if (userData.expiryDate) {
                const now = new Date();
                const expiry = new Date(userData.expiryDate);
                if (now > expiry) {
                    alert('Tu suscripción ha expirado. Por favor, renueva tu plan.');
                    localStorage.clear();
                    window.location.href = 'index.html';
                    return;
                }
            }

            // Control de acceso por plan
            controlAccessByPlan(userPlan);
        };

        function controlAccessByPlan(plan) {
            // Definir qué puede acceder cada plan
            const planAccess = {
                'basico': [
                    'chat.html',
                    'trading-vivo.html',
                    'calculadora.html',
                    'noticias.html',
                    'senales.html',
                    'notificaciones.html',
                    'alertas.html',
                    'tradingview.html',
                    'dashboard-personal.html'
                ],
                'intermedio': [
                    'chat.html',
                    'trading-vivo.html',
                    'calculadora.html',
                    'noticias.html',
                    'senales.html',
                    'notificaciones.html',
                    'alertas.html',
                    'tradingview.html',
                    'canal-videos-basicos.html',
                    'dashboard-personal.html'
                ],
                'avanzado': [
                    'chat.html',
                    'trading-vivo.html',
                    'calculadora.html',
                    'noticias.html',
                    'senales.html',
                    'notificaciones.html',
                    'alertas.html',
                    'tradingview.html',
                    'canal-videos-basicos.html',
                    'canal-videos-intermedios.html',
                    'canal-videos-educativos.html',
                    'dashboard-personal.html'
                ],
                'completo': [] // Acceso a todo
            };

            const allowedPages = planAccess[plan] || planAccess['basico'];

            // Si no es plan completo, bloquear elementos no permitidos
            if (plan !== 'completo') {
                blockRestrictedElements(allowedPages);
            }
        }

        function blockRestrictedElements(allowedPages) {
            // Bloquear canales en sidebar
            const channelItems = document.querySelectorAll('.channel-item');
            channelItems.forEach(item => {
                const onclick = item.getAttribute('onclick');
                if (onclick) {
                    const page = onclick.match(/window\.location\.href='([^']+)'/);
                    if (page && !allowedPages.includes(page[1])) {
                        item.style.opacity = '0.5';
                        item.style.cursor = 'not-allowed';
                        item.onclick = function(e) {
                            e.preventDefault();
                            showUpgradeModal(page[1]);
                        };

                        // Agregar badge de BLOQUEADO
                        const badge = item.querySelector('.text-xs');
                        if (badge) {
                            badge.textContent = 'BLOQUEADO';
                            badge.className = 'text-xs bg-red-500 text-white px-2 py-1 rounded-full font-bold';
                        }
                    }
                }
            });

            // Bloquear cards en main content
            const cards = document.querySelectorAll('[onclick*="window.location.href"]');
            cards.forEach(card => {
                const onclick = card.getAttribute('onclick');
                if (onclick) {
                    const page = onclick.match(/window\.location\.href='([^']+)'/);
                    if (page && !allowedPages.includes(page[1])) {
                        card.style.opacity = '0.5';
                        card.style.cursor = 'not-allowed';
                        card.onclick = function(e) {
                            e.preventDefault();
                            showUpgradeModal(page[1]);
                        };

                        // Agregar overlay de bloqueo
                        const overlay = document.createElement('div');
                        overlay.className = 'absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg';
                        overlay.innerHTML = '<i class="fas fa-lock text-white text-2xl"></i>';
                        card.style.position = 'relative';
                        card.appendChild(overlay);
                    }
                }
            });
        }

        function showUpgradeModal(page) {
            const pageNames = {
                'canal-videos-educativos.html': 'Videos Educativos',
                'mentoria.html': 'Mentoría Directa',
                'canal-zoom-enlaces.html': 'Enlaces Zoom',
                'canal-analisis.html': 'Análisis y Certificaciones',
                'canal-inversiones.html': 'Inversiones',
                'canal-vip-inversores.html': 'VIP Inversores',
                'canal-marketing.html': 'Canal de Marketing',
                'canal-videos-basicos.html': 'Videos Básicos',
                'canal-videos-intermedios.html': 'Videos Intermedios',
                'canal-videos-avanzados.html': 'Videos Avanzados',
                'tradingview.html': 'TradingView'
            };

            const pageName = pageNames[page] || 'Esta función';

            alert(`🔒 ${pageName} requiere un plan superior.\n\n💰 Actualiza tu plan para acceder:\n• Intermedio: $70/mes\n• Avanzado: $95/mes\n• Completo: $140/mes\n\n📞 Contacta al soporte para actualizar.`);
        }
    </script>

</body>
</html>
