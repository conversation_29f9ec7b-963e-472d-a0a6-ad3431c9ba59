<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Calculadora</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="calculadora.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-calculator mr-3 text-green-400"></i>
                    <span>Calculadora</span>
                </a>

                <a href="noticias.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i>
                    <span>Noticias</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="notificaciones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-bell mr-3 text-pink-400"></i>
                    <span>Notificaciones</span>
                </a>

                <a href="alertas.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i>
                    <span>Alertas Mercado</span>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- Calculator Content -->
        <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">

            <!-- Header -->
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-white mb-2">🧮 Calculadora de Trading</h1>
                <p class="text-gray-200">Herramientas profesionales para calcular pips, profit y gestión de riesgo</p>
            </div>

            <!-- Calculadoras Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- Calculadora de Pips -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-green-600">💰 Calculadora de Pips</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Par de Divisas</label>
                            <select id="currency-pair" class="w-full p-3 border rounded-lg">
                                <option value="EURUSD">EUR/USD</option>
                                <option value="GBPUSD">GBP/USD</option>
                                <option value="USDJPY">USD/JPY</option>
                                <option value="AUDUSD">AUD/USD</option>
                                <option value="USDCAD">USD/CAD</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Tamaño del Lote</label>
                            <input type="number" id="lot-size" placeholder="0.01" step="0.01" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Pips</label>
                            <input type="number" id="pips" placeholder="10" class="w-full p-3 border rounded-lg">
                        </div>
                        <button onclick="calculatePips()" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 font-medium">
                            Calcular Ganancia/Pérdida
                        </button>
                        <div id="pip-result" class="hidden mt-4 p-4 bg-green-100 rounded-lg border border-green-200">
                            <p id="pip-result-text" class="text-green-800 font-bold text-lg"></p>
                            <p id="pip-details" class="text-green-600 text-sm mt-2"></p>
                        </div>
                    </div>
                </div>

                <!-- Calculadora de Riesgo -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-blue-600">⚖️ Gestión de Riesgo</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Capital Total ($)</label>
                            <input type="number" id="capital" placeholder="10000" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Riesgo por Operación (%)</label>
                            <input type="number" id="risk-percent" placeholder="2" step="0.1" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Stop Loss (pips)</label>
                            <input type="number" id="stop-loss-pips" placeholder="20" class="w-full p-3 border rounded-lg">
                        </div>
                        <button onclick="calculateRisk()" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-medium">
                            Calcular Tamaño de Posición
                        </button>
                        <div id="risk-result" class="hidden mt-4 p-4 bg-blue-100 rounded-lg border border-blue-200">
                            <p id="risk-result-text" class="text-blue-800 font-bold text-lg"></p>
                            <p id="risk-details" class="text-blue-600 text-sm mt-2"></p>
                        </div>
                    </div>
                </div>

                <!-- Calculadora Risk/Reward -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-purple-600">📊 Risk/Reward Ratio</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Precio de Entrada</label>
                            <input type="number" id="entry-price" placeholder="1.0850" step="0.0001" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Stop Loss</label>
                            <input type="number" id="stop-loss-price" placeholder="1.0820" step="0.0001" class="w-full p-3 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Take Profit</label>
                            <input type="number" id="take-profit-price" placeholder="1.0920" step="0.0001" class="w-full p-3 border rounded-lg">
                        </div>
                        <button onclick="calculateRiskReward()" class="w-full bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 font-medium">
                            Calcular Risk/Reward
                        </button>
                        <div id="rr-result" class="hidden mt-4 p-4 bg-purple-100 rounded-lg border border-purple-200">
                            <p id="rr-result-text" class="text-purple-800 font-bold text-lg"></p>
                            <p id="rr-details" class="text-purple-600 text-sm mt-2"></p>
                        </div>
                    </div>
                </div>

                <!-- Enlaces MyFXBook -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-orange-600">🔗 Herramientas Externas</h2>
                    <div class="space-y-4">
                        <a href="https://www.myfxbook.com/forex-calculators/profit-calculator" target="_blank"
                           class="block p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors border border-orange-200">
                            <div class="flex items-center">
                                <i class="fas fa-calculator text-orange-600 mr-3"></i>
                                <div>
                                    <div class="font-semibold text-orange-800">MyFXBook Calculadora</div>
                                    <div class="text-sm text-orange-600">Calculadora avanzada de profit/loss</div>
                                </div>
                            </div>
                        </a>

                        <a href="https://www.myfxbook.com/forex-economic-calendar" target="_blank"
                           class="block p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors border border-yellow-200">
                            <div class="flex items-center">
                                <i class="fas fa-calendar text-yellow-600 mr-3"></i>
                                <div>
                                    <div class="font-semibold text-yellow-800">Calendario Económico</div>
                                    <div class="text-sm text-yellow-600">Eventos que mueven el mercado</div>
                                </div>
                            </div>
                        </a>

                        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <h3 class="font-semibold text-gray-800 mb-2">💡 Consejos de Trading</h3>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Nunca arriesgues más del 2% de tu capital</li>
                                <li>• Usa siempre stop loss</li>
                                <li>• Mantén un ratio risk/reward mínimo de 1:2</li>
                                <li>• Practica con cuenta demo primero</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = checkAuth();
            if (user) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type.toUpperCase();
                document.getElementById('user-avatar').src = user.avatar;
            }
        }

        // Calculadora de Pips
        function calculatePips() {
            const pair = document.getElementById('currency-pair').value;
            const lotSize = parseFloat(document.getElementById('lot-size').value);
            const pips = parseFloat(document.getElementById('pips').value);

            if (!lotSize || !pips) {
                alert('Por favor completa todos los campos');
                return;
            }

            // Valor del pip (simplificado)
            let pipValue = 10; // Para pares con USD como moneda cotizada
            if (pair === 'USDJPY') {
                pipValue = 0.01; // Para JPY
            }

            const profit = lotSize * pips * pipValue;

            document.getElementById('pip-result-text').textContent = `💰 Resultado: $${profit.toFixed(2)} USD`;
            document.getElementById('pip-details').textContent = `${lotSize} lotes × ${pips} pips × $${pipValue}/pip = $${profit.toFixed(2)}`;
            document.getElementById('pip-result').classList.remove('hidden');
        }

        // Calculadora de Riesgo
        function calculateRisk() {
            const capital = parseFloat(document.getElementById('capital').value);
            const riskPercent = parseFloat(document.getElementById('risk-percent').value);
            const stopLossPips = parseFloat(document.getElementById('stop-loss-pips').value);

            if (!capital || !riskPercent || !stopLossPips) {
                alert('Por favor completa todos los campos');
                return;
            }

            const riskAmount = capital * (riskPercent / 100);
            const pipValue = 10; // Simplificado
            const lotSize = riskAmount / (stopLossPips * pipValue);

            document.getElementById('risk-result-text').textContent = `📊 Tamaño de posición: ${lotSize.toFixed(2)} lotes`;
            document.getElementById('risk-details').textContent = `Riesgo: $${riskAmount.toFixed(2)} (${riskPercent}% de $${capital})`;
            document.getElementById('risk-result').classList.remove('hidden');
        }

        // Calculadora Risk/Reward
        function calculateRiskReward() {
            const entry = parseFloat(document.getElementById('entry-price').value);
            const stopLoss = parseFloat(document.getElementById('stop-loss-price').value);
            const takeProfit = parseFloat(document.getElementById('take-profit-price').value);

            if (!entry || !stopLoss || !takeProfit) {
                alert('Por favor completa todos los campos');
                return;
            }

            const risk = Math.abs(entry - stopLoss);
            const reward = Math.abs(takeProfit - entry);
            const ratio = reward / risk;

            let color = 'red';
            let recommendation = 'No recomendado';
            if (ratio >= 2) {
                color = 'green';
                recommendation = 'Excelente ratio';
            } else if (ratio >= 1.5) {
                color = 'orange';
                recommendation = 'Ratio aceptable';
            }

            document.getElementById('rr-result-text').textContent = `⚖️ Risk/Reward: 1:${ratio.toFixed(2)}`;
            document.getElementById('rr-details').textContent = `${recommendation} - Riesgo: ${(risk * 10000).toFixed(1)} pips, Recompensa: ${(reward * 10000).toFixed(1)} pips`;
            document.getElementById('rr-result').classList.remove('hidden');
        }

        // Inicializar
        window.onload = function() {
            loadUserInfo();
        };
    </script>
</body>
</html>
