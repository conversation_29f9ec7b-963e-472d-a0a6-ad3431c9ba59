<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Registro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            backdrop-filter: blur(10px);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #1e3c72;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .logo p {
            color: #666;
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2a5298;
            background: white;
            box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.1);
        }

        .register-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(42, 82, 152, 0.3);
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
        }

        .login-link a {
            color: #2a5298;
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .plan-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #2a5298;
        }

        .plan-info h3 {
            color: #1e3c72;
            margin-bottom: 10px;
        }

        .plan-info p {
            color: #666;
            font-size: 14px;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            display: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
            display: none;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="logo">
            <h1>GERMAYORI</h1>
            <p>Únete a la comunidad de trading</p>
        </div>

        <div class="success-message" id="successMessage">
            ¡Registro exitoso! Redirigiendo al dashboard...
        </div>

        <div class="error-message" id="errorMessage">
            Error en el registro. Por favor, intenta nuevamente.
        </div>

        <div class="plan-info">
            <h3>Plan Básico - $45/mes</h3>
            <p>Acceso a señales, noticias, alertas, calculadora, notificaciones y trading en vivo</p>
        </div>

        <form id="registerForm">
            <div class="form-group">
                <label for="username">Nombre de Usuario</label>
                <input type="text" id="username" name="username" required minlength="3" maxlength="30">
            </div>

            <div class="form-group">
                <label for="email">Correo Electrónico</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Contraseña</label>
                <input type="password" id="password" name="password" required minlength="6">
            </div>

            <div class="form-group">
                <label for="confirmPassword">Confirmar Contraseña</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>

            <div class="form-group">
                <label for="plan">Plan de Suscripción</label>
                <select id="plan" name="plan" required>
                    <option value="basico">Básico - $45/mes</option>
                    <option value="intermedio">Intermedio - $70/mes</option>
                    <option value="avanzado">Avanzado - $95/mes</option>
                    <option value="completo">Completo - $140/mes</option>
                </select>
            </div>

            <div class="form-group">
                <label for="firstName">Nombre</label>
                <input type="text" id="firstName" name="firstName">
            </div>

            <div class="form-group">
                <label for="lastName">Apellido</label>
                <input type="text" id="lastName" name="lastName">
            </div>

            <div class="form-group">
                <label for="country">País</label>
                <input type="text" id="country" name="country">
            </div>

            <button type="submit" class="register-btn">Crear Cuenta</button>
        </form>

        <div class="login-link">
            <p>¿Ya tienes cuenta? <a href="login.html">Iniciar Sesión</a></p>
        </div>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');

            // Validar contraseñas
            if (password !== confirmPassword) {
                showError('Las contraseñas no coinciden');
                return;
            }

            const userData = {
                name: formData.get('username'),
                email: formData.get('email'),
                password: password
            };

            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok) {
                    showSuccess('¡Registro exitoso! Redirigiendo...');
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 2000);
                } else {
                    showError(result.message || 'Error en el registro');
                }
            } catch (error) {
                console.error('Error:', error);
                showError('Error de conexión. Por favor, intenta nuevamente.');
            }
        });

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');

            errorDiv.style.display = 'none';
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        function showError(message) {
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');

            successDiv.style.display = 'none';
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    </script>
</body>
</html>
