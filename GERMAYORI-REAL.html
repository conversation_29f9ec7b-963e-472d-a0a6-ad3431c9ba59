<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Trading Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        .admin-panel {
            background: linear-gradient(180deg, #0f3460 0%, #1a1a2e 100%);
            border-left: 2px solid #00d4ff;
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
        .chat-message {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <div onclick="showChannel('chat')" id="channel-chat" class="channel-item channel-active p-3 rounded cursor-pointer">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </div>

                <div onclick="showChannel('calculadora')" id="channel-calculadora" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-calculator mr-3 text-green-400"></i>
                    <span>Calculadora Pips</span>
                </div>

                <div onclick="showChannel('noticias')" id="channel-noticias" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i>
                    <span>Noticias Forex</span>
                </div>

                <div onclick="showChannel('senales')" id="channel-senales" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales Trading</span>
                </div>

                <div onclick="showChannel('notificaciones')" id="channel-notificaciones" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-bell mr-3 text-pink-400"></i>
                    <span>Notificaciones</span>
                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">3</span>
                </div>

                <div onclick="showChannel('alertas')" id="channel-alertas" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i>
                    <span>Alertas Mercado</span>
                    <span class="bg-amber-500 text-white text-xs px-2 py-1 rounded-full ml-2">2</span>
                </div>

                <div onclick="showChannel('academia')" id="channel-academia" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-graduation-cap mr-3 text-blue-400"></i>
                    <span>Academia</span>
                </div>

                <div onclick="showChannel('recursos')" id="channel-recursos" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-link mr-3 text-orange-400"></i>
                    <span>MyFXBook</span>
                </div>

                <div onclick="showChannel('tradingview')" id="channel-tradingview" class="channel-item p-3 rounded cursor-pointer">
                    <i class="fas fa-chart-area mr-3 text-red-400"></i>
                    <span>TradingView</span>
                </div>
            </div>

            <!-- Estado -->
            <div class="mt-8 p-3 bg-gray-800 rounded">
                <div class="text-xs text-gray-400 mb-2">Estado del Sistema</div>
                <div class="flex items-center text-green-400 text-sm">
                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    Online
                </div>
                <div class="flex items-center text-cyan-400 text-sm mt-1">
                    <div class="w-2 h-2 bg-cyan-400 rounded-full mr-2"></div>
                    Chat AI Activo
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex">

            <!-- Content Area -->
            <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">

                <!-- Chat Educativo -->
                <div id="content-chat" class="channel-content">
                    <div class="mb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">🤖 Chat Educativo GERMAYORI</h2>
                        <p class="text-gray-600">Tu asistente personal de trading forex</p>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg h-96">
                        <div class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white p-4 rounded-t-lg">
                            <h3 class="font-bold">💬 Conversación</h3>
                        </div>

                        <div id="chat-messages" class="h-64 p-4 overflow-y-auto space-y-3">
                            <div class="flex justify-start">
                                <div class="max-w-xs p-3 rounded-lg bg-gray-200 text-gray-800 chat-message">
                                    ¡Hola! Soy tu asistente GERMAYORI. ¿En qué puedo ayudarte a aprender sobre forex?
                                </div>
                            </div>
                        </div>

                        <div class="p-4 border-t">
                            <div class="flex gap-2">
                                <input
                                    type="text"
                                    id="chat-input"
                                    placeholder="Pregunta sobre forex, análisis técnico, gestión de riesgo..."
                                    class="flex-1 p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500"
                                    onkeypress="if(event.key==='Enter') sendMessage()"
                                />
                                <button
                                    onclick="sendMessage()"
                                    id="send-button"
                                    class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-cyan-600 hover:to-blue-700 transition-all"
                                >
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calculadora -->
                <div id="content-calculadora" class="channel-content hidden">
                    <div class="mb-4">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">🧮 Calculadora de Trading</h2>
                        <p class="text-gray-600">Herramientas profesionales para calcular pips, profit y riesgo</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Calculadora de Pips -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-green-600">💰 Calculadora de Pips</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Tamaño del Lote</label>
                                    <input type="number" id="lot-size" placeholder="0.01" class="w-full p-3 border rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Pips</label>
                                    <input type="number" id="pips" placeholder="10" class="w-full p-3 border rounded-lg">
                                </div>
                                <button onclick="calculatePips()" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700">
                                    Calcular Ganancia/Pérdida
                                </button>
                                <div id="calc-result" class="hidden mt-4 p-4 bg-green-100 rounded-lg">
                                    <p id="result-text" class="text-green-800 font-bold"></p>
                                </div>
                            </div>
                        </div>

                        <!-- Enlaces MyFXBook -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-blue-600">🔗 Herramientas MyFXBook</h3>
                            <div class="space-y-3">
                                <a href="https://www.myfxbook.com/forex-calculators/profit-calculator" target="_blank"
                                   class="block p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="font-semibold text-blue-800">Calculadora Avanzada</div>
                                    <div class="text-sm text-blue-600">Profit, Loss, Margin, Pip Value</div>
                                </a>
                                <a href="https://www.myfxbook.com/forex-economic-calendar" target="_blank"
                                   class="block p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                                    <div class="font-semibold text-yellow-800">Calendario Económico</div>
                                    <div class="text-sm text-yellow-600">Eventos que mueven el mercado</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Otros canales (placeholder) -->
                <div id="content-noticias" class="channel-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">📰 Noticias Forex</h2>
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <iframe src="https://www.myfxbook.com/forex-economic-calendar"
                                class="w-full h-96 border-0 rounded"></iframe>
                    </div>
                </div>

                <div id="content-senales" class="channel-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">📈 Señales de Trading</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Señales Activas -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-purple-600">🔥 Señales Activas</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-green-500 pl-4 bg-green-50 p-3 rounded">
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="font-bold text-green-700">EUR/USD</span>
                                        <span class="bg-green-500 text-white px-2 py-1 rounded text-xs">BUY</span>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <div>Entrada: 1.0850</div>
                                        <div>Stop Loss: 1.0820</div>
                                        <div>Take Profit: 1.0920</div>
                                        <div class="text-green-600 font-semibold">+45 pips</div>
                                    </div>
                                </div>

                                <div class="border-l-4 border-blue-500 pl-4 bg-blue-50 p-3 rounded">
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="font-bold text-blue-700">GBP/USD</span>
                                        <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs">SELL</span>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <div>Entrada: 1.2650</div>
                                        <div>Stop Loss: 1.2680</div>
                                        <div>Take Profit: 1.2580</div>
                                        <div class="text-blue-600 font-semibold">Pendiente</div>
                                    </div>
                                </div>

                                <div class="border-l-4 border-red-500 pl-4 bg-red-50 p-3 rounded">
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="font-bold text-red-700">USD/JPY</span>
                                        <span class="bg-red-500 text-white px-2 py-1 rounded text-xs">SELL</span>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <div>Entrada: 149.50</div>
                                        <div>Stop Loss: 150.00</div>
                                        <div>Take Profit: 148.50</div>
                                        <div class="text-red-600 font-semibold">-15 pips</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Estadísticas -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-purple-600">📊 Estadísticas del Día</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center p-3 bg-green-50 rounded">
                                    <span class="text-green-700">Señales Ganadoras</span>
                                    <span class="font-bold text-green-700">7</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-red-50 rounded">
                                    <span class="text-red-700">Señales Perdedoras</span>
                                    <span class="font-bold text-red-700">2</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-blue-50 rounded">
                                    <span class="text-blue-700">Tasa de Éxito</span>
                                    <span class="font-bold text-blue-700">78%</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-purple-50 rounded">
                                    <span class="text-purple-700">Pips Totales</span>
                                    <span class="font-bold text-purple-700">+125</span>
                                </div>
                            </div>

                            <div class="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                                    <span class="font-semibold text-yellow-800">Aviso Educativo</span>
                                </div>
                                <p class="text-sm text-yellow-700">
                                    Estas señales son solo para fines educativos. Siempre haz tu propio análisis antes de operar.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="content-academia" class="channel-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">🎓 Academia GERMAYORI</h2>
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <p class="text-gray-600">Próximamente: Cursos y tutoriales de trading</p>
                    </div>
                </div>

                <div id="content-recursos" class="channel-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">🔗 Recursos MyFXBook</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="https://www.myfxbook.com/forex-calculators/profit-calculator" target="_blank"
                           class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
                            <h3 class="font-bold text-blue-600 mb-2">🧮 Calculadoras</h3>
                            <p class="text-gray-600">Herramientas profesionales de cálculo</p>
                        </a>
                        <a href="https://www.myfxbook.com/forex-economic-calendar" target="_blank"
                           class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
                            <h3 class="font-bold text-yellow-600 mb-2">📅 Calendario</h3>
                            <p class="text-gray-600">Eventos económicos importantes</p>
                        </a>
                    </div>
                </div>

                <!-- Notificaciones -->
                <div id="content-notificaciones" class="channel-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">🔔 Notificaciones</h2>

                    <div class="space-y-4">
                        <!-- Notificaciones Recientes -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-pink-600">📬 Notificaciones Recientes</h3>
                            <div class="space-y-3">
                                <div class="flex items-start p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                                    <i class="fas fa-info-circle text-blue-500 mr-3 mt-1"></i>
                                    <div>
                                        <div class="font-semibold text-blue-800">Nueva señal EUR/USD</div>
                                        <div class="text-sm text-blue-600">Se ha generado una nueva señal de compra</div>
                                        <div class="text-xs text-gray-500">Hace 5 minutos</div>
                                    </div>
                                </div>

                                <div class="flex items-start p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                                    <div>
                                        <div class="font-semibold text-green-800">Take Profit alcanzado</div>
                                        <div class="text-sm text-green-600">GBP/USD +45 pips de ganancia</div>
                                        <div class="text-xs text-gray-500">Hace 15 minutos</div>
                                    </div>
                                </div>

                                <div class="flex items-start p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-3 mt-1"></i>
                                    <div>
                                        <div class="font-semibold text-yellow-800">Evento económico importante</div>
                                        <div class="text-sm text-yellow-600">NFP en 30 minutos - Alta volatilidad esperada</div>
                                        <div class="text-xs text-gray-500">Hace 30 minutos</div>
                                    </div>
                                </div>

                                <div class="flex items-start p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                                    <i class="fas fa-times-circle text-red-500 mr-3 mt-1"></i>
                                    <div>
                                        <div class="font-semibold text-red-800">Stop Loss activado</div>
                                        <div class="text-sm text-red-600">USD/JPY -20 pips</div>
                                        <div class="text-xs text-gray-500">Hace 1 hora</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configuración de Notificaciones -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-pink-600">⚙️ Configuración</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <span class="font-medium">Señales de Trading</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <span class="font-medium">Noticias Económicas</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <span class="font-medium">Alertas de Mercado</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alertas del Mercado -->
                <div id="content-alertas" class="channel-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">⚠️ Alertas del Mercado</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Alertas Activas -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-amber-600">🚨 Alertas Activas</h3>
                            <div class="space-y-4">
                                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                        <span class="font-bold text-red-700">Alta Volatilidad</span>
                                    </div>
                                    <div class="text-sm text-red-600">
                                        <div>Par: EUR/USD</div>
                                        <div>Volatilidad: 150% sobre promedio</div>
                                        <div>Causa: Decisión BCE</div>
                                    </div>
                                </div>

                                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-chart-line text-yellow-500 mr-2"></i>
                                        <span class="font-bold text-yellow-700">Ruptura de Resistencia</span>
                                    </div>
                                    <div class="text-sm text-yellow-600">
                                        <div>Par: GBP/USD</div>
                                        <div>Nivel: 1.2700</div>
                                        <div>Confirmación: Pendiente</div>
                                    </div>
                                </div>

                                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-clock text-blue-500 mr-2"></i>
                                        <span class="font-bold text-blue-700">Evento Próximo</span>
                                    </div>
                                    <div class="text-sm text-blue-600">
                                        <div>Evento: NFP USA</div>
                                        <div>Tiempo: 15 minutos</div>
                                        <div>Impacto: Alto</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configurar Alertas -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-lg font-bold mb-4 text-amber-600">⚙️ Configurar Alertas</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Par de Divisas</label>
                                    <select class="w-full p-3 border rounded-lg">
                                        <option>EUR/USD</option>
                                        <option>GBP/USD</option>
                                        <option>USD/JPY</option>
                                        <option>AUD/USD</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium mb-2">Tipo de Alerta</label>
                                    <select class="w-full p-3 border rounded-lg">
                                        <option>Precio alcanzado</option>
                                        <option>Ruptura de soporte/resistencia</option>
                                        <option>Alta volatilidad</option>
                                        <option>Evento económico</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium mb-2">Valor/Nivel</label>
                                    <input type="number" step="0.0001" placeholder="1.0850" class="w-full p-3 border rounded-lg">
                                </div>

                                <button class="w-full bg-amber-600 text-white py-3 rounded-lg hover:bg-amber-700">
                                    Crear Alerta
                                </button>
                            </div>

                            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                                <h4 class="font-semibold mb-2">📊 Estadísticas</h4>
                                <div class="text-sm space-y-1">
                                    <div class="flex justify-between">
                                        <span>Alertas Activas:</span>
                                        <span class="font-bold">8</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Activadas Hoy:</span>
                                        <span class="font-bold">12</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Precisión:</span>
                                        <span class="font-bold text-green-600">89%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="content-tradingview" class="channel-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">📊 TradingView</h2>
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <iframe src="https://es.tradingview.com/chart/"
                                class="w-full h-96 border-0 rounded"></iframe>
                    </div>
                </div>

            </div>

            <!-- Admin Panel -->
            <div class="admin-panel w-80 text-white p-4 overflow-y-auto">
                <div class="mb-6">
                    <h3 class="text-lg font-bold text-cyan-400 mb-4">⚙️ Panel de Control</h3>

                    <!-- Usuario -->
                    <div class="bg-gray-800 rounded-lg p-4 mb-4">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-cyan-400 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-gray-800"></i>
                            </div>
                            <div>
                                <div class="font-semibold">Usuario GERMAYORI</div>
                                <div class="text-xs text-gray-400">Premium Account</div>
                            </div>
                        </div>
                    </div>

                    <!-- Estadísticas -->
                    <div class="bg-gray-800 rounded-lg p-4 mb-4">
                        <h4 class="font-semibold mb-3 text-cyan-400">📊 Estadísticas</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>Mensajes Chat:</span>
                                <span class="text-green-400">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Cálculos:</span>
                                <span class="text-blue-400">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Sesión:</span>
                                <span class="text-yellow-400">Activa</span>
                            </div>
                        </div>
                    </div>

                    <!-- Acciones Rápidas -->
                    <div class="bg-gray-800 rounded-lg p-4">
                        <h4 class="font-semibold mb-3 text-cyan-400">⚡ Acciones Rápidas</h4>
                        <div class="space-y-2">
                            <button onclick="showChannel('calculadora')" class="w-full bg-green-600 text-white py-2 rounded text-sm hover:bg-green-700">
                                🧮 Calculadora
                            </button>
                            <button onclick="showChannel('noticias')" class="w-full bg-yellow-600 text-white py-2 rounded text-sm hover:bg-yellow-700">
                                📰 Noticias
                            </button>
                            <button onclick="showChannel('tradingview')" class="w-full bg-red-600 text-white py-2 rounded text-sm hover:bg-red-700">
                                📊 TradingView
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        // Variables globales
        let isLoading = false;
        let messageCount = 0;
        let calcCount = 0;

        // Función para cambiar canales
        function showChannel(channelName) {
            // Ocultar todos los contenidos
            document.querySelectorAll('.channel-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Mostrar el contenido seleccionado
            document.getElementById('content-' + channelName).classList.remove('hidden');

            // Actualizar estilos de canales
            document.querySelectorAll('.channel-item').forEach(item => {
                item.classList.remove('channel-active');
            });

            document.getElementById('channel-' + channelName).classList.add('channel-active');
        }

        // Función para enviar mensaje
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message || isLoading) return;

            addMessage(message, 'user');
            input.value = '';
            messageCount++;
            updateStats();

            isLoading = true;
            document.getElementById('send-button').disabled = true;

            // Simular respuesta del AI
            setTimeout(() => {
                let response = getAIResponse(message);
                addMessage(response, 'bot');
                isLoading = false;
                document.getElementById('send-button').disabled = false;
            }, 1000);
        }

        // Función para obtener respuesta del AI
        function getAIResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('hola') || lowerMessage.includes('hello')) {
                return '¡Hola! Soy tu asistente GERMAYORI. ¿En qué puedo ayudarte a aprender sobre forex y trading?';
            } else if (lowerMessage.includes('forex') || lowerMessage.includes('divisas')) {
                return 'Forex es el mercado de divisas más grande del mundo. Funciona 24/5 y permite intercambiar monedas. ¿Te gustaría aprender sobre análisis técnico o fundamental?';
            } else if (lowerMessage.includes('pip')) {
                return 'Un pip es la unidad mínima de cambio en forex. Para EUR/USD, 1 pip = 0.0001. El valor del pip depende del tamaño de tu posición. ¿Quieres que calculemos algunos ejemplos?';
            } else if (lowerMessage.includes('riesgo') || lowerMessage.includes('risk')) {
                return 'La gestión de riesgo es fundamental en trading. Regla básica: nunca arriesgues más del 2% de tu capital por operación. Usa siempre stop loss y calcula tu risk/reward antes de entrar.';
            } else if (lowerMessage.includes('soporte') || lowerMessage.includes('resistencia')) {
                return 'Soportes y resistencias son niveles clave donde el precio tiende a rebotar. Soporte = nivel donde el precio encuentra compras. Resistencia = nivel donde encuentra ventas. ¡Úsalos para tus entradas!';
            } else if (lowerMessage.includes('rsi')) {
                return 'El RSI (Relative Strength Index) mide la fuerza del precio. Valores > 70 = sobrecompra, < 30 = sobreventa. Es útil para identificar posibles reversiones, pero úsalo con otros indicadores.';
            } else if (lowerMessage.includes('media') || lowerMessage.includes('moving average')) {
                return 'Las medias móviles suavizan el precio y muestran la tendencia. MA20 para corto plazo, MA50 para medio, MA200 para largo. Cuando el precio está arriba = tendencia alcista.';
            } else {
                return 'Excelente pregunta sobre trading. Te recomiendo usar nuestras herramientas: calculadora de pips, calendario económico de MyFXBook, y TradingView para análisis. ¿Hay algo específico que quieras aprender?';
            }
        }

        // Función para agregar mensaje al chat
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex ' + (sender === 'user' ? 'justify-end' : 'justify-start');

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'max-w-xs p-3 rounded-lg chat-message ' +
                (sender === 'user' ? 'bg-cyan-500 text-white' : 'bg-gray-200 text-gray-800');
            bubbleDiv.textContent = text;

            messageDiv.appendChild(bubbleDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Función para calcular pips
        function calculatePips() {
            const lotSize = parseFloat(document.getElementById('lot-size').value);
            const pips = parseFloat(document.getElementById('pips').value);

            if (lotSize && pips) {
                const profit = lotSize * pips * 10;
                document.getElementById('result-text').textContent = '💰 Resultado: $' + profit.toFixed(2) + ' USD';
                document.getElementById('calc-result').classList.remove('hidden');
                calcCount++;
                updateStats();
            } else {
                alert('Por favor ingresa valores válidos para el lote y los pips.');
            }
        }

        // Función para actualizar estadísticas
        function updateStats() {
            const statsContainer = document.querySelector('.admin-panel');
            if (statsContainer) {
                const messageSpan = statsContainer.querySelector('.text-green-400');
                const calcSpan = statsContainer.querySelector('.text-blue-400');
                if (messageSpan) messageSpan.textContent = messageCount;
                if (calcSpan) calcSpan.textContent = calcCount;
            }
        }

        // Inicializar
        console.log('🚀 GERMAYORI Trading Platform - Iniciada correctamente');
    </script>
</body>
</html>
