<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Trading Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
        }
        .btn-primary {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
        }
        .floating {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .hidden { display: none; }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center">

    <!-- Login/Register Screen -->
    <div id="auth-screen" class="w-full max-w-md">
        <div class="glass rounded-2xl p-8 glow">
            <!-- Logo -->
            <div class="text-center mb-8">
                <div class="floating">
                    <h1 class="text-4xl font-black text-white mb-2">
                        <i class="fas fa-rocket mr-3 text-cyan-400"></i>GERMAYORI
                    </h1>
                </div>
                <p class="text-white/80">Trading Platform</p>
            </div>

            <!-- Login Form -->
            <div id="login-form">
                <h2 class="text-2xl font-bold text-white mb-6 text-center">Iniciar Sesión</h2>
                <form onsubmit="login(event)">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-white/80 text-sm font-medium mb-2">Email</label>
                            <input
                                type="email"
                                id="login-email"
                                required
                                class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                                placeholder="<EMAIL>"
                            />
                        </div>
                        <div>
                            <label class="block text-white/80 text-sm font-medium mb-2">Contraseña</label>
                            <input
                                type="password"
                                id="login-password"
                                required
                                class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                                placeholder="••••••••"
                            />
                        </div>
                        <button
                            type="submit"
                            class="w-full btn-primary text-white font-bold py-3 rounded-lg"
                        >
                            <i class="fas fa-sign-in-alt mr-2"></i>Entrar
                        </button>
                    </div>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-white/60">¿No tienes cuenta?</p>
                    <button
                        onclick="showPlans()"
                        class="text-cyan-400 hover:text-cyan-300 font-medium"
                    >
                        Ver Planes y Crear Cuenta
                    </button>
                </div>

                <!-- Demo Accounts -->
                <div class="mt-6 p-4 bg-white/5 rounded-lg">
                    <p class="text-white/80 text-sm mb-3">Cuentas de prueba:</p>
                    <div class="space-y-2 text-xs">
                        <div class="flex justify-between">
                            <span class="text-white/60">Premium:</span>
                            <span class="text-cyan-400"><EMAIL> / 123456</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-white/60">Usuario:</span>
                            <span class="text-cyan-400"><EMAIL> / 123456</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plans Selection -->
            <div id="plans-screen" class="hidden">
                <h2 class="text-2xl font-bold text-white mb-6 text-center">Elige tu Plan GERMAYORI</h2>

                <!-- Plan GERMAYORI Básico -->
                <div class="space-y-4 mb-6">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-5 cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all border-2 border-blue-300" onclick="selectPlan('germayori-basico')">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-2xl font-bold text-white">📊 GERMAYORI BÁSICO</h3>
                            <span class="text-3xl font-bold text-white">$45/mes</span>
                        </div>
                        <div class="text-white/95 text-sm space-y-2">
                            <div class="font-semibold text-yellow-200 mb-2">✅ CANALES INCLUIDOS:</div>
                            <div>✅ 💬 Canal de Chat GERMAYORI</div>
                            <div>✅ 📈 Canal de Señales</div>
                            <div>✅ 📰 Canal de Noticias</div>
                            <div>✅ 🚨 Canal de Alertas</div>
                            <div>✅ 🧮 Canal de Calculadora</div>
                            <div>✅ 🔔 Canal de Notificaciones</div>
                            <div>✅ 📹 Canal de Trading en Vivo</div>
                            <div>✅ 📊 TradingView</div>
                            <div>✅ 👤 Dashboard Personal</div>
                            <div class="font-semibold text-red-200 mt-3 mb-1">❌ BLOQUEADO:</div>
                            <div>❌ Videos Educativos Premium</div>
                            <div>❌ Mentorías Zoom VIP</div>
                            <div>❌ Canal de Inversiones</div>
                            <div>❌ Análisis Personalizados</div>
                        </div>
                    </div>

                    <!-- Plan GERMAYORI Completo -->
                    <div class="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg p-5 cursor-pointer hover:from-yellow-600 hover:to-orange-600 transition-all border-4 border-yellow-300 relative" onclick="selectPlan('germayori-completo')">
                        <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                            <span class="bg-red-500 text-white px-4 py-1 rounded-full text-xs font-bold animate-pulse">🔥 MÁS POPULAR</span>
                        </div>
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-2xl font-bold text-white">👑 GERMAYORI COMPLETO</h3>
                            <span class="text-3xl font-bold text-white">$125/mes</span>
                        </div>
                        <div class="text-white/95 text-sm space-y-2">
                            <div class="font-semibold text-yellow-200 mb-2">✅ ACCESO TOTAL A TODO:</div>
                            <div>✅ 💬 Canal de Chat GERMAYORI</div>
                            <div>✅ 📈 Canal de Señales</div>
                            <div>✅ 📰 Canal de Noticias</div>
                            <div>✅ 🚨 Canal de Alertas</div>
                            <div>✅ 🧮 Canal de Calculadora</div>
                            <div>✅ 🔔 Canal de Notificaciones</div>
                            <div>✅ 📹 Canal de Trading en Vivo</div>
                            <div class="font-semibold text-green-200 mt-3 mb-1">🎯 PREMIUM EXCLUSIVO:</div>
                            <div>✅ 🎓 Videos Educativos Premium</div>
                            <div>✅ 📹 Mentorías Zoom VIP</div>
                            <div>✅ 💰 Canal de Inversiones</div>
                            <div>✅ 📊 Análisis Personalizados</div>
                            <div>✅ 🏆 Certificaciones GERMAYORI</div>
                            <div>✅ 👑 Soporte VIP Prioritario</div>
                            <div class="text-yellow-200 font-bold text-center mt-3 p-2 bg-black/30 rounded">🚀 SIN RESTRICCIONES</div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button
                        onclick="showLogin()"
                        class="text-cyan-400 hover:text-cyan-300 font-medium text-sm"
                    >
                        ← Volver al Login
                    </button>
                </div>
            </div>

            <!-- Register Form -->
            <div id="register-form" class="hidden">
                <h2 class="text-2xl font-bold text-white mb-4 text-center">Crear Cuenta</h2>
                <div id="selected-plan-info" class="mb-4 p-3 bg-white/10 rounded-lg">
                    <div class="text-center">
                        <span class="text-white font-bold" id="plan-name">Plan Seleccionado</span>
                        <span class="text-cyan-400 font-bold ml-2" id="plan-price">$0/mes</span>
                    </div>
                </div>

                <form onsubmit="register(event)">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-white/80 text-sm font-medium mb-2">Nombre</label>
                            <input
                                type="text"
                                id="register-name"
                                required
                                class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                                placeholder="Tu nombre"
                            />
                        </div>
                        <div>
                            <label class="block text-white/80 text-sm font-medium mb-2">Email</label>
                            <input
                                type="email"
                                id="register-email"
                                required
                                class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                                placeholder="<EMAIL>"
                            />
                        </div>
                        <div>
                            <label class="block text-white/80 text-sm font-medium mb-2">Contraseña</label>
                            <input
                                type="password"
                                id="register-password"
                                required
                                class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                                placeholder="••••••••"
                            />
                        </div>
                        <button
                            type="submit"
                            class="w-full btn-primary text-white font-bold py-3 rounded-lg"
                        >
                            <i class="fas fa-user-plus mr-2"></i>Crear Cuenta y Proceder al Pago
                        </button>
                    </div>
                </form>

                <div class="mt-6 text-center">
                    <button
                        onclick="showPlans()"
                        class="text-cyan-400 hover:text-cyan-300 font-medium text-sm"
                    >
                        ← Cambiar Plan
                    </button>
                    <span class="text-white/40 mx-2">|</span>
                    <button
                        onclick="showLogin()"
                        class="text-cyan-400 hover:text-cyan-300 font-medium text-sm"
                    >
                        Ya tengo cuenta
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentUser = null;
        let selectedPlan = null;

        // Planes disponibles GERMAYORI
        const plans = {
            'germayori-basico': {
                name: '📊 GERMAYORI BÁSICO',
                price: 45,
                features: [
                    'Canal de Chat GERMAYORI',
                    'Canal de Señales',
                    'Canal de Noticias',
                    'Canal de Alertas',
                    'Canal de Calculadora',
                    'Canal de Notificaciones',
                    'Canal de Trading en Vivo',
                    'TradingView',
                    'Dashboard Personal'
                ],
                blocked: [
                    'Videos Educativos Premium',
                    'Mentorías Zoom VIP',
                    'Canal de Inversiones',
                    'Análisis Personalizados'
                ]
            },
            'germayori-completo': {
                name: '👑 GERMAYORI COMPLETO',
                price: 125,
                features: [
                    'ACCESO TOTAL A TODOS LOS CANALES',
                    'Canal de Chat GERMAYORI',
                    'Canal de Señales',
                    'Canal de Noticias',
                    'Canal de Alertas',
                    'Canal de Calculadora',
                    'Canal de Notificaciones',
                    'Canal de Trading en Vivo',
                    'Videos Educativos Premium',
                    'Mentorías Zoom VIP',
                    'Canal de Inversiones',
                    'Análisis Personalizados',
                    'Certificaciones GERMAYORI',
                    'Soporte VIP Prioritario'
                ],
                blocked: []
            }
        };

        // Usuarios de prueba
        const users = {
            '<EMAIL>': {
                password: '123456',
                name: 'jhon0608 GERMAYORI',
                type: 'premium',
                plan: 'germayori-completo',
                planPrice: 125,
                avatar: 'https://ui-avatars.com/api/?name=jhon0608&background=00d4ff&color=fff'
            },
            '<EMAIL>': {
                password: '123456',
                name: 'Usuario Demo',
                type: 'paid',
                plan: 'germayori-basico',
                planPrice: 45,
                avatar: 'https://ui-avatars.com/api/?name=User&background=0099cc&color=fff'
            }
        };

        // Función de login
        function login(event) {
            event.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            if (users[email] && users[email].password === password) {
                currentUser = {
                    email: email,
                    ...users[email]
                };

                // Guardar en localStorage
                localStorage.setItem('germayori_user', JSON.stringify(currentUser));

                // Redirigir a dashboard
                window.location.href = 'dashboard-simple.html';
            } else {
                alert('Email o contraseña incorrectos');
            }
        }

        // Función de registro
        function register(event) {
            event.preventDefault();

            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;

            if (users[email]) {
                alert('Este email ya está registrado');
                return;
            }

            if (!selectedPlan) {
                alert('Por favor selecciona un plan primero');
                return;
            }

            // Crear nuevo usuario con plan seleccionado
            users[email] = {
                password: password,
                name: name,
                type: selectedPlan === 'germayori-completo' ? 'premium' : 'paid',
                plan: selectedPlan,
                planPrice: plans[selectedPlan].price,
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=0099cc&color=fff`
            };

            currentUser = {
                email: email,
                ...users[email]
            };

            // Guardar en localStorage
            localStorage.setItem('germayori_user', JSON.stringify(currentUser));
            localStorage.setItem('germayori_selected_plan', selectedPlan);

            alert(`Cuenta creada exitosamente con plan ${plans[selectedPlan].name}. Procede al pago de $${plans[selectedPlan].price}/mes`);

            // Redirigir a página de pago específica para planes
            window.location.href = 'pago-planes.html';
        }

        // Función para seleccionar plan
        function selectPlan(planType) {
            selectedPlan = planType;
            const plan = plans[planType];

            document.getElementById('plan-name').textContent = plan.name;
            document.getElementById('plan-price').textContent = `$${plan.price}/mes`;

            showRegister();
        }

        // Función para mostrar planes
        function showPlans() {
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('register-form').classList.add('hidden');
            document.getElementById('plans-screen').classList.remove('hidden');
        }

        // Funciones para cambiar entre login y registro
        function showRegister() {
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('plans-screen').classList.add('hidden');
            document.getElementById('register-form').classList.remove('hidden');
        }

        function showLogin() {
            document.getElementById('register-form').classList.add('hidden');
            document.getElementById('plans-screen').classList.add('hidden');
            document.getElementById('login-form').classList.remove('hidden');
        }

        // Verificar si ya está logueado
        window.onload = function() {
            const savedUser = localStorage.getItem('germayori_user');
            if (savedUser) {
                window.location.href = 'dashboard-simple.html';
            }
        };
    </script>
</body>
</html>
