<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Alertas</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>
                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i><span>Chat Educativo</span>
                </a>
                <a href="calculadora.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-calculator mr-3 text-green-400"></i><span>Calculadora</span>
                </a>
                <a href="noticias.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i><span>Noticias</span>
                </a>
                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i><span>Señales</span>
                </a>
                <a href="notificaciones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-bell mr-3 text-pink-400"></i><span>Notificaciones</span>
                </a>
                <a href="alertas.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i><span>Alertas Mercado</span>
                    <span class="bg-amber-500 text-white text-xs px-2 py-1 rounded-full ml-2">2</span>
                </a>
            </div>

            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- Alerts Content -->
        <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-white mb-2">⚠️ Alertas del Mercado</h1>
                <p class="text-gray-200">Alertas personalizables en tiempo real</p>
            </div>

            <!-- Alertas en Tiempo Real - Sistema Interno -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-bold mb-4 text-red-600">🔴 Alertas en Tiempo Real</h2>
                <div class="bg-gray-100 rounded-lg p-4">
                    <!-- Feed de Alertas en Vivo -->
                    <div id="live-alerts-feed" class="space-y-3 max-h-96 overflow-y-auto">
                        <div class="text-center py-8">
                            <i class="fas fa-satellite-dish text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">Haz clic en "Iniciar Alertas" para comenzar el feed en tiempo real</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button onclick="startLiveAlerts()" id="alerts-btn" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                        <i class="fas fa-play mr-2"></i>Iniciar Alertas en Vivo
                    </button>
                    <button onclick="stopLiveAlerts()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 ml-2">
                        <i class="fas fa-stop mr-2"></i>Detener
                    </button>
                    <p class="text-sm text-gray-600 mt-2">📡 Sistema de alertas integrado en GERMAYORI</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Active Alerts -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-amber-600">🚨 Alertas Activas</h2>
                    <div class="space-y-4">
                        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                <span class="font-bold text-red-700">Alta Volatilidad</span>
                            </div>
                            <div class="text-sm text-red-600">
                                <div>Par: EUR/USD</div>
                                <div>Volatilidad: 150% sobre promedio</div>
                                <div>Causa: Decisión BCE</div>
                            </div>
                        </div>

                        <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-chart-line text-yellow-500 mr-2"></i>
                                <span class="font-bold text-yellow-700">Ruptura de Resistencia</span>
                            </div>
                            <div class="text-sm text-yellow-600">
                                <div>Par: GBP/USD</div>
                                <div>Nivel: 1.2700</div>
                                <div>Confirmación: Pendiente</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Alert -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-amber-600">⚙️ Configurar Alertas</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Par de Divisas</label>
                            <select class="w-full p-3 border rounded-lg">
                                <option>EUR/USD</option>
                                <option>GBP/USD</option>
                                <option>USD/JPY</option>
                                <option>AUD/USD</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Tipo de Alerta</label>
                            <select class="w-full p-3 border rounded-lg">
                                <option>Precio alcanzado</option>
                                <option>Ruptura de soporte/resistencia</option>
                                <option>Alta volatilidad</option>
                                <option>Evento económico</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Valor/Nivel</label>
                            <input type="number" step="0.0001" placeholder="1.0850" class="w-full p-3 border rounded-lg">
                        </div>

                        <button class="w-full bg-amber-600 text-white py-3 rounded-lg hover:bg-amber-700">
                            Crear Alerta
                        </button>
                    </div>

                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="font-semibold mb-2">📊 Estadísticas</h3>
                        <div class="text-sm space-y-1">
                            <div class="flex justify-between">
                                <span>Alertas Activas:</span>
                                <span class="font-bold">8</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Activadas Hoy:</span>
                                <span class="font-bold">12</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Precisión:</span>
                                <span class="font-bold text-green-600">89%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        function loadUserInfo() {
            const user = checkAuth();
            if (user) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type.toUpperCase();
                document.getElementById('user-avatar').src = user.avatar;
            }
        }

        // Sistema de Alertas en Tiempo Real
        let alertsInterval;
        let alertsRunning = false;
        let alertCounter = 0;

        // Base de datos de alertas simuladas
        const alertsDatabase = [
            { type: 'price', symbol: 'EUR/USD', message: 'Ruptura de resistencia en 1.0850', level: 'high', color: 'red' },
            { type: 'news', symbol: 'USD', message: 'NFP mejor de lo esperado: +250K vs +200K', level: 'high', color: 'red' },
            { type: 'technical', symbol: 'GBP/USD', message: 'RSI en sobrecompra (>70)', level: 'medium', color: 'orange' },
            { type: 'volume', symbol: 'USD/JPY', message: 'Volumen inusual detectado', level: 'medium', color: 'yellow' },
            { type: 'price', symbol: 'AUD/USD', message: 'Soporte en 0.6750 mantenido', level: 'low', color: 'blue' },
            { type: 'news', symbol: 'EUR', message: 'BCE mantiene tasas sin cambios', level: 'medium', color: 'orange' },
            { type: 'technical', symbol: 'XAU/USD', message: 'Patrón de martillo formado', level: 'medium', color: 'yellow' },
            { type: 'volatility', symbol: 'BTC/USD', message: 'Alta volatilidad detectada', level: 'high', color: 'red' },
            { type: 'correlation', symbol: 'Oil', message: 'Correlación USD/CAD rompiendo', level: 'low', color: 'blue' },
            { type: 'sentiment', symbol: 'Market', message: 'Sentimiento del mercado: Alcista', level: 'low', color: 'green' }
        ];

        // Función para iniciar alertas en vivo
        function startLiveAlerts() {
            if (alertsRunning) return;

            alertsRunning = true;
            const btn = document.getElementById('alerts-btn');
            btn.innerHTML = '<i class="fas fa-broadcast-tower mr-2"></i>Alertas Activas';
            btn.className = 'bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700';

            // Limpiar feed
            const feed = document.getElementById('live-alerts-feed');
            feed.innerHTML = '<div class="text-center text-green-600 font-bold mb-4"><i class="fas fa-satellite-dish mr-2"></i>Sistema de alertas iniciado...</div>';

            // Generar alertas cada 3-8 segundos
            alertsInterval = setInterval(() => {
                generateRandomAlert();
            }, Math.random() * 5000 + 3000);

            // Primera alerta inmediata
            setTimeout(() => generateRandomAlert(), 1000);
        }

        // Función para detener alertas
        function stopLiveAlerts() {
            if (!alertsRunning) return;

            alertsRunning = false;
            clearInterval(alertsInterval);

            const btn = document.getElementById('alerts-btn');
            btn.innerHTML = '<i class="fas fa-play mr-2"></i>Iniciar Alertas en Vivo';
            btn.className = 'bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700';

            const feed = document.getElementById('live-alerts-feed');
            const stopMessage = document.createElement('div');
            stopMessage.className = 'text-center text-red-600 font-bold p-4 bg-red-50 rounded-lg border border-red-200';
            stopMessage.innerHTML = '<i class="fas fa-stop-circle mr-2"></i>Sistema de alertas detenido';
            feed.insertBefore(stopMessage, feed.firstChild);
        }

        // Función para generar alerta aleatoria
        function generateRandomAlert() {
            if (!alertsRunning) return;

            const alert = alertsDatabase[Math.floor(Math.random() * alertsDatabase.length)];
            const now = new Date();
            const timeStr = now.toLocaleTimeString('es-ES');

            alertCounter++;

            const alertElement = document.createElement('div');
            alertElement.className = `p-3 rounded-lg border-l-4 border-${alert.color}-500 bg-${alert.color}-50 animate-pulse`;
            alertElement.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-1">
                            <span class="font-semibold text-${alert.color}-800">${getAlertIcon(alert.type)} ${alert.symbol}</span>
                            <span class="ml-2 text-xs bg-${alert.color}-500 text-white px-2 py-1 rounded">${alert.level.toUpperCase()}</span>
                        </div>
                        <div class="text-sm text-${alert.color}-700">${alert.message}</div>
                        <div class="text-xs text-${alert.color}-600 mt-1">${timeStr} - Alerta #${alertCounter}</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="text-${alert.color}-400 hover:text-${alert.color}-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            const feed = document.getElementById('live-alerts-feed');
            feed.insertBefore(alertElement, feed.firstChild);

            // Mantener solo las últimas 10 alertas
            while (feed.children.length > 10) {
                feed.removeChild(feed.lastChild);
            }

            // Remover animación después de 2 segundos
            setTimeout(() => {
                alertElement.classList.remove('animate-pulse');
            }, 2000);
        }

        // Función para obtener icono según tipo de alerta
        function getAlertIcon(type) {
            const icons = {
                'price': '💰',
                'news': '📰',
                'technical': '📊',
                'volume': '📈',
                'volatility': '⚡',
                'correlation': '🔗',
                'sentiment': '😊'
            };
            return icons[type] || '🔔';
        }

        window.onload = function() {
            loadUserInfo();
        };
    </script>
</body>
</html>
