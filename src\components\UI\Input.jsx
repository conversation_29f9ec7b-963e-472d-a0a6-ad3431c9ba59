import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Eye, EyeOff } from 'lucide-react';

const Input = ({ 
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  error,
  disabled = false,
  required = false,
  className = '',
  icon: Icon,
  ...props 
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <motion.div 
      className={`relative ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {label && (
        <label className="block text-sm font-medium text-text mb-2">
          {label}
          {required && <span className="text-danger ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {Icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon className="h-5 w-5 text-gray-400" />
          </div>
        )}
        
        <input
          type={inputType}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={`
            w-full px-4 py-3 
            ${Icon ? 'pl-10' : ''} 
            ${type === 'password' ? 'pr-10' : ''}
            bg-secondary border rounded-lg text-text placeholder-gray-400
            focus:outline-none focus:ring-2 focus:ring-opacity-50 
            transition-all duration-200
            disabled:opacity-50 disabled:cursor-not-allowed
            ${error 
              ? 'border-danger focus:border-danger focus:ring-danger' 
              : isFocused 
                ? 'border-danger focus:border-danger focus:ring-danger'
                : 'border-accent hover:border-gray-500'
            }
          `}
          {...props}
        />
        
        {type === 'password' && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5 text-gray-400 hover:text-text transition-colors" />
            ) : (
              <Eye className="h-5 w-5 text-gray-400 hover:text-text transition-colors" />
            )}
          </button>
        )}
      </div>
      
      {error && (
        <motion.p 
          className="mt-2 text-sm text-danger"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          {error}
        </motion.p>
      )}
    </motion.div>
  );
};

export default Input;
