<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro - GERMAYORI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-field {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .input-field:focus {
            background: rgba(255, 255, 255, 1);
            border-color: #00d4ff;
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
        }

    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="glass-card rounded-2xl p-8 w-full max-w-md shadow-2xl">
        <!-- Logo -->
        <div class="text-center mb-8">
            <div class="text-4xl mb-2">🚀</div>
            <h1 class="text-3xl font-bold text-white mb-2">GERMAYORI</h1>
            <p class="text-gray-300">Trading Platform</p>
        </div>

        <!-- Plan Info -->
        <div class="bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg p-4 mb-6 text-white">
            <h3 class="font-bold text-lg mb-1">Plan Básico - $45/mes</h3>
            <p class="text-sm opacity-90">Acceso a señales, noticias, alertas, calculadora, notificaciones y trading en vivo</p>
        </div>

        <!-- Error/Success Messages -->
        <div id="errorMessage" class="hidden bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg mb-4">
        </div>
        <div id="successMessage" class="hidden bg-green-500 bg-opacity-20 border border-green-500 text-green-200 px-4 py-3 rounded-lg mb-4">
        </div>

        <!-- Form -->
        <form id="registerForm" class="space-y-4">
            <div>
                <label class="block text-white text-sm font-medium mb-2">Nombre de Usuario</label>
                <input type="text" id="name" name="name" required
                       class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800 placeholder-gray-500"
                       placeholder="Tu nombre de usuario">
            </div>

            <div>
                <label class="block text-white text-sm font-medium mb-2">Correo Electrónico</label>
                <input type="email" id="email" name="email" required
                       class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800 placeholder-gray-500"
                       placeholder="<EMAIL>">
            </div>

            <div>
                <label class="block text-white text-sm font-medium mb-2">Contraseña</label>
                <input type="password" id="password" name="password" required
                       class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800 placeholder-gray-500"
                       placeholder="••••••••">
            </div>

            <div>
                <label class="block text-white text-sm font-medium mb-2">Confirmar Contraseña</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required
                       class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800 placeholder-gray-500"
                       placeholder="••••••••">
            </div>

            <div>
                <label class="block text-white text-sm font-medium mb-2">Plan de Suscripción</label>
                <select id="plan" name="plan" required
                        class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800">
                    <option value="basico">Básico - $45/mes</option>
                    <option value="intermedio">Intermedio - $70/mes</option>
                    <option value="avanzado">Avanzado - $95/mes</option>
                    <option value="completo">Acceso Completo - $140/mes</option>
                </select>
            </div>

            <div>
                <label class="block text-white text-sm font-medium mb-2">Nombre</label>
                <input type="text" id="firstName" name="firstName" required
                       class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800 placeholder-gray-500"
                       placeholder="Tu nombre">
            </div>

            <div>
                <label class="block text-white text-sm font-medium mb-2">Apellido</label>
                <input type="text" id="lastName" name="lastName" required
                       class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800 placeholder-gray-500"
                       placeholder="Tu apellido">
            </div>

            <div>
                <label class="block text-white text-sm font-medium mb-2">País</label>
                <input type="text" id="country" name="country" required
                       class="input-field w-full px-4 py-3 rounded-lg outline-none text-gray-800 placeholder-gray-500"
                       placeholder="Tu país">
            </div>

            <button type="submit" class="btn-primary w-full py-3 rounded-lg text-white font-semibold text-lg">
                <i class="fas fa-user-plus mr-2"></i>Crear Cuenta
            </button>
        </form>

        <!-- Login Link -->
        <div class="text-center mt-6">
            <p class="text-gray-300">¿Ya tienes cuenta?
                <a href="index.html" class="text-cyan-400 hover:text-cyan-300 font-medium">Iniciar Sesión</a>
            </p>
        </div>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const plan = document.getElementById('plan').value;
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const country = document.getElementById('country').value;

            // Validaciones
            if (password !== confirmPassword) {
                showError('Las contraseñas no coinciden');
                return;
            }

            if (password.length < 6) {
                showError('La contraseña debe tener al menos 6 caracteres');
                return;
            }

            // Datos para enviar
            const userData = {
                name: name,
                email: email,
                password: password,
                plan: plan,
                planPrice: getPlanPrice(plan)
            };

            try {
                const response = await fetch('http://localhost:3000/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess('¡Registro exitoso! Redirigiendo...');

                    // Guardar datos del usuario
                    localStorage.setItem('germayori_user', JSON.stringify(data.user));
                    localStorage.setItem('germayori_selected_plan', plan);

                    // Redirigir después de 2 segundos
                    setTimeout(() => {
                        window.location.href = 'dashboard-simple.html';
                    }, 2000);
                } else {
                    showError(data.error || 'Error en el registro');
                }
            } catch (error) {
                console.error('Error:', error);
                showError('Error de conexión. Intenta de nuevo.');
            }
        });

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            successDiv.classList.add('hidden');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');

            setTimeout(() => {
                errorDiv.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            errorDiv.classList.add('hidden');
            successDiv.textContent = message;
            successDiv.classList.remove('hidden');
        }

        function getPlanPrice(plan) {
            const prices = {
                'basico': 45,
                'intermedio': 70,
                'avanzado': 95,
                'completo': 140
            };
            return prices[plan] || 45;
        }
    </script>
</body>
</html>
