import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Play, Clock, Star, Crown, CheckCircle, Lock } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import PaywallModal from '../components/Paywall/PaywallModal';

const Academia = () => {
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState('basico');
  const [showPaywall, setShowPaywall] = useState(false);

  const categories = [
    { id: 'basico', name: 'Básico', icon: BookOpen, free: true },
    { id: 'intermedio', name: 'Intermedio', icon: Star, free: false },
    { id: 'avanzado', name: 'Avanzado', icon: Crown, free: false },
    { id: 'estrategias', name: 'Estrategia<PERSON>', icon: CheckCircle, free: false }
  ];

  const courses = {
    basico: [
      {
        id: 1,
        title: "Introducción al Trading",
        description: "Conceptos básicos del mercado forex",
        duration: "45 min",
        lessons: 8,
        level: "Principiante",
        free: true,
        completed: true,
        rating: 4.8
      },
      {
        id: 2,
        title: "Análisis Técnico Básico",
        description: "Fundamentos del análisis técnico",
        duration: "60 min",
        lessons: 10,
        level: "Principiante",
        free: true,
        completed: false,
        rating: 4.9
      },
      {
        id: 3,
        title: "Gestión de Riesgo",
        description: "Cómo proteger tu capital",
        duration: "30 min",
        lessons: 6,
        level: "Principiante",
        free: true,
        completed: false,
        rating: 4.7
      }
    ],
    intermedio: [
      {
        id: 4,
        title: "Patrones de Velas Japonesas",
        description: "Identificación y uso de patrones",
        duration: "90 min",
        lessons: 15,
        level: "Intermedio",
        free: false,
        completed: false,
        rating: 4.9
      },
      {
        id: 5,
        title: "Indicadores Técnicos Avanzados",
        description: "RSI, MACD, Bollinger Bands y más",
        duration: "120 min",
        lessons: 18,
        level: "Intermedio",
        free: false,
        completed: false,
        rating: 4.8
      },
      {
        id: 6,
        title: "Psicología del Trading",
        description: "Control emocional y disciplina",
        duration: "75 min",
        lessons: 12,
        level: "Intermedio",
        free: false,
        completed: false,
        rating: 4.9
      }
    ],
    avanzado: [
      {
        id: 7,
        title: "Análisis Multi-Timeframe",
        description: "Estrategias de múltiples marcos temporales",
        duration: "150 min",
        lessons: 20,
        level: "Avanzado",
        free: false,
        completed: false,
        rating: 4.9
      },
      {
        id: 8,
        title: "Trading Algorítmico",
        description: "Automatización de estrategias",
        duration: "180 min",
        lessons: 25,
        level: "Avanzado",
        free: false,
        completed: false,
        rating: 4.8
      }
    ],
    estrategias: [
      {
        id: 9,
        title: "Estrategia Scalping",
        description: "Trading de corto plazo",
        duration: "100 min",
        lessons: 16,
        level: "Intermedio",
        free: false,
        completed: false,
        rating: 4.7
      },
      {
        id: 10,
        title: "Swing Trading",
        description: "Operaciones de medio plazo",
        duration: "110 min",
        lessons: 14,
        level: "Intermedio",
        free: false,
        completed: false,
        rating: 4.8
      }
    ]
  };

  const handleCourseClick = (course) => {
    if (!course.free && !user?.isPaid) {
      setShowPaywall(true);
    } else {
      // Abrir curso
      console.log('Abriendo curso:', course.title);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-text mb-2 flex items-center justify-center">
          <BookOpen className="mr-3 text-danger" size={32} />
          Academia GERMAYORI
        </h1>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Aprende trading desde cero hasta nivel profesional con nuestros cursos estructurados
        </p>
      </motion.div>

      {/* Categories */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => {
              const Icon = category.icon;
              const isLocked = !category.free && !user?.isPaid;
              return (
                <button
                  key={category.id}
                  onClick={() => {
                    if (isLocked) {
                      setShowPaywall(true);
                    } else {
                      setSelectedCategory(category.id);
                    }
                  }}
                  className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-all duration-200 ${
                    selectedCategory === category.id && !isLocked
                      ? 'bg-danger text-white'
                      : isLocked
                      ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                      : 'bg-accent text-gray-300 hover:bg-danger hover:text-white'
                  }`}
                >
                  <Icon size={20} />
                  <span>{category.name}</span>
                  {isLocked && <Lock size={16} />}
                  {!category.free && <Crown size={16} className="text-warning" />}
                </button>
              );
            })}
          </div>
        </Card>
      </motion.div>

      {/* Courses Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {courses[selectedCategory]?.map((course, index) => {
            const isLocked = !course.free && !user?.isPaid;
            return (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
              >
                <Card 
                  className={`cursor-pointer transition-all duration-200 ${
                    isLocked ? 'opacity-60' : 'hover:scale-105'
                  }`}
                  onClick={() => handleCourseClick(course)}
                >
                  {/* Course Header */}
                  <div className="relative mb-4">
                    <div className="w-full h-32 bg-gradient-to-r from-primary to-accent rounded-lg flex items-center justify-center">
                      {isLocked ? (
                        <Lock className="text-gray-500" size={32} />
                      ) : (
                        <Play className="text-white" size={32} />
                      )}
                    </div>
                    {!course.free && (
                      <div className="absolute top-2 right-2">
                        <Crown className="text-warning" size={20} />
                      </div>
                    )}
                    {course.completed && (
                      <div className="absolute top-2 left-2">
                        <CheckCircle className="text-success" size={20} />
                      </div>
                    )}
                  </div>

                  {/* Course Info */}
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-text mb-1">{course.title}</h3>
                      <p className="text-sm text-gray-400">{course.description}</p>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Clock size={14} />
                        <span>{course.duration}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <BookOpen size={14} />
                        <span>{course.lessons} lecciones</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`px-2 py-1 rounded text-xs ${
                        course.level === 'Principiante' ? 'bg-success bg-opacity-20 text-success' :
                        course.level === 'Intermedio' ? 'bg-warning bg-opacity-20 text-warning' :
                        'bg-danger bg-opacity-20 text-danger'
                      }`}>
                        {course.level}
                      </span>
                      <div className="flex items-center space-x-1">
                        <Star className="text-warning" size={14} />
                        <span className="text-sm text-gray-400">{course.rating}</span>
                      </div>
                    </div>

                    <Button
                      variant={course.completed ? "outline" : "primary"}
                      className="w-full"
                      disabled={isLocked}
                    >
                      {isLocked ? (
                        <>
                          <Lock size={16} className="mr-2" />
                          Premium
                        </>
                      ) : course.completed ? (
                        <>
                          <CheckCircle size={16} className="mr-2" />
                          Completado
                        </>
                      ) : (
                        <>
                          <Play size={16} className="mr-2" />
                          Comenzar
                        </>
                      )}
                    </Button>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </motion.div>

      {/* Progress Section */}
      {user?.isPaid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-text mb-6">Tu Progreso</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-success bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="text-success" size={32} />
                </div>
                <h4 className="text-2xl font-bold text-text mb-1">3</h4>
                <p className="text-gray-400">Cursos Completados</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Clock className="text-warning" size={32} />
                </div>
                <h4 className="text-2xl font-bold text-text mb-1">12h</h4>
                <p className="text-gray-400">Tiempo de Estudio</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-danger bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Star className="text-danger" size={32} />
                </div>
                <h4 className="text-2xl font-bold text-text mb-1">85%</h4>
                <p className="text-gray-400">Progreso General</p>
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      <PaywallModal 
        isOpen={showPaywall} 
        onClose={() => setShowPaywall(false)} 
        feature="Academia Completa"
      />
    </div>
  );
};

export default Academia;
