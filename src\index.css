@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS personalizadas */
:root {
  --primary: #1a1a2e;
  --secondary: #16213e;
  --accent: #0f3460;
  --danger: #e94560;
  --text: #f5f5f5;
  --success: #27ae60;
  --warning: #f39c12;
}

/* Estilos base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Centrar aplicación en pantallas grandes */
@media (min-width: 1600px) {
  body {
    background: linear-gradient(135deg, #0f1419 0%, #1a1a2e 50%, #16213e 100%);
  }
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--danger);
}

/* Animaciones globales */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Clases utilitarias */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s infinite;
}

/* Gradientes personalizados */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.gradient-danger {
  background: linear-gradient(135deg, var(--danger) 0%, #c73650 100%);
}

.gradient-success {
  background: linear-gradient(135deg, var(--success) 0%, #219a52 100%);
}

/* Efectos de hover */
.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Sombras personalizadas */
.shadow-germayori {
  box-shadow: 0 10px 25px rgba(233, 69, 96, 0.2);
}

.shadow-primary {
  box-shadow: 0 10px 25px rgba(26, 26, 46, 0.3);
}

/* Botones personalizados */
.btn-primary {
  @apply bg-danger hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105;
}

.btn-secondary {
  @apply bg-accent hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105;
}

.btn-outline {
  @apply border-2 border-danger text-danger hover:bg-danger hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200;
}

/* Inputs personalizados */
.input-primary {
  @apply bg-secondary border border-accent text-text placeholder-gray-400 rounded-lg px-4 py-3 focus:outline-none focus:border-danger focus:ring-2 focus:ring-danger focus:ring-opacity-50 transition-all duration-200;
}

/* Cards personalizadas */
.card-primary {
  @apply bg-secondary border border-accent rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200;
}

/* Efectos de texto */
.text-gradient {
  background: linear-gradient(135deg, var(--danger) 0%, var(--warning) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading spinner */
.spinner {
  border: 3px solid var(--secondary);
  border-top: 3px solid var(--danger);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none;
  }
}
