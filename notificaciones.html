<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Notificaciones</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>
                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i><span>Chat Educativo</span>
                </a>
                <a href="calculadora.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-calculator mr-3 text-green-400"></i><span>Calculadora</span>
                </a>
                <a href="noticias.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i><span>Noticias</span>
                </a>
                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i><span>Señales</span>
                </a>
                <a href="notificaciones.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-bell mr-3 text-pink-400"></i><span>Notificaciones</span>
                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">3</span>
                </a>
                <a href="alertas.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i><span>Alertas Mercado</span>
                </a>
            </div>

            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- Notifications Content -->
        <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-white mb-2">🔔 Notificaciones</h1>
                <p class="text-gray-200">Centro de notificaciones y configuración de alertas</p>
            </div>

            <div class="space-y-6">
                <!-- Recent Notifications -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-pink-600">📬 Notificaciones Recientes</h2>
                    <div class="space-y-3">
                        <div class="flex items-start p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                            <i class="fas fa-info-circle text-blue-500 mr-3 mt-1"></i>
                            <div>
                                <div class="font-semibold text-blue-800">Nueva señal EUR/USD</div>
                                <div class="text-sm text-blue-600">Se ha generado una nueva señal de compra</div>
                                <div class="text-xs text-gray-500">Hace 5 minutos</div>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                            <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                            <div>
                                <div class="font-semibold text-green-800">Take Profit alcanzado</div>
                                <div class="text-sm text-green-600">GBP/USD +45 pips de ganancia</div>
                                <div class="text-xs text-gray-500">Hace 15 minutos</div>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-3 mt-1"></i>
                            <div>
                                <div class="font-semibold text-yellow-800">Evento económico importante</div>
                                <div class="text-sm text-yellow-600">NFP en 30 minutos - Alta volatilidad esperada</div>
                                <div class="text-xs text-gray-500">Hace 30 minutos</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Settings -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-pink-600">⚙️ Configuración de Notificaciones</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="font-medium">Señales de Trading</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="font-medium">Noticias Económicas</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="font-medium">Alertas de Mercado</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        function loadUserInfo() {
            const user = checkAuth();
            if (user) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type.toUpperCase();
                document.getElementById('user-avatar').src = user.avatar;
            }
        }

        window.onload = function() {
            loadUserInfo();
        };
    </script>
</body>
</html>
