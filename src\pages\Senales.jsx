import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, Crown, AlertTriangle, Clock, Target, DollarSign } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import PaywallModal from '../components/Paywall/PaywallModal';

const Senales = () => {
  const { user } = useAuth();
  const [showPaywall, setShowPaywall] = useState(!user?.isPaid);
  const [activeTab, setActiveTab] = useState('educativas');

  // Señales educativas (ejemplos para aprender)
  const senalesEducativas = [
    {
      id: 1,
      pair: 'EUR/USD',
      type: 'BUY',
      entry: 1.0845,
      stopLoss: 1.0820,
      takeProfit: 1.0895,
      riskReward: '1:2',
      analysis: 'Ejemplo educativo: Ruptura de resistencia en 1.0840 con volumen alto. RSI saliendo de sobreventa.',
      timeframe: '1H',
      status: 'Ejemplo',
      timestamp: '2024-01-15 10:30',
      educational: true
    },
    {
      id: 2,
      pair: 'GBP/JPY',
      type: 'SELL',
      entry: 185.50,
      stopLoss: 186.00,
      takeProfit: 184.50,
      riskReward: '1:2',
      analysis: 'Ejemplo educativo: Patrón de doble techo formado. MACD mostrando divergencia bajista.',
      timeframe: '4H',
      status: 'Ejemplo',
      timestamp: '2024-01-15 08:15',
      educational: true
    }
  ];

  const analisisEducativo = [
    {
      id: 1,
      title: 'Cómo Identificar Soportes y Resistencias',
      content: 'Los soportes y resistencias son niveles clave donde el precio tiende a rebotar. En este ejemplo, vemos cómo EUR/USD respeta el soporte en 1.0800...',
      image: '/images/analysis1.jpg',
      category: 'Análisis Técnico',
      readTime: '5 min'
    },
    {
      id: 2,
      title: 'Patrones de Velas Japonesas Más Efectivos',
      content: 'Los patrones de velas nos dan información valiosa sobre el sentimiento del mercado. El martillo en GBP/USD sugiere una posible reversión...',
      image: '/images/analysis2.jpg',
      category: 'Patrones',
      readTime: '7 min'
    }
  ];

  if (!user?.isPaid) {
    return (
      <>
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="w-24 h-24 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="text-warning" size={48} />
            </div>
            <h1 className="text-3xl font-bold text-text mb-4">🔒 Señales de Trading Premium</h1>
            <p className="text-gray-400 max-w-2xl mx-auto mb-8">
              Las señales de trading son exclusivas para miembros premium.
              Obtén acceso a análisis profesionales y señales en tiempo real enviadas por nuestros expertos.
            </p>
            <div className="bg-accent bg-opacity-20 rounded-lg p-6 mb-8 max-w-md mx-auto">
              <h3 className="text-lg font-semibold text-text mb-4">¿Qué incluye Premium?</h3>
              <ul className="text-left text-gray-300 space-y-2">
                <li>✅ Señales de trading en tiempo real</li>
                <li>✅ Análisis técnico detallado</li>
                <li>✅ Chat educativo con IA</li>
                <li>✅ Videos educativos avanzados</li>
                <li>✅ Alertas personalizadas</li>
              </ul>
            </div>
            <Button variant="primary" onClick={() => setShowPaywall(true)}>
              <Crown size={16} className="mr-2" />
              Obtener Acceso Premium
            </Button>
          </motion.div>
        </div>
        <PaywallModal
          isOpen={showPaywall}
          onClose={() => setShowPaywall(false)}
          feature="Canal de Señales Educativas"
        />
      </>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <TrendingUp className="mr-3 text-danger" size={32} />
            Canal Educativo
            <Crown className="ml-2 text-warning" size={24} />
          </h1>
          <p className="text-gray-400">
            Ejemplos educativos y análisis técnico para aprender
          </p>
        </div>
      </motion.div>

      {/* Warning */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="border-warning bg-warning bg-opacity-10">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="text-warning mt-1" size={20} />
            <div>
              <h3 className="font-semibold text-warning mb-1">Contenido Educativo</h3>
              <p className="text-sm text-gray-300">
                Este canal contiene únicamente ejemplos educativos y análisis técnico para fines de aprendizaje.
                NO son señales de trading reales ni consejos de inversión. Siempre haz tu propia investigación.
              </p>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <div className="flex space-x-4">
            <button
              onClick={() => setActiveTab('educativas')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'educativas'
                  ? 'bg-danger text-white'
                  : 'text-gray-400 hover:text-white hover:bg-accent'
              }`}
            >
              Ejemplos de Análisis
            </button>
            <button
              onClick={() => setActiveTab('analisis')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'analisis'
                  ? 'bg-danger text-white'
                  : 'text-gray-400 hover:text-white hover:bg-accent'
              }`}
            >
              Casos de Estudio
            </button>
          </div>
        </Card>
      </motion.div>

      {/* Content */}
      {activeTab === 'educativas' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-4"
        >
          {senalesEducativas.map((senal, index) => (
            <motion.div
              key={senal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1 }}
            >
              <Card className="border-accent">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      senal.type === 'BUY' ? 'bg-success bg-opacity-20' : 'bg-danger bg-opacity-20'
                    }`}>
                      {senal.type === 'BUY' ? (
                        <TrendingUp className="text-success" size={24} />
                      ) : (
                        <TrendingDown className="text-danger" size={24} />
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-text">{senal.pair}</h3>
                      <p className="text-sm text-gray-400">{senal.timeframe} • {senal.timestamp}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      senal.type === 'BUY' ? 'bg-success text-white' : 'bg-danger text-white'
                    }`}>
                      {senal.type} (Ejemplo)
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-400 mb-1">Entrada</p>
                    <p className="font-semibold text-text">{senal.entry}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-400 mb-1">Stop Loss</p>
                    <p className="font-semibold text-danger">{senal.stopLoss}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-400 mb-1">Take Profit</p>
                    <p className="font-semibold text-success">{senal.takeProfit}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-400 mb-1">Risk:Reward</p>
                    <p className="font-semibold text-warning">{senal.riskReward}</p>
                  </div>
                </div>

                <div className="bg-primary rounded-lg p-4">
                  <h4 className="font-semibold text-text mb-2">Análisis Educativo:</h4>
                  <p className="text-gray-300 text-sm">{senal.analysis}</p>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}

      {activeTab === 'analisis' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {analisisEducativo.map((analisis, index) => (
            <motion.div
              key={analisis.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1 }}
            >
              <Card className="h-full">
                <div className="w-full h-48 bg-gradient-to-r from-primary to-accent rounded-lg mb-4 flex items-center justify-center">
                  <TrendingUp className="text-white" size={48} />
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="px-2 py-1 bg-accent bg-opacity-20 text-accent rounded text-xs">
                      {analisis.category}
                    </span>
                    <div className="flex items-center space-x-1 text-gray-400 text-xs">
                      <Clock size={12} />
                      <span>{analisis.readTime}</span>
                    </div>
                  </div>
                  <h3 className="font-semibold text-text">{analisis.title}</h3>
                  <p className="text-sm text-gray-400">{analisis.content}</p>
                  <Button variant="outline" className="w-full">
                    Leer Análisis Completo
                  </Button>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}
    </div>
  );
};

export default Senales;
