<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canal de Marketing - GERMAYORI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #dc2626, #ef4444, #f87171);
            color: #fff;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #fbbf24;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .marketing-sections {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .section-btn {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
            border: 2px solid #fbbf24;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .section-btn.active {
            background: #fbbf24;
            color: #000;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .marketing-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(251, 191, 36, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .marketing-card:hover {
            border-color: #fbbf24;
            transform: translateY(-5px);
        }

        .video-thumbnail {
            width: 100%;
            height: 180px;
            background: #7f1d1d;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .play-icon {
            font-size: 3rem;
            color: #fbbf24;
        }

        .card-title {
            color: #fbbf24;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border: 2px dashed #fbbf24;
        }

        .upload-btn {
            background: #fbbf24;
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
        }

        .team-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #fbbf24;
        }

        .team-member {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1a1a1a;
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #fbbf24;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff4444;
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
        }

        .video-player {
            width: 800px;
            height: 450px;
            background: #000;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="text-align: left; margin-bottom: 20px;">
                <a href="dashboard-simple.html" style="background: #0066cc; color: white; padding: 12px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block; transition: all 0.3s ease;">
                    ← Volver al Dashboard
                </a>
            </div>
            <h1>📢 Canal de Marketing GERMAYORI</h1>
            <p>Videos y estrategias de network marketing para armar tu equipo</p>
        </div>

        <div class="marketing-sections">
            <button class="section-btn active" onclick="showSection('videos')">📹 Videos Marketing</button>
            <button class="section-btn" onclick="showSection('equipo')">👥 Mi Equipo</button>
            <button class="section-btn" onclick="showSection('estrategias')">🎯 Estrategias</button>
            <button class="section-btn" onclick="showSection('capacitacion')">📚 Capacitación</button>
        </div>

        <!-- Sección Videos Marketing -->
        <div id="videos-section">
            <div class="upload-section">
                <h3>📤 Gestión de Videos de Marketing</h3>
                <input type="file" id="fileInput" accept="video/*" style="display: none;">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">📹 Subir Video Marketing</button>
                <button class="upload-btn" onclick="addYouTubeVideo()">🔗 Agregar YouTube</button>
                <button class="upload-btn" onclick="recordZoomVideo()">🎥 Grabar Zoom</button>
                <button class="upload-btn" onclick="deleteAllVideos()" style="background: #ff4444; color: white;">🗑️ Borrar Todos</button>
            </div>

            <div class="content-grid" id="videosGrid"></div>
        </div>

        <!-- Sección Mi Equipo -->
        <div id="equipo-section" style="display: none;">
            <div class="team-section">
                <h3 style="color: #fbbf24; margin-bottom: 20px;">👥 Mi Equipo de Network Marketing</h3>
                
                <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
                    <button class="upload-btn" onclick="addTeamMember()">➕ Agregar Miembro</button>
                    <button class="upload-btn" onclick="sendGroupMessage()" style="background: #10b981;">📢 Mensaje Grupal</button>
                    <button class="upload-btn" onclick="scheduleTraining()" style="background: #8b5cf6;">📅 Programar Capacitación</button>
                </div>

                <div id="teamList">
                    <!-- Lista de miembros del equipo -->
                </div>

                <div style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 10px;">
                    <h4 style="color: #fbbf24; margin-bottom: 10px;">📊 Estadísticas del Equipo</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                        <div style="text-align: center;">
                            <div style="color: #fbbf24; font-size: 1.5rem; font-weight: bold;" id="totalMembers">0</div>
                            <div style="color: #ccc; font-size: 0.9rem;">Miembros Totales</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #10b981; font-size: 1.5rem; font-weight: bold;" id="activeMembers">0</div>
                            <div style="color: #ccc; font-size: 0.9rem;">Miembros Activos</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #ef4444; font-size: 1.5rem; font-weight: bold;" id="newMembers">0</div>
                            <div style="color: #ccc; font-size: 0.9rem;">Nuevos Este Mes</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección Estrategias -->
        <div id="estrategias-section" style="display: none;">
            <div class="content-grid">
                <div class="marketing-card">
                    <div class="video-thumbnail">
                        <div class="play-icon">🎯</div>
                    </div>
                    <div class="card-title">Estrategia de Reclutamiento</div>
                    <div style="color: #ccc; margin-bottom: 10px;">Técnicas probadas para reclutar nuevos miembros</div>
                    <button onclick="viewStrategy('reclutamiento')" style="background: #fbbf24; color: #000; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">Ver Estrategia</button>
                </div>

                <div class="marketing-card">
                    <div class="video-thumbnail">
                        <div class="play-icon">💰</div>
                    </div>
                    <div class="card-title">Plan de Compensación</div>
                    <div style="color: #ccc; margin-bottom: 10px;">Cómo maximizar tus ganancias en GERMAYORI</div>
                    <button onclick="viewStrategy('compensacion')" style="background: #fbbf24; color: #000; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">Ver Plan</button>
                </div>

                <div class="marketing-card">
                    <div class="video-thumbnail">
                        <div class="play-icon">📱</div>
                    </div>
                    <div class="card-title">Marketing Digital</div>
                    <div style="color: #ccc; margin-bottom: 10px;">Estrategias de redes sociales y marketing online</div>
                    <button onclick="viewStrategy('digital')" style="background: #fbbf24; color: #000; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">Ver Estrategia</button>
                </div>
            </div>
        </div>

        <!-- Sección Capacitación -->
        <div id="capacitacion-section" style="display: none;">
            <div class="upload-section">
                <h3>📚 Material de Capacitación</h3>
                <p style="margin-bottom: 20px;">Recursos para entrenar a tu equipo de network marketing</p>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px;">
                        <h4 style="color: #fbbf24; margin-bottom: 10px;">📖 Manual del Distribuidor</h4>
                        <p style="color: #ccc; margin-bottom: 15px;">Guía completa para nuevos miembros</p>
                        <button class="upload-btn" onclick="downloadManual()">📥 Descargar</button>
                    </div>

                    <div style="background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px;">
                        <h4 style="color: #fbbf24; margin-bottom: 10px;">🎥 Videos de Entrenamiento</h4>
                        <p style="color: #ccc; margin-bottom: 15px;">Biblioteca de videos educativos</p>
                        <button class="upload-btn" onclick="viewTrainingVideos()">▶️ Ver Videos</button>
                    </div>

                    <div style="background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px;">
                        <h4 style="color: #fbbf24; margin-bottom: 10px;">📊 Presentaciones</h4>
                        <p style="color: #ccc; margin-bottom: 15px;">Slides para presentar GERMAYORI</p>
                        <button class="upload-btn" onclick="downloadPresentations()">📥 Descargar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para videos -->
    <div class="modal" id="videoModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeVideo()">&times;</button>
            <h3 id="modalTitle" style="color: #fbbf24; text-align: center; margin-bottom: 20px;"></h3>
            <video id="videoPlayer" class="video-player" controls autoplay>
                Tu navegador no soporta video HTML5.
            </video>
        </div>
    </div>

    <script>
        let marketingVideos = [];
        let teamMembers = [];
        let currentSection = 'videos';

        // Inicializar
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            showSection('videos');
        });

        function loadData() {
            const savedVideos = localStorage.getItem('germayori_marketing_videos');
            const savedTeam = localStorage.getItem('germayori_team_members');
            
            if (savedVideos) {
                marketingVideos = JSON.parse(savedVideos);
            }
            
            if (savedTeam) {
                teamMembers = JSON.parse(savedTeam);
            }
        }

        function showSection(section) {
            currentSection = section;
            
            // Ocultar todas las secciones
            document.getElementById('videos-section').style.display = 'none';
            document.getElementById('equipo-section').style.display = 'none';
            document.getElementById('estrategias-section').style.display = 'none';
            document.getElementById('capacitacion-section').style.display = 'none';
            
            // Mostrar sección seleccionada
            document.getElementById(section + '-section').style.display = 'block';
            
            // Actualizar botones
            document.querySelectorAll('.section-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Cargar contenido específico
            if (section === 'videos') {
                displayVideos();
            } else if (section === 'equipo') {
                displayTeam();
                updateTeamStats();
            }
        }

        function displayVideos() {
            const grid = document.getElementById('videosGrid');
            
            if (marketingVideos.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #ccc; padding: 50px;">No hay videos de marketing aún</div>';
                return;
            }

            grid.innerHTML = marketingVideos.map(video => `
                <div class="marketing-card">
                    <div class="video-thumbnail" onclick="playVideo('${video.id}')">
                        <div class="play-icon">▶️</div>
                    </div>
                    <div class="card-title">${video.title}</div>
                    <div style="color: #ccc; margin-bottom: 10px;">${video.description}</div>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="playVideo('${video.id}')" style="background: #fbbf24; color: #000; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">▶️ Reproducir</button>
                        <button onclick="deleteVideo('${video.id}')" style="background: #ff4444; color: white; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">🗑️ Eliminar</button>
                    </div>
                </div>
            `).join('');
        }

        function addYouTubeVideo() {
            const url = prompt('Ingresa la URL de YouTube para video de marketing:');
            if (url) {
                const title = prompt('Título del video:') || 'Video de Marketing';
                const description = prompt('Descripción:') || 'Video de network marketing';
                
                const newVideo = {
                    id: 'yt_marketing_' + Date.now(),
                    title: title,
                    description: description,
                    url: url,
                    type: 'youtube'
                };
                
                marketingVideos.push(newVideo);
                localStorage.setItem('germayori_marketing_videos', JSON.stringify(marketingVideos));
                displayVideos();
                alert('✅ Video de marketing agregado correctamente');
            }
        }

        function playVideo(videoId) {
            const video = marketingVideos.find(v => v.id === videoId);
            if (!video) return;

            document.getElementById('modalTitle').textContent = video.title;
            const player = document.getElementById('videoPlayer');
            const videoSrc = video.url || video.data || 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
            player.src = videoSrc;
            document.getElementById('videoModal').style.display = 'block';
        }

        function closeVideo() {
            document.getElementById('videoModal').style.display = 'none';
            const player = document.getElementById('videoPlayer');
            player.pause();
            player.src = '';
        }

        function deleteVideo(videoId) {
            const video = marketingVideos.find(v => v.id === videoId);
            if (!video) return;

            const confirmDelete = confirm(`¿Estás seguro de que quieres eliminar el video "${video.title}"?`);

            if (confirmDelete) {
                marketingVideos = marketingVideos.filter(v => v.id !== videoId);
                localStorage.setItem('germayori_marketing_videos', JSON.stringify(marketingVideos));
                displayVideos();
                alert('✅ Video de marketing eliminado correctamente');
            }
        }

        function deleteAllVideos() {
            if (marketingVideos.length === 0) {
                alert('No hay videos de marketing para eliminar');
                return;
            }

            const confirmDeleteAll = confirm(`¿Estás seguro de que quieres eliminar TODOS los videos de marketing (${marketingVideos.length} videos)?`);

            if (confirmDeleteAll) {
                marketingVideos = [];
                localStorage.setItem('germayori_marketing_videos', JSON.stringify(marketingVideos));
                displayVideos();
                alert('✅ Todos los videos de marketing han sido eliminados');
            }
        }

        function addTeamMember() {
            const name = prompt('Nombre del nuevo miembro:');
            if (name) {
                const email = prompt('Email:') || '';
                const phone = prompt('Teléfono:') || '';
                
                const newMember = {
                    id: 'member_' + Date.now(),
                    name: name,
                    email: email,
                    phone: phone,
                    joinDate: new Date().toISOString(),
                    status: 'active',
                    level: 'Principiante'
                };
                
                teamMembers.push(newMember);
                localStorage.setItem('germayori_team_members', JSON.stringify(teamMembers));
                displayTeam();
                updateTeamStats();
                alert('✅ Miembro agregado al equipo');
            }
        }

        function displayTeam() {
            const teamList = document.getElementById('teamList');
            
            if (teamMembers.length === 0) {
                teamList.innerHTML = '<div style="text-align: center; color: #ccc; padding: 30px;">No hay miembros en tu equipo aún</div>';
                return;
            }

            teamList.innerHTML = teamMembers.map(member => `
                <div class="team-member">
                    <div>
                        <div style="color: #fbbf24; font-weight: bold;">${member.name}</div>
                        <div style="color: #ccc; font-size: 0.9rem;">${member.email} | ${member.phone}</div>
                        <div style="color: #ccc; font-size: 0.8rem;">Nivel: ${member.level} | Desde: ${new Date(member.joinDate).toLocaleDateString()}</div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="contactMember('${member.id}')" style="background: #10b981; color: white; border: none; padding: 5px 10px; border-radius: 10px; cursor: pointer;">📞 Contactar</button>
                        <button onclick="removeMember('${member.id}')" style="background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 10px; cursor: pointer;">❌ Remover</button>
                    </div>
                </div>
            `).join('');
        }

        function updateTeamStats() {
            document.getElementById('totalMembers').textContent = teamMembers.length;
            document.getElementById('activeMembers').textContent = teamMembers.filter(m => m.status === 'active').length;
            
            const thisMonth = new Date().getMonth();
            const newThisMonth = teamMembers.filter(m => new Date(m.joinDate).getMonth() === thisMonth).length;
            document.getElementById('newMembers').textContent = newThisMonth;
        }

        function recordZoomVideo() {
            alert('🎥 Función de grabación Zoom\n\nEsta función te permitirá grabar directamente desde Zoom y guardar el video en tu canal de marketing.\n\n📹 Próximamente disponible');
        }

        function viewStrategy(type) {
            alert(`🎯 Estrategia: ${type}\n\nEsta sección contendrá estrategias detalladas de network marketing específicas para GERMAYORI.\n\n📚 Contenido en desarrollo`);
        }

        function sendGroupMessage() {
            const message = prompt('Mensaje para todo el equipo:');
            if (message) {
                alert(`📢 Mensaje enviado a ${teamMembers.length} miembros:\n\n"${message}"\n\n✅ Mensaje entregado exitosamente`);
            }
        }

        function scheduleTraining() {
            const date = prompt('Fecha de capacitación (DD/MM/YYYY):');
            const time = prompt('Hora (HH:MM):');
            if (date && time) {
                alert(`📅 Capacitación programada:\n\n📅 Fecha: ${date}\n⏰ Hora: ${time}\n👥 Participantes: ${teamMembers.length} miembros\n\n✅ Invitaciones enviadas`);
            }
        }

        function contactMember(memberId) {
            const member = teamMembers.find(m => m.id === memberId);
            if (member) {
                alert(`📞 Contactando a ${member.name}\n\n📧 Email: ${member.email}\n📱 Teléfono: ${member.phone}\n\n💬 Opciones de contacto disponibles`);
            }
        }

        function removeMember(memberId) {
            const member = teamMembers.find(m => m.id === memberId);
            if (member && confirm(`¿Remover a ${member.name} del equipo?`)) {
                teamMembers = teamMembers.filter(m => m.id !== memberId);
                localStorage.setItem('germayori_team_members', JSON.stringify(teamMembers));
                displayTeam();
                updateTeamStats();
                alert('✅ Miembro removido del equipo');
            }
        }

        // Subir archivo
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const newVideo = {
                        id: 'file_marketing_' + Date.now(),
                        title: file.name.replace(/\.[^/.]+$/, ""),
                        description: 'Video de marketing subido por jhon0608',
                        data: e.target.result,
                        type: 'file'
                    };
                    marketingVideos.push(newVideo);
                    localStorage.setItem('germayori_marketing_videos', JSON.stringify(marketingVideos));
                    displayVideos();
                    alert('✅ Video de marketing subido correctamente');
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
