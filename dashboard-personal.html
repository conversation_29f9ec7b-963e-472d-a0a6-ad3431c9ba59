<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Personal | GERMAYORI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .main-content {
            background: rgba(30, 60, 114, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar {
            background: rgba(20, 40, 80, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }
        .channel-item {
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }
        .channel-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
            transform: translateX(5px);
        }
        .channel-active {
            background: rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.5);
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }
        .notification-item {
            background: rgba(255, 255, 255, 0.1);
            border-left: 4px solid #10b981;
            transition: all 0.3s ease;
        }
        .notification-item:hover {
            background: rgba(255, 255, 255, 0.15);
        }
        .certification-badge {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-80 sidebar flex flex-col">
            <!-- User Profile -->
            <div class="p-6 border-b border-white/10">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="trading-vivo.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-green-400"></i>
                    <span>Trading en Vivo</span>
                </a>

                <a href="videos-educativos.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-signal mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="canal-analisis.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-bar mr-3 text-pink-400"></i>
                    <span>Análisis y Certificaciones</span>
                </a>

                <a href="dashboard-personal.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-user mr-3 text-yellow-400"></i>
                    <span>Dashboard Personal</span>
                </a>
            </div>

            <!-- Back to Main Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard Principal
                </a>
            </div>

            <!-- Logout -->
            <div class="mt-4">
                <button onclick="logout()" class="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>Cerrar Sesión
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 main-content m-4 rounded-lg overflow-y-auto">
            
            <!-- Header -->
            <div class="bg-gradient-to-r from-yellow-500 to-orange-600 text-white p-6 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-3xl font-bold mb-2">👤 Dashboard Personal</h2>
                        <p class="text-yellow-100">Tu progreso y certificaciones GERMAYORI</p>
                    </div>
                    <div class="text-right">
                        <div id="user-plan" class="text-yellow-100 text-sm">Plan: Básico</div>
                        <div class="text-2xl">🏆</div>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Análisis Enviados -->
                    <div class="stat-card rounded-lg p-6 text-center">
                        <div class="text-blue-400 text-3xl mb-3">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white" id="total-analysis">0</h3>
                        <p class="text-gray-300 text-sm">Análisis Enviados</p>
                    </div>

                    <!-- Certificaciones Aprobadas -->
                    <div class="stat-card rounded-lg p-6 text-center">
                        <div class="text-green-400 text-3xl mb-3">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white" id="approved-analysis">0</h3>
                        <p class="text-gray-300 text-sm">Certificaciones Aprobadas</p>
                    </div>

                    <!-- Tasa de Éxito -->
                    <div class="stat-card rounded-lg p-6 text-center">
                        <div class="text-yellow-400 text-3xl mb-3">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white" id="success-rate">0%</h3>
                        <p class="text-gray-300 text-sm">Tasa de Éxito</p>
                    </div>

                    <!-- Nivel GERMAYORI -->
                    <div class="stat-card rounded-lg p-6 text-center">
                        <div class="text-purple-400 text-3xl mb-3">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white" id="user-level">Principiante</h3>
                        <p class="text-gray-300 text-sm">Nivel GERMAYORI</p>
                    </div>
                </div>
            </div>

            <!-- Certificaciones Recientes -->
            <div class="px-6 mb-6">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-award mr-3 text-green-400"></i>Certificaciones Recientes
                </h3>
                <div id="recent-certifications" class="space-y-4">
                    <!-- Se llenarán dinámicamente -->
                </div>
            </div>

            <!-- Notificaciones -->
            <div class="px-6 mb-6">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-bell mr-3 text-yellow-400"></i>Notificaciones
                </h3>
                <div id="notifications-container" class="space-y-3">
                    <!-- Se llenarán dinámicamente -->
                </div>
            </div>

            <!-- Progreso de Aprendizaje -->
            <div class="px-6 mb-6">
                <h3 class="text-2xl font-bold text-white mb-4">
                    <i class="fas fa-graduation-cap mr-3 text-blue-400"></i>Progreso de Aprendizaje
                </h3>
                <div class="stat-card rounded-lg p-6">
                    <div class="space-y-4">
                        <!-- Fair Value Gap -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-white font-semibold">Fair Value Gap (FVG)</span>
                                <span class="text-green-400" id="fvg-progress">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-green-400 h-2 rounded-full" style="width: 0%" id="fvg-bar"></div>
                            </div>
                        </div>

                        <!-- Order Blocks -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-white font-semibold">Order Blocks</span>
                                <span class="text-blue-400" id="ob-progress">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-400 h-2 rounded-full" style="width: 0%" id="ob-bar"></div>
                            </div>
                        </div>

                        <!-- Break of Structure -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-white font-semibold">Break of Structure</span>
                                <span class="text-purple-400" id="bos-progress">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-purple-400 h-2 rounded-full" style="width: 0%" id="bos-bar"></div>
                            </div>
                        </div>

                        <!-- Análisis Completo -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-white font-semibold">Análisis Completo GERMAYORI</span>
                                <span class="text-yellow-400" id="complete-progress">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-yellow-400 h-2 rounded-full" style="width: 0%" id="complete-bar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                document.getElementById('user-plan').textContent = `Plan: ${user.plan || 'Básico'}`;
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }
            }
        }

        // Cargar estadísticas del usuario
        function loadUserStats() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            
            // Simular datos de análisis (en producción vendría de base de datos)
            const userAnalysis = [
                { type: 'FVG', status: 'approved' },
                { type: 'Order Block', status: 'approved' },
                { type: 'BOS', status: 'pending' },
                { type: 'FVG', status: 'rejected' },
                { type: 'Completo', status: 'approved' }
            ];

            const totalAnalysis = userAnalysis.length;
            const approvedAnalysis = userAnalysis.filter(a => a.status === 'approved').length;
            const successRate = totalAnalysis > 0 ? Math.round((approvedAnalysis / totalAnalysis) * 100) : 0;

            document.getElementById('total-analysis').textContent = totalAnalysis;
            document.getElementById('approved-analysis').textContent = approvedAnalysis;
            document.getElementById('success-rate').textContent = successRate + '%';

            // Determinar nivel
            let level = 'Principiante';
            if (approvedAnalysis >= 10) level = 'Experto';
            else if (approvedAnalysis >= 5) level = 'Intermedio';
            else if (approvedAnalysis >= 2) level = 'Avanzado';

            document.getElementById('user-level').textContent = level;

            // Calcular progreso por tipo
            const fvgCount = userAnalysis.filter(a => a.type === 'FVG' && a.status === 'approved').length;
            const obCount = userAnalysis.filter(a => a.type === 'Order Block' && a.status === 'approved').length;
            const bosCount = userAnalysis.filter(a => a.type === 'BOS' && a.status === 'approved').length;
            const completeCount = userAnalysis.filter(a => a.type === 'Completo' && a.status === 'approved').length;

            updateProgressBar('fvg', Math.min(fvgCount * 25, 100));
            updateProgressBar('ob', Math.min(obCount * 25, 100));
            updateProgressBar('bos', Math.min(bosCount * 25, 100));
            updateProgressBar('complete', Math.min(completeCount * 25, 100));
        }

        // Actualizar barra de progreso
        function updateProgressBar(type, percentage) {
            document.getElementById(type + '-progress').textContent = percentage + '%';
            document.getElementById(type + '-bar').style.width = percentage + '%';
        }

        // Cargar certificaciones recientes
        function loadRecentCertifications() {
            const container = document.getElementById('recent-certifications');
            
            // Simular certificaciones (en producción vendría de base de datos)
            const certifications = [
                {
                    id: 1,
                    type: 'FVG',
                    pair: 'EUR/USD',
                    status: 'approved',
                    certification: 'Excelente identificación de Fair Value Gap. Análisis preciso según metodología GERMAYORI.',
                    date: '2024-01-15'
                },
                {
                    id: 2,
                    type: 'Order Block',
                    pair: 'GBP/USD',
                    status: 'approved',
                    certification: 'Muy buen análisis de Order Block. Correcta identificación de zona institucional.',
                    date: '2024-01-14'
                }
            ];

            if (certifications.length === 0) {
                container.innerHTML = `
                    <div class="stat-card rounded-lg p-6 text-center">
                        <div class="text-gray-400">
                            <i class="fas fa-certificate text-4xl mb-4"></i>
                            <p>No tienes certificaciones aún</p>
                            <p class="text-sm mt-2">¡Sube tu primer análisis para recibir certificación!</p>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = certifications.map(cert => `
                <div class="certification-badge rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <i class="fas fa-certificate text-white text-xl mr-3"></i>
                            <div>
                                <h4 class="text-white font-bold">${cert.type} - ${cert.pair}</h4>
                                <p class="text-green-100 text-sm">Certificado por jhon0608</p>
                            </div>
                        </div>
                        <span class="text-xs bg-white text-green-600 px-3 py-1 rounded-full font-bold">✅ APROBADO</span>
                    </div>
                    <p class="text-green-100 text-sm">${cert.certification}</p>
                    <p class="text-green-200 text-xs mt-2">Fecha: ${cert.date}</p>
                </div>
            `).join('');
        }

        // Cargar notificaciones
        function loadNotifications() {
            const container = document.getElementById('notifications-container');
            const notifications = JSON.parse(localStorage.getItem('user_notifications') || '[]');

            if (notifications.length === 0) {
                container.innerHTML = `
                    <div class="notification-item rounded-lg p-4">
                        <div class="text-gray-400 text-center">
                            <i class="fas fa-bell text-2xl mb-2"></i>
                            <p>No tienes notificaciones</p>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = notifications.slice(0, 5).map(notif => `
                <div class="notification-item rounded-lg p-4">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h4 class="text-white font-semibold mb-1">${notif.title}</h4>
                            <p class="text-gray-300 text-sm mb-2">${notif.message}</p>
                            <p class="text-gray-400 text-xs">${new Date(notif.timestamp).toLocaleString('es-ES')}</p>
                        </div>
                        <i class="fas fa-${notif.type === 'certification' ? 'certificate' : 'bell'} text-green-400"></i>
                    </div>
                </div>
            `).join('');
        }

        // Función para cerrar sesión
        function logout() {
            localStorage.removeItem('germayori_user');
            localStorage.removeItem('germayori_selected_plan');
            localStorage.removeItem('germayori_token');
            alert('Sesión cerrada exitosamente');
            window.location.href = 'index.html';
        }

        // Inicializar página
        window.onload = function() {
            const user = checkAuth();
            if (user) {
                loadUserInfo();
                loadUserStats();
                loadRecentCertifications();
                loadNotifications();
            }
        };
    </script>
</body>
</html>
