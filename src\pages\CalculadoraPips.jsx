import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calculator, DollarSign, TrendingUp, Target, ExternalLink, Info } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';

const CalculadoraPips = () => {
  const [formData, setFormData] = useState({
    pair: 'EURUSD',
    accountCurrency: 'USD',
    lotSize: '1',
    entryPrice: '',
    exitPrice: '',
    tradeType: 'buy'
  });

  const [results, setResults] = useState(null);

  const majorPairs = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 'AUDUSD', 'NZDUSD'
  ];

  const calculatePips = () => {
    const entry = parseFloat(formData.entryPrice);
    const exit = parseFloat(formData.exitPrice);
    const lots = parseFloat(formData.lotSize);

    if (!entry || !exit || !lots) return;

    let pips = 0;
    let pipValue = 0;
    let profit = 0;

    // Calcular pips según el par
    if (formData.pair.includes('JPY')) {
      // Pares con JPY (2 decimales)
      pips = formData.tradeType === 'buy' 
        ? (exit - entry) * 100 
        : (entry - exit) * 100;
      pipValue = (lots * 100000 * 0.01) / exit;
    } else {
      // Pares mayores (4 decimales)
      pips = formData.tradeType === 'buy' 
        ? (exit - entry) * 10000 
        : (entry - exit) * 10000;
      pipValue = (lots * 100000 * 0.0001) / 1; // Simplificado para USD
    }

    profit = pips * pipValue * lots;

    setResults({
      pips: Math.round(pips * 100) / 100,
      pipValue: Math.round(pipValue * 100) / 100,
      profit: Math.round(profit * 100) / 100,
      profitPercentage: Math.round((profit / (lots * 100000 * entry)) * 10000) / 100
    });
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <div className="w-16 h-16 bg-success bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
          <Calculator className="text-success" size={32} />
        </div>
        <h1 className="text-3xl font-bold text-text mb-2">Calculadora de Pips</h1>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Calcula pips, valor por pip y ganancias/pérdidas de tus operaciones forex
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Formulario de Cálculo */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-text mb-6 flex items-center">
              <Calculator className="mr-2 text-success" size={20} />
              Parámetros de Cálculo
            </h3>

            <div className="space-y-4">
              {/* Par de Divisas */}
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Par de Divisas
                </label>
                <select
                  value={formData.pair}
                  onChange={(e) => handleInputChange('pair', e.target.value)}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text focus:border-danger focus:outline-none"
                >
                  {majorPairs.map(pair => (
                    <option key={pair} value={pair}>{pair}</option>
                  ))}
                </select>
              </div>

              {/* Tipo de Operación */}
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Tipo de Operación
                </label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleInputChange('tradeType', 'buy')}
                    className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
                      formData.tradeType === 'buy'
                        ? 'bg-success text-white'
                        : 'bg-accent text-gray-400 hover:text-white'
                    }`}
                  >
                    Compra (Buy)
                  </button>
                  <button
                    onClick={() => handleInputChange('tradeType', 'sell')}
                    className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
                      formData.tradeType === 'sell'
                        ? 'bg-danger text-white'
                        : 'bg-accent text-gray-400 hover:text-white'
                    }`}
                  >
                    Venta (Sell)
                  </button>
                </div>
              </div>

              {/* Tamaño del Lote */}
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Tamaño del Lote
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.lotSize}
                  onChange={(e) => handleInputChange('lotSize', e.target.value)}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text focus:border-danger focus:outline-none"
                  placeholder="1.00"
                />
              </div>

              {/* Precio de Entrada */}
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Precio de Entrada
                </label>
                <input
                  type="number"
                  step="0.00001"
                  value={formData.entryPrice}
                  onChange={(e) => handleInputChange('entryPrice', e.target.value)}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text focus:border-danger focus:outline-none"
                  placeholder="1.08500"
                />
              </div>

              {/* Precio de Salida */}
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Precio de Salida
                </label>
                <input
                  type="number"
                  step="0.00001"
                  value={formData.exitPrice}
                  onChange={(e) => handleInputChange('exitPrice', e.target.value)}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text focus:border-danger focus:outline-none"
                  placeholder="1.08750"
                />
              </div>

              <Button 
                variant="primary" 
                onClick={calculatePips}
                className="w-full"
              >
                <Calculator size={16} className="mr-2" />
                Calcular
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Resultados */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-text mb-6 flex items-center">
              <Target className="mr-2 text-warning" size={20} />
              Resultados
            </h3>

            {results ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-primary rounded-lg">
                    <div className="text-2xl font-bold text-text mb-1">
                      {results.pips > 0 ? '+' : ''}{results.pips}
                    </div>
                    <div className="text-sm text-gray-400">Pips</div>
                  </div>
                  <div className="text-center p-4 bg-primary rounded-lg">
                    <div className="text-2xl font-bold text-text mb-1">
                      ${results.pipValue}
                    </div>
                    <div className="text-sm text-gray-400">Valor por Pip</div>
                  </div>
                </div>

                <div className="text-center p-6 bg-gradient-to-r from-danger to-warning rounded-lg">
                  <div className={`text-3xl font-bold mb-2 ${results.profit >= 0 ? 'text-white' : 'text-white'}`}>
                    {results.profit >= 0 ? '+' : ''}${results.profit}
                  </div>
                  <div className="text-white text-sm opacity-90">
                    {results.profit >= 0 ? 'Ganancia' : 'Pérdida'} Total
                  </div>
                  <div className="text-white text-xs opacity-75 mt-1">
                    ({results.profitPercentage >= 0 ? '+' : ''}{results.profitPercentage}%)
                  </div>
                </div>

                <div className="bg-accent bg-opacity-20 rounded-lg p-4">
                  <div className="flex items-start space-x-2">
                    <Info className="text-accent mt-0.5" size={16} />
                    <div className="text-sm text-gray-400">
                      <p className="mb-1">
                        <strong>Par:</strong> {formData.pair} | 
                        <strong> Lotes:</strong> {formData.lotSize} | 
                        <strong> Tipo:</strong> {formData.tradeType === 'buy' ? 'Compra' : 'Venta'}
                      </p>
                      <p>
                        <strong>Entrada:</strong> {formData.entryPrice} → 
                        <strong> Salida:</strong> {formData.exitPrice}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <DollarSign className="mx-auto text-gray-500 mb-4" size={48} />
                <p className="text-gray-400">
                  Completa los campos y presiona "Calcular" para ver los resultados
                </p>
              </div>
            )}
          </Card>
        </motion.div>
      </div>

      {/* Enlace a MyFXBook */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="text-center">
          <h3 className="text-lg font-semibold text-text mb-4">
            ¿Necesitas una calculadora más avanzada?
          </h3>
          <p className="text-gray-400 mb-6">
            MyFXBook ofrece una calculadora profesional con más opciones y pares de divisas
          </p>
          <Button 
            variant="outline"
            onClick={() => window.open('https://www.myfxbook.com/forex-calculators/profit-calculator', '_blank')}
          >
            <ExternalLink size={16} className="mr-2" />
            Ir a MyFXBook Calculator
          </Button>
        </Card>
      </motion.div>
    </div>
  );
};

export default CalculadoraPips;
