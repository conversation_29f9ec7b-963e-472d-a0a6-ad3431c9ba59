import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Users, 
  Send, 
  Bell, 
  Upload, 
  BarChart3,
  MessageSquare,
  Video,
  TrendingUp,
  TrendingDown,
  Crown,
  Eye,
  DollarSign
} from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';

const AdminPanel = () => {
  const [activeTab, setActiveTab] = useState('senales');
  const [signalForm, setSignalForm] = useState({
    pair: 'EURUSD',
    type: 'buy',
    entry: '',
    sl: '',
    tp: '',
    description: ''
  });

  const [alertForm, setAlertForm] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  const [videoForm, setVideoForm] = useState({
    title: '',
    description: '',
    url: '',
    category: 'basico'
  });

  const stats = {
    totalUsers: 1247,
    premiumUsers: 89,
    freeUsers: 1158,
    signalsSent: 156,
    videosUploaded: 23
  };

  const handleSendSignal = () => {
    // Aquí enviarías la señal a todos los usuarios premium
    console.log('Enviando señal:', signalForm);
    alert('¡Señal enviada a usuarios premium!');
    setSignalForm({
      pair: 'EURUSD',
      type: 'buy',
      entry: '',
      sl: '',
      tp: '',
      description: ''
    });
  };

  const handleSendAlert = () => {
    // Aquí enviarías la alerta/notificación
    console.log('Enviando alerta:', alertForm);
    alert('¡Alerta enviada a todos los usuarios!');
    setAlertForm({
      title: '',
      message: '',
      type: 'info'
    });
  };

  const handleUploadVideo = () => {
    // Aquí subirías el video
    console.log('Subiendo video:', videoForm);
    alert('¡Video subido exitosamente!');
    setVideoForm({
      title: '',
      description: '',
      url: '',
      category: 'basico'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <div className="w-16 h-16 bg-danger bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
          <Settings className="text-danger" size={32} />
        </div>
        <h1 className="text-3xl font-bold text-text mb-2">Panel de Administración</h1>
        <p className="text-gray-400">Gestiona señales, alertas, videos y usuarios</p>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="text-center">
          <Users className="mx-auto text-accent mb-2" size={24} />
          <h3 className="text-xl font-bold text-text">{stats.totalUsers}</h3>
          <p className="text-sm text-gray-400">Total Usuarios</p>
        </Card>
        <Card className="text-center">
          <Crown className="mx-auto text-warning mb-2" size={24} />
          <h3 className="text-xl font-bold text-text">{stats.premiumUsers}</h3>
          <p className="text-sm text-gray-400">Premium</p>
        </Card>
        <Card className="text-center">
          <Eye className="mx-auto text-gray-400 mb-2" size={24} />
          <h3 className="text-xl font-bold text-text">{stats.freeUsers}</h3>
          <p className="text-sm text-gray-400">Gratuitos</p>
        </Card>
        <Card className="text-center">
          <Send className="mx-auto text-success mb-2" size={24} />
          <h3 className="text-xl font-bold text-text">{stats.signalsSent}</h3>
          <p className="text-sm text-gray-400">Señales</p>
        </Card>
        <Card className="text-center">
          <Video className="mx-auto text-purple-400 mb-2" size={24} />
          <h3 className="text-xl font-bold text-text">{stats.videosUploaded}</h3>
          <p className="text-sm text-gray-400">Videos</p>
        </Card>
      </div>

      {/* Tabs */}
      <Card>
        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => setActiveTab('senales')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'senales'
                ? 'bg-danger text-white'
                : 'text-gray-400 hover:text-white hover:bg-accent'
            }`}
          >
            <Send size={16} className="inline mr-2" />
            Enviar Señales
          </button>
          <button
            onClick={() => setActiveTab('alertas')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'alertas'
                ? 'bg-danger text-white'
                : 'text-gray-400 hover:text-white hover:bg-accent'
            }`}
          >
            <Bell size={16} className="inline mr-2" />
            Alertas
          </button>
          <button
            onClick={() => setActiveTab('videos')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'videos'
                ? 'bg-danger text-white'
                : 'text-gray-400 hover:text-white hover:bg-accent'
            }`}
          >
            <Video size={16} className="inline mr-2" />
            Videos
          </button>
          <button
            onClick={() => setActiveTab('usuarios')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              activeTab === 'usuarios'
                ? 'bg-danger text-white'
                : 'text-gray-400 hover:text-white hover:bg-accent'
            }`}
          >
            <Users size={16} className="inline mr-2" />
            Usuarios
          </button>
        </div>

        {/* Enviar Señales */}
        {activeTab === 'senales' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text mb-4">Enviar Nueva Señal</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">Par de Divisas</label>
                <select
                  value={signalForm.pair}
                  onChange={(e) => setSignalForm({...signalForm, pair: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                >
                  <option value="EURUSD">EUR/USD</option>
                  <option value="GBPUSD">GBP/USD</option>
                  <option value="USDJPY">USD/JPY</option>
                  <option value="AUDUSD">AUD/USD</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Tipo</label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSignalForm({...signalForm, type: 'buy'})}
                    className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
                      signalForm.type === 'buy'
                        ? 'bg-success text-white'
                        : 'bg-accent text-gray-400'
                    }`}
                  >
                    <TrendingUp size={16} className="inline mr-1" />
                    BUY
                  </button>
                  <button
                    onClick={() => setSignalForm({...signalForm, type: 'sell'})}
                    className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
                      signalForm.type === 'sell'
                        ? 'bg-danger text-white'
                        : 'bg-accent text-gray-400'
                    }`}
                  >
                    <TrendingDown size={16} className="inline mr-1" />
                    SELL
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Precio de Entrada</label>
                <input
                  type="number"
                  step="0.00001"
                  value={signalForm.entry}
                  onChange={(e) => setSignalForm({...signalForm, entry: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  placeholder="1.08500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Stop Loss</label>
                <input
                  type="number"
                  step="0.00001"
                  value={signalForm.sl}
                  onChange={(e) => setSignalForm({...signalForm, sl: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  placeholder="1.08200"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Take Profit</label>
                <input
                  type="number"
                  step="0.00001"
                  value={signalForm.tp}
                  onChange={(e) => setSignalForm({...signalForm, tp: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  placeholder="1.08800"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Descripción</label>
                <textarea
                  value={signalForm.description}
                  onChange={(e) => setSignalForm({...signalForm, description: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  rows="3"
                  placeholder="Análisis y razón de la señal..."
                />
              </div>
            </div>
            <Button variant="primary" onClick={handleSendSignal} className="w-full">
              <Send size={16} className="mr-2" />
              Enviar Señal a Usuarios Premium
            </Button>
          </div>
        )}

        {/* Enviar Alertas */}
        {activeTab === 'alertas' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text mb-4">Enviar Alerta/Notificación</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">Título</label>
                <input
                  type="text"
                  value={alertForm.title}
                  onChange={(e) => setAlertForm({...alertForm, title: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  placeholder="Título de la alerta"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Mensaje</label>
                <textarea
                  value={alertForm.message}
                  onChange={(e) => setAlertForm({...alertForm, message: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  rows="4"
                  placeholder="Mensaje de la alerta..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Tipo</label>
                <select
                  value={alertForm.type}
                  onChange={(e) => setAlertForm({...alertForm, type: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                >
                  <option value="info">Información</option>
                  <option value="warning">Advertencia</option>
                  <option value="success">Éxito</option>
                  <option value="danger">Urgente</option>
                </select>
              </div>
            </div>
            <Button variant="primary" onClick={handleSendAlert} className="w-full">
              <Bell size={16} className="mr-2" />
              Enviar Alerta a Todos los Usuarios
            </Button>
          </div>
        )}

        {/* Subir Videos */}
        {activeTab === 'videos' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text mb-4">Subir Nuevo Video</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">Título del Video</label>
                <input
                  type="text"
                  value={videoForm.title}
                  onChange={(e) => setVideoForm({...videoForm, title: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  placeholder="Título del video educativo"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Descripción</label>
                <textarea
                  value={videoForm.description}
                  onChange={(e) => setVideoForm({...videoForm, description: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  rows="3"
                  placeholder="Descripción del contenido..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">URL del Video</label>
                <input
                  type="url"
                  value={videoForm.url}
                  onChange={(e) => setVideoForm({...videoForm, url: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                  placeholder="https://youtube.com/watch?v=..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text mb-2">Categoría</label>
                <select
                  value={videoForm.category}
                  onChange={(e) => setVideoForm({...videoForm, category: e.target.value})}
                  className="w-full bg-primary border border-accent rounded-lg px-3 py-2 text-text"
                >
                  <option value="basico">Básico (Gratuito)</option>
                  <option value="intermedio">Intermedio (Premium)</option>
                  <option value="avanzado">Avanzado (Premium)</option>
                  <option value="estrategias">Estrategias (Premium)</option>
                </select>
              </div>
            </div>
            <Button variant="primary" onClick={handleUploadVideo} className="w-full">
              <Upload size={16} className="mr-2" />
              Subir Video
            </Button>
          </div>
        )}

        {/* Gestión de Usuarios */}
        {activeTab === 'usuarios' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text mb-4">Gestión de Usuarios</h3>
            <div className="bg-primary rounded-lg p-4">
              <p className="text-gray-400 text-center">
                Aquí podrás ver la lista de usuarios, cambiar membresías, y gestionar accesos.
              </p>
              <div className="mt-4 text-center">
                <Button variant="outline">
                  <Users size={16} className="mr-2" />
                  Ver Lista de Usuarios
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AdminPanel;
