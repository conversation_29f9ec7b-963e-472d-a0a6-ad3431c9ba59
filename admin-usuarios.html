<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Admin <PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.1.24.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen p-4">

    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">💥 GERMAYORI ADMIN</h1>
            <p class="text-white/80">Panel de Usuarios Registrados</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="glass rounded-lg p-6 text-center">
                <i class="fas fa-users text-3xl text-cyan-400 mb-2"></i>
                <h3 class="text-2xl font-bold text-white" id="total-users">0</h3>
                <p class="text-white/60">Total Usuarios</p>
            </div>
            <div class="glass rounded-lg p-6 text-center">
                <i class="fas fa-crown text-3xl text-yellow-400 mb-2"></i>
                <h3 class="text-2xl font-bold text-white" id="premium-users">0</h3>
                <p class="text-white/60">Usuarios Premium</p>
            </div>
            <div class="glass rounded-lg p-6 text-center">
                <i class="fas fa-calendar text-3xl text-green-400 mb-2"></i>
                <h3 class="text-2xl font-bold text-white" id="today-registrations">0</h3>
                <p class="text-white/60">Hoy</p>
            </div>
            <div class="glass rounded-lg p-6 text-center">
                <i class="fas fa-dollar-sign text-3xl text-purple-400 mb-2"></i>
                <h3 class="text-2xl font-bold text-white" id="monthly-revenue">$0</h3>
                <p class="text-white/60">Ingresos/Mes</p>
            </div>
        </div>

        <!-- Controls -->
        <div class="glass rounded-lg p-6 mb-6">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-bold text-white">👥 Usuarios Registrados</h2>
                <div class="flex gap-3">
                    <button onclick="loadUsers()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        <i class="fas fa-sync-alt mr-2"></i>Actualizar
                    </button>
                    <button onclick="exportUsers()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-download mr-2"></i>Exportar
                    </button>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="glass rounded-lg p-6">
            <div id="loading" class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-white text-2xl mb-2"></i>
                <p class="text-white">Cargando usuarios desde AWS...</p>
            </div>

            <div id="users-container" class="hidden">
                <div class="overflow-x-auto">
                    <table class="w-full text-white">
                        <thead>
                            <tr class="border-b border-white/20">
                                <th class="text-left p-3">Usuario</th>
                                <th class="text-left p-3">Email</th>
                                <th class="text-left p-3">Plan</th>
                                <th class="text-left p-3">Registro</th>
                                <th class="text-left p-3">Estado</th>
                                <th class="text-left p-3">Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="users-table">
                            <!-- Los usuarios se cargarán aquí -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div id="no-users" class="text-center py-8 hidden">
                <i class="fas fa-user-slash text-white/40 text-3xl mb-2"></i>
                <p class="text-white/60">No hay usuarios registrados</p>
            </div>
        </div>

        <!-- Back Button -->
        <div class="text-center mt-8">
            <a href="dashboard-simple.html" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all">
                <i class="fas fa-arrow-left mr-2"></i>Volver al Dashboard
            </a>
        </div>
    </div>

    <script>
        // Configuración AWS
        AWS.config.update({
            region: 'us-east-1',
            accessKeyId: 'AKIAZI6IXQHQVQHQHQHQ',
            secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'
        });

        const dynamodb = new AWS.DynamoDB.DocumentClient();
        let allUsers = [];

        // Cargar usuarios desde AWS
        async function loadUsers() {
            const loading = document.getElementById('loading');
            const container = document.getElementById('users-container');
            const noUsers = document.getElementById('no-users');

            loading.classList.remove('hidden');
            container.classList.add('hidden');
            noUsers.classList.add('hidden');

            try {
                const params = {
                    TableName: 'germayori-users'
                };

                const result = await dynamodb.scan(params).promise();
                allUsers = result.Items || [];

                console.log('✅ Usuarios cargados desde AWS:', allUsers);
                displayUsers(allUsers);
                updateStats(allUsers);

            } catch (error) {
                console.error('Error cargando usuarios:', error);

                // Si falla AWS, mostrar mensaje
                loading.classList.add('hidden');
                noUsers.classList.remove('hidden');
                document.getElementById('no-users').innerHTML = `
                    <i class="fas fa-exclamation-triangle text-red-400 text-3xl mb-2"></i>
                    <p class="text-red-400">Error conectando con AWS</p>
                    <button onclick="loadUsers()" class="mt-2 bg-red-600 text-white px-4 py-2 rounded">Reintentar</button>
                `;
            }
        }

        // Mostrar usuarios en tabla
        function displayUsers(users) {
            const loading = document.getElementById('loading');
            const container = document.getElementById('users-container');
            const noUsers = document.getElementById('no-users');
            const tableBody = document.getElementById('users-table');

            loading.classList.add('hidden');

            if (users.length === 0) {
                noUsers.classList.remove('hidden');
                return;
            }

            container.classList.remove('hidden');

            tableBody.innerHTML = users.map(user => `
                <tr class="border-b border-white/10 hover:bg-white/5">
                    <td class="p-3">
                        <div class="flex items-center">
                            <img src="${user.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(user.fullName)}"
                                 class="w-8 h-8 rounded-full mr-3" alt="Avatar">
                            <div>
                                <div class="font-semibold">${user.fullName}</div>
                                <div class="text-sm text-white/60">${user.phone || 'Sin teléfono'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="p-3">${user.email}</td>
                    <td class="p-3">
                        <span class="bg-${getPlanColor(user.plan)}-500 text-white px-2 py-1 rounded text-xs">
                            ${user.plan.toUpperCase()}
                        </span>
                    </td>
                    <td class="p-3 text-sm">${new Date(user.registrationDate).toLocaleDateString()}</td>
                    <td class="p-3">
                        <span class="bg-green-500 text-white px-2 py-1 rounded text-xs">
                            ${user.status || 'ACTIVO'}
                        </span>
                    </td>
                    <td class="p-3">
                        <button onclick="viewUser('${user.id}')" class="bg-blue-500 text-white px-2 py-1 rounded text-xs mr-1">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="deleteUser('${user.id}')" class="bg-red-500 text-white px-2 py-1 rounded text-xs">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Actualizar estadísticas
        function updateStats(users) {
            const total = users.length;
            const premium = users.filter(u => ['avanzado', 'completo'].includes(u.plan)).length;
            const today = users.filter(u => {
                const userDate = new Date(u.registrationDate).toDateString();
                const todayDate = new Date().toDateString();
                return userDate === todayDate;
            }).length;

            const planPrices = {
                'senales': 25,
                'basico': 45,
                'intermedio': 70,
                'avanzado': 95,
                'completo': 140
            };

            const revenue = users.reduce((sum, user) => sum + (planPrices[user.plan] || 0), 0);

            document.getElementById('total-users').textContent = total;
            document.getElementById('premium-users').textContent = premium;
            document.getElementById('today-registrations').textContent = today;
            document.getElementById('monthly-revenue').textContent = '$' + revenue;
        }

        // Obtener color del plan
        function getPlanColor(plan) {
            const colors = {
                'senales': 'blue',
                'basico': 'green',
                'intermedio': 'yellow',
                'avanzado': 'purple',
                'completo': 'red'
            };
            return colors[plan] || 'gray';
        }

        // Ver usuario
        function viewUser(userId) {
            const user = allUsers.find(u => u.id === userId);
            if (user) {
                alert(`Usuario: ${user.fullName}\nEmail: ${user.email}\nPlan: ${user.plan}\nRegistro: ${new Date(user.registrationDate).toLocaleString()}`);
            }
        }

        // Eliminar usuario
        async function deleteUser(userId) {
            if (confirm('¿Estás seguro de eliminar este usuario?')) {
                try {
                    const params = {
                        TableName: 'germayori-users',
                        Key: { id: userId }
                    };

                    await dynamodb.delete(params).promise();
                    alert('✅ Usuario eliminado');
                    loadUsers();
                } catch (error) {
                    console.error('Error eliminando usuario:', error);
                    alert('❌ Error eliminando usuario');
                }
            }
        }

        // Exportar usuarios
        function exportUsers() {
            const csv = 'Nombre,Email,Plan,Teléfono,Registro\n' +
                allUsers.map(u => `${u.fullName},${u.email},${u.plan},${u.phone || ''},${u.registrationDate}`).join('\n');

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'germayori-usuarios.csv';
            a.click();
        }

        // Cargar usuarios al iniciar
        window.onload = function() {
            loadUsers();
        };
    </script>
</body>
</html>
