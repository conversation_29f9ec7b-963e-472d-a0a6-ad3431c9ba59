<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canal Videos Educativos - GERMAYORI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #fff;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #FFD700;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .categories {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .category-btn {
            background: rgba(255, 215, 0, 0.2);
            color: #FFD700;
            border: 2px solid #FFD700;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .category-btn.active {
            background: #FFD700;
            color: #000;
        }

        .videos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .video-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .video-card:hover {
            border-color: #FFD700;
            transform: translateY(-5px);
        }

        .video-thumbnail {
            width: 100%;
            height: 180px;
            background: #333;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .play-icon {
            font-size: 3rem;
            color: #FFD700;
        }

        .video-title {
            color: #FFD700;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border: 2px dashed #FFD700;
        }

        .upload-btn {
            background: #FFD700;
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
        }

        /* Modal SIMPLE que SÍ FUNCIONA */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1a1a1a;
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #FFD700;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff4444;
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
        }

        .video-player {
            width: 800px;
            height: 450px;
            background: #000;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .subtitles-area {
            background: rgba(0, 0, 0, 0.9);
            color: #FFD700;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            border: 2px solid #FFD700;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="text-align: left; margin-bottom: 20px;">
                <a href="dashboard-simple.html" style="background: #0066cc; color: white; padding: 12px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block; transition: all 0.3s ease;">
                    ← Volver al Dashboard
                </a>
            </div>
            <h1>📚 Canal Videos Educativos GERMAYORI</h1>
            <p>Contenido educativo premium de trading institucional</p>
        </div>

        <div class="categories">
            <button class="category-btn active" onclick="showCategory('basico')">📖 Básico</button>
            <button class="category-btn" onclick="showCategory('intermedio')">📊 Intermedio</button>
            <button class="category-btn" onclick="showCategory('avanzado')">🚀 Avanzado</button>
            <button class="category-btn" onclick="showCategory('videos-del-dia')" style="background: #ff6600; border-color: #ff6600;">🔴 Videos del Día EN VIVO</button>
            <button class="category-btn" onclick="showCategory('zoom-live')" style="background: #0066cc; border-color: #0066cc;">📹 Zoom EN VIVO</button>
        </div>

        <div class="upload-section" id="normalUploadSection">
            <h3>📤 Gestión de Videos</h3>
            <input type="file" id="fileInput" accept="video/*" style="display: none;">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">📹 Seleccionar Video</button>
            <button class="upload-btn" onclick="addYouTubeVideo()">🔗 Agregar YouTube</button>
            <button class="upload-btn" onclick="deleteAllVideos()" style="background: #ff4444; color: white;">🗑️ Borrar Todos</button>
        </div>

        <!-- Sección especial para Videos del Día EN VIVO -->
        <div class="upload-section" id="videosDelDiaSection" style="display: none; background: rgba(255, 102, 0, 0.1); border: 2px solid #ff6600;">
            <h3 style="color: #ff6600;">🔴 Videos del Día EN VIVO - GERMAYORI</h3>
            <p style="color: #ccc; margin-bottom: 20px;">Videos grabados automáticamente desde las sesiones de Zoom del día</p>

            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <input type="file" id="liveVideoInput" accept="video/*" style="display: none;">
                <button class="upload-btn" onclick="document.getElementById('liveVideoInput').click()" style="background: #ff6600;">📹 Subir Video EN VIVO</button>
                <button class="upload-btn" onclick="addZoomRecording()" style="background: #0066cc;">🎥 Agregar Grabación Zoom</button>
                <button class="upload-btn" onclick="markAsLiveVideo()" style="background: #00aa00;">🔴 Marcar como EN VIVO</button>
                <button class="upload-btn" onclick="deleteAllLiveVideos()" style="background: #ff4444; color: white;">🗑️ Borrar Videos del Día</button>
            </div>

            <div style="margin-top: 15px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 10px; text-align: center;">
                <div style="color: #ff6600; font-weight: bold; margin-bottom: 10px;">📊 Estadísticas del Día</div>
                <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                    <span style="color: #ccc;">📹 Videos: <span id="liveVideoCount">0</span></span>
                    <span style="color: #ccc;">⏱️ Duración Total: <span id="totalDuration">0:00</span></span>
                    <span style="color: #ccc;">📅 Fecha: <span id="todayDate"></span></span>
                </div>
            </div>
        </div>

        <!-- Sección especial para Zoom EN VIVO -->
        <div id="zoomLiveSection" style="display: none;">
            <div style="background: rgba(0, 102, 204, 0.1); border: 2px solid #0066cc; border-radius: 15px; padding: 30px; margin-bottom: 30px;">
                <h3 style="color: #0066cc; text-align: center; margin-bottom: 20px;">📹 ZOOM EN VIVO - GERMAYORI</h3>

                <!-- Área de transmisión en vivo -->
                <div style="background: #000; border-radius: 10px; padding: 20px; margin-bottom: 20px;">
                    <div id="zoomContainer" style="width: 100%; height: 500px; background: #1a1a1a; border-radius: 10px; display: flex; align-items: center; justify-content: center; border: 2px solid #0066cc;">
                        <div style="text-align: center; color: #0066cc;">
                            <h3>📹 Transmisión Zoom EN VIVO</h3>
                            <p>Conecta tu reunión de Zoom aquí</p>
                            <button onclick="startZoomLive()" style="background: #0066cc; color: white; border: none; padding: 15px 30px; border-radius: 25px; font-size: 16px; font-weight: bold; cursor: pointer; margin: 10px;">🔴 INICIAR TRANSMISIÓN</button>
                        </div>
                    </div>
                </div>

                <!-- Controles de grabación -->
                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                    <button onclick="startRecording()" style="background: #ff4444; color: white; border: none; padding: 12px 25px; border-radius: 20px; font-weight: bold; cursor: pointer;">🔴 GRABAR</button>
                    <button onclick="stopRecording()" style="background: #666; color: white; border: none; padding: 12px 25px; border-radius: 20px; font-weight: bold; cursor: pointer;">⏹️ PARAR</button>
                    <button onclick="saveRecording()" style="background: #00aa00; color: white; border: none; padding: 12px 25px; border-radius: 20px; font-weight: bold; cursor: pointer;">💾 GUARDAR VIDEO</button>
                    <input type="text" id="zoomUrl" placeholder="URL de Zoom Meeting" style="padding: 12px; border-radius: 20px; border: 2px solid #0066cc; background: rgba(255,255,255,0.1); color: white; width: 300px;">
                    <button onclick="connectZoom()" style="background: #0066cc; color: white; border: none; padding: 12px 25px; border-radius: 20px; font-weight: bold; cursor: pointer;">🔗 CONECTAR</button>
                </div>

                <!-- Estado de la transmisión -->
                <div id="liveStatus" style="text-align: center; padding: 15px; background: rgba(0,0,0,0.5); border-radius: 10px; color: #0066cc; font-weight: bold;">
                    ⚪ Desconectado - Listo para transmitir
                </div>
            </div>

            <!-- Videos guardados de Zoom -->
            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #FFD700; margin-bottom: 15px;">📼 Videos Guardados de Zoom</h3>
                <div id="zoomVideosGrid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <!-- Videos de Zoom guardados aparecerán aquí -->
                </div>
            </div>
        </div>

        <div class="videos-grid" id="videosGrid"></div>
    </div>

    <!-- Modal SIMPLE -->
    <div class="modal" id="videoModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeVideo()">&times;</button>
            <h3 id="modalTitle" style="color: #FFD700; text-align: center; margin-bottom: 20px;"></h3>
            <video id="videoPlayer" class="video-player" controls autoplay>
                Tu navegador no soporta video HTML5.
            </video>
            <div class="subtitles-area">
                <div id="subtitleText">Bienvenidos traders, soy jhon0608 de GERMAYORI</div>
            </div>
        </div>
    </div>

    <script>
        let videos = [];
        let currentCategory = 'basico';

        // Inicializar
        document.addEventListener('DOMContentLoaded', function() {
            loadVideos();
            showCategory('basico');
        });

        function loadVideos() {
            const saved = localStorage.getItem('germayori_videos');
            if (saved) {
                videos = JSON.parse(saved);
            } else {
                // Iniciar con lista vacía para que jhon0608 suba sus propios videos
                videos = [];
                localStorage.setItem('germayori_videos', JSON.stringify(videos));
            }
        }

        function showCategory(category) {
            currentCategory = category;
            document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Ocultar todas las secciones especiales primero
            document.getElementById('zoomLiveSection').style.display = 'none';
            document.getElementById('normalUploadSection').style.display = 'block';
            document.getElementById('videosDelDiaSection').style.display = 'none';

            // Mostrar/ocultar secciones según la categoría
            if (category === 'zoom-live') {
                document.getElementById('zoomLiveSection').style.display = 'block';
                document.getElementById('videosGrid').style.display = 'none';
                document.getElementById('normalUploadSection').style.display = 'none';
                loadZoomVideos();
            } else if (category === 'videos-del-dia') {
                document.getElementById('videosGrid').style.display = 'grid';
                document.getElementById('normalUploadSection').style.display = 'none';
                document.getElementById('videosDelDiaSection').style.display = 'block';
                displayVideos(category);
                updateLiveVideoStats();
            } else {
                document.getElementById('videosGrid').style.display = 'grid';
                displayVideos(category);
            }
        }

        function displayVideos(category) {
            const grid = document.getElementById('videosGrid');
            const categoryVideos = videos.filter(v => v.category === category);

            if (categoryVideos.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #ccc; padding: 50px;">No hay videos en esta categoría</div>';
                return;
            }

            grid.innerHTML = categoryVideos.map(video => `
                <div class="video-card">
                    <div class="video-thumbnail" onclick="playVideo('${video.id}')">
                        <div class="play-icon">▶️</div>
                    </div>
                    <div class="video-title">${video.title}</div>
                    <div style="color: #ccc; margin-bottom: 10px;">${video.description}</div>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="playVideo('${video.id}')" style="background: #FFD700; color: #000; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">▶️ Reproducir</button>
                        <button onclick="deleteVideo('${video.id}')" style="background: #ff4444; color: white; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">🗑️ Eliminar</button>
                    </div>
                </div>
            `).join('');
        }

        function playVideo(videoId) {
            const video = videos.find(v => v.id === videoId);
            if (!video) return;

            console.log('🎬 Reproduciendo:', video);

            document.getElementById('modalTitle').textContent = video.title;
            const player = document.getElementById('videoPlayer');

            // Usar URL del video o fallback
            const videoSrc = video.url || video.data || 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
            player.src = videoSrc;

            console.log('📺 Video source:', videoSrc);

            document.getElementById('videoModal').style.display = 'block';

            // Subtítulos SIMPLES
            startSubtitles();
        }

        function startSubtitles() {
            const subtitles = [
                'Bienvenidos traders, soy jhon0608 de GERMAYORI',
                'En este video analizaremos estrategias FVG institucionales',
                'Observen cómo identificar Fair Value Gaps',
                'Los institucionales manipulan estos niveles clave',
                'Order Blocks en zonas de alta probabilidad',
                'Gracias por ver contenido educativo GERMAYORI'
            ];

            let index = 0;
            document.getElementById('subtitleText').textContent = subtitles[0];

            setInterval(() => {
                index = (index + 1) % subtitles.length;
                document.getElementById('subtitleText').textContent = subtitles[index];
            }, 5000);
        }

        function closeVideo() {
            document.getElementById('videoModal').style.display = 'none';
            const player = document.getElementById('videoPlayer');
            player.pause();
            player.src = '';
        }

        // Función para eliminar videos
        function deleteVideo(videoId) {
            const video = videos.find(v => v.id === videoId);
            if (!video) return;

            const confirmDelete = confirm(`¿Estás seguro de que quieres eliminar el video "${video.title}"?\n\nEsta acción no se puede deshacer.`);

            if (confirmDelete) {
                // Eliminar video del array
                videos = videos.filter(v => v.id !== videoId);

                // Guardar en localStorage
                localStorage.setItem('germayori_videos', JSON.stringify(videos));

                // Actualizar la vista
                displayVideos(currentCategory);

                alert('✅ Video eliminado correctamente');
                console.log('🗑️ Video eliminado:', video.title);
            }
        }

        // Función para eliminar TODOS los videos
        function deleteAllVideos() {
            if (videos.length === 0) {
                alert('No hay videos para eliminar');
                return;
            }

            const confirmDeleteAll = confirm(`¿Estás seguro de que quieres eliminar TODOS los videos (${videos.length} videos)?\n\n⚠️ ESTA ACCIÓN NO SE PUEDE DESHACER ⚠️`);

            if (confirmDeleteAll) {
                const secondConfirm = confirm('🚨 ÚLTIMA CONFIRMACIÓN 🚨\n\n¿Realmente quieres BORRAR TODOS LOS VIDEOS?\n\nEscribe "CONFIRMAR" en el siguiente prompt para continuar.');

                if (secondConfirm) {
                    const finalConfirm = prompt('Escribe "CONFIRMAR" para borrar todos los videos:');

                    if (finalConfirm === 'CONFIRMAR') {
                        // Borrar todos los videos
                        videos = [];
                        localStorage.setItem('germayori_videos', JSON.stringify(videos));

                        // Actualizar todas las categorías
                        displayVideos(currentCategory);

                        alert('✅ Todos los videos han sido eliminados correctamente');
                        console.log('🗑️ Todos los videos eliminados');
                    } else {
                        alert('❌ Operación cancelada - No se escribió "CONFIRMAR"');
                    }
                } else {
                    alert('❌ Operación cancelada');
                }
            }
        }

        function addYouTubeVideo() {
            const url = prompt('Ingresa la URL de YouTube:');
            if (url) {
                const newVideo = {
                    id: 'yt_' + Date.now(),
                    title: 'Video de YouTube',
                    description: 'Video educativo de YouTube',
                    category: currentCategory,
                    url: url
                };
                videos.push(newVideo);
                localStorage.setItem('germayori_videos', JSON.stringify(videos));
                displayVideos(currentCategory);
                alert('✅ Video de YouTube agregado correctamente');
            }
        }

        // Subir archivo
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const newVideo = {
                        id: 'file_' + Date.now(),
                        title: file.name.replace(/\.[^/.]+$/, ""),
                        description: 'Video subido por jhon0608',
                        category: currentCategory,
                        data: e.target.result
                    };
                    videos.push(newVideo);
                    localStorage.setItem('germayori_videos', JSON.stringify(videos));
                    displayVideos(currentCategory);
                    alert('✅ Video subido correctamente');
                };
                reader.readAsDataURL(file);
            }
        });

        // Cerrar con ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeVideo();
            }
        });

        // ===== FUNCIONES PARA ZOOM EN VIVO =====
        let isRecording = false;
        let recordedChunks = [];
        let mediaRecorder = null;
        let zoomStream = null;

        // Función para iniciar transmisión Zoom
        function startZoomLive() {
            const zoomContainer = document.getElementById('zoomContainer');
            const status = document.getElementById('liveStatus');

            // Simular conexión a Zoom
            status.innerHTML = '🔴 CONECTANDO A ZOOM...';
            status.style.color = '#ff4444';

            setTimeout(() => {
                zoomContainer.innerHTML = `
                    <div style="width: 100%; height: 100%; background: #000; border-radius: 10px; position: relative; overflow: hidden;">
                        <video id="zoomVideo" style="width: 100%; height: 100%; object-fit: cover;" autoplay muted>
                            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                        </video>
                        <div style="position: absolute; top: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px 15px; border-radius: 20px; font-weight: bold;">
                            🔴 EN VIVO - jhon0608 GERMAYORI
                        </div>
                        <div style="position: absolute; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 8px 12px; border-radius: 15px;">
                            👥 Participantes: 1
                        </div>
                    </div>
                `;

                status.innerHTML = '🔴 TRANSMISIÓN EN VIVO - ZOOM CONECTADO';
                status.style.color = '#00aa00';

                // Obtener el stream del video para grabación
                const video = document.getElementById('zoomVideo');
                if (video) {
                    video.addEventListener('loadedmetadata', () => {
                        // Preparar para grabación
                        console.log('📹 Zoom video listo para grabar');
                    });
                }
            }, 2000);
        }

        // Función para conectar URL de Zoom REAL
        function connectZoom() {
            const zoomUrl = document.getElementById('zoomUrl').value;
            const status = document.getElementById('liveStatus');
            const zoomContainer = document.getElementById('zoomContainer');

            if (!zoomUrl) {
                alert('Por favor ingresa una URL de Zoom');
                return;
            }

            status.innerHTML = '🔗 CONECTANDO A ZOOM...';
            status.style.color = '#ff6600';

            try {
                // Extraer Meeting ID de la URL
                let meetingId = '';
                let password = '';

                // Diferentes formatos de URL de Zoom
                if (zoomUrl.includes('zoom.us/j/')) {
                    meetingId = zoomUrl.split('/j/')[1].split('?')[0];
                } else if (zoomUrl.includes('zoom.us/meeting/')) {
                    meetingId = zoomUrl.split('meeting/')[1].split('/')[0];
                } else if (/^\d+$/.test(zoomUrl)) {
                    meetingId = zoomUrl; // Solo el ID numérico
                }

                // Extraer password si existe
                if (zoomUrl.includes('pwd=')) {
                    password = zoomUrl.split('pwd=')[1].split('&')[0];
                }

                if (!meetingId) {
                    throw new Error('URL de Zoom inválida');
                }

                // Crear iframe de Zoom
                zoomContainer.innerHTML = `
                    <div style="width: 100%; height: 100%; position: relative;">
                        <iframe
                            src="https://zoom.us/wc/join/${meetingId}${password ? '?pwd=' + password : ''}"
                            style="width: 100%; height: 100%; border: none; border-radius: 10px;"
                            allow="camera; microphone; fullscreen; speaker; display-capture"
                            allowfullscreen>
                        </iframe>
                        <div style="position: absolute; top: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px 15px; border-radius: 20px; font-weight: bold;">
                            🔴 ZOOM EN VIVO - Meeting ID: ${meetingId}
                        </div>
                        <div style="position: absolute; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 8px 12px; border-radius: 15px;">
                            🌐 Conectado via Web
                        </div>
                    </div>
                `;

                status.innerHTML = '✅ CONECTADO A ZOOM - Meeting ID: ' + meetingId;
                status.style.color = '#00aa00';

                console.log('✅ Zoom conectado:', meetingId);

            } catch (error) {
                console.error('❌ Error conectando Zoom:', error);
                status.innerHTML = '❌ ERROR DE CONEXIÓN - Verifica la URL';
                status.style.color = '#ff4444';

                // Mostrar ayuda
                zoomContainer.innerHTML = `
                    <div style="width: 100%; height: 100%; background: #1a1a1a; border-radius: 10px; display: flex; align-items: center; justify-content: center; border: 2px solid #ff4444;">
                        <div style="text-align: center; color: white; padding: 20px;">
                            <h3 style="color: #ff4444; margin-bottom: 15px;">❌ Error de Conexión</h3>
                            <p style="margin-bottom: 10px;">Formatos de URL válidos:</p>
                            <div style="background: rgba(0,0,0,0.5); padding: 15px; border-radius: 8px; text-align: left; font-family: monospace; font-size: 12px;">
                                • https://zoom.us/j/1234567890<br>
                                • https://zoom.us/j/1234567890?pwd=abc123<br>
                                • Solo el ID: 1234567890
                            </div>
                            <button onclick="resetZoomConnection()" style="background: #0066cc; color: white; border: none; padding: 10px 20px; border-radius: 15px; cursor: pointer; margin-top: 15px;">
                                🔄 Intentar de Nuevo
                            </button>
                        </div>
                    </div>
                `;

                alert('❌ Error: ' + error.message + '\n\nFormatos válidos:\n• https://zoom.us/j/1234567890\n• Solo el ID: 1234567890');
            }
        }

        // Función para iniciar grabación
        function startRecording() {
            if (isRecording) {
                alert('Ya se está grabando');
                return;
            }

            const status = document.getElementById('liveStatus');
            isRecording = true;
            recordedChunks = [];

            // Simular inicio de grabación
            status.innerHTML = '🔴 TRANSMISIÓN EN VIVO + 🎥 GRABANDO';
            status.style.color = '#ff4444';

            alert('🔴 Grabación iniciada');
            console.log('🎥 Iniciando grabación de Zoom');
        }

        // Función para parar grabación
        function stopRecording() {
            if (!isRecording) {
                alert('No hay grabación activa');
                return;
            }

            isRecording = false;
            const status = document.getElementById('liveStatus');
            status.innerHTML = '🔴 TRANSMISIÓN EN VIVO - Grabación detenida';
            status.style.color = '#00aa00';

            alert('⏹️ Grabación detenida');
            console.log('⏹️ Grabación de Zoom detenida');
        }

        // Función para guardar grabación como video
        function saveRecording() {
            if (isRecording) {
                alert('Detén la grabación primero');
                return;
            }

            const title = prompt('Nombre del video grabado:') || 'Sesión EN VIVO ' + new Date().toLocaleDateString();

            // AUTOMÁTICAMENTE guardar en "Videos del Día EN VIVO"
            const newVideo = {
                id: 'live_' + Date.now(),
                title: title,
                description: 'Video grabado EN VIVO desde Zoom - ' + new Date().toLocaleString(),
                category: 'videos-del-dia', // AUTOMÁTICAMENTE en Videos del Día
                duration: '45:30',
                date: new Date().toISOString().split('T')[0],
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                isLiveVideo: true,
                isZoomRecording: true,
                recordedDate: new Date().toLocaleString(),
                liveDate: new Date().toISOString().split('T')[0]
            };

            // Guardar en videos normales
            videos.push(newVideo);
            localStorage.setItem('germayori_videos', JSON.stringify(videos));

            // También guardar en videos de Zoom
            let zoomVideos = JSON.parse(localStorage.getItem('germayori_zoom_videos') || '[]');
            zoomVideos.push(newVideo);
            localStorage.setItem('germayori_zoom_videos', JSON.stringify(zoomVideos));

            // Actualizar vista
            loadZoomVideos();

            alert('✅ Video guardado automáticamente en "Videos del Día EN VIVO"');
            console.log('💾 Video EN VIVO guardado:', title);

            // Mostrar notificación especial
            showLiveVideoNotification(title);
        }

        // Función para cargar videos de Zoom guardados
        function loadZoomVideos() {
            const zoomVideos = JSON.parse(localStorage.getItem('germayori_zoom_videos') || '[]');
            const grid = document.getElementById('zoomVideosGrid');

            if (zoomVideos.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #ccc; padding: 30px;">No hay videos de Zoom guardados</div>';
                return;
            }

            grid.innerHTML = zoomVideos.map(video => `
                <div style="background: rgba(0, 102, 204, 0.1); border: 2px solid #0066cc; border-radius: 10px; padding: 15px;">
                    <div style="width: 100%; height: 120px; background: #333; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 10px; cursor: pointer;" onclick="playVideo('${video.id}')">
                        <div style="font-size: 2rem; color: #0066cc;">📹</div>
                    </div>
                    <div style="color: #0066cc; font-weight: bold; margin-bottom: 5px;">${video.title}</div>
                    <div style="color: #ccc; font-size: 0.9rem; margin-bottom: 8px;">Grabado: ${video.recordedDate}</div>
                    <div style="display: flex; gap: 8px; justify-content: center;">
                        <button onclick="playVideo('${video.id}')" style="background: #0066cc; color: white; border: none; padding: 6px 12px; border-radius: 12px; cursor: pointer; font-size: 0.9rem;">▶️ Ver</button>
                        <button onclick="deleteZoomVideo('${video.id}')" style="background: #ff4444; color: white; border: none; padding: 6px 12px; border-radius: 12px; cursor: pointer; font-size: 0.9rem;">🗑️</button>
                    </div>
                </div>
            `).join('');
        }

        // Función para eliminar video de Zoom
        function deleteZoomVideo(videoId) {
            let zoomVideos = JSON.parse(localStorage.getItem('germayori_zoom_videos') || '[]');
            const video = zoomVideos.find(v => v.id === videoId);

            if (video && confirm(`¿Eliminar "${video.title}"?`)) {
                // Eliminar de videos de Zoom
                zoomVideos = zoomVideos.filter(v => v.id !== videoId);
                localStorage.setItem('germayori_zoom_videos', JSON.stringify(zoomVideos));

                // Eliminar también de videos normales
                videos = videos.filter(v => v.id !== videoId);
                localStorage.setItem('germayori_videos', JSON.stringify(videos));

                loadZoomVideos();
                alert('✅ Video eliminado');
            }
        }

        // ===== FUNCIONES PARA VIDEOS DEL DÍA EN VIVO =====

        // Función para actualizar estadísticas de videos del día
        function updateLiveVideoStats() {
            const today = new Date().toISOString().split('T')[0];
            const liveVideos = videos.filter(v => v.category === 'videos-del-dia' && v.liveDate === today);

            document.getElementById('liveVideoCount').textContent = liveVideos.length;
            document.getElementById('todayDate').textContent = new Date().toLocaleDateString();

            // Calcular duración total (simulada)
            const totalMinutes = liveVideos.length * 45; // Asumiendo 45 min por video
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            document.getElementById('totalDuration').textContent = `${hours}:${minutes.toString().padStart(2, '0')}`;
        }

        // Función para subir video EN VIVO
        document.getElementById('liveVideoInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const newVideo = {
                        id: 'live_' + Date.now(),
                        title: file.name.replace(/\.[^/.]+$/, "") + ' - EN VIVO',
                        description: 'Video EN VIVO subido el ' + new Date().toLocaleString(),
                        category: 'videos-del-dia',
                        data: e.target.result,
                        isLiveVideo: true,
                        liveDate: new Date().toISOString().split('T')[0],
                        uploadTime: new Date().toLocaleString()
                    };
                    videos.push(newVideo);
                    localStorage.setItem('germayori_videos', JSON.stringify(videos));
                    displayVideos('videos-del-dia');
                    updateLiveVideoStats();
                    showLiveVideoNotification(newVideo.title);
                    alert('✅ Video EN VIVO subido correctamente');
                };
                reader.readAsDataURL(file);
            }
        });

        // Función para agregar grabación de Zoom
        function addZoomRecording() {
            const title = prompt('Nombre de la grabación de Zoom:') || 'Grabación Zoom ' + new Date().toLocaleDateString();
            const url = prompt('URL de la grabación (opcional):') || '';

            const newVideo = {
                id: 'zoom_live_' + Date.now(),
                title: title,
                description: 'Grabación de Zoom agregada el ' + new Date().toLocaleString(),
                category: 'videos-del-dia',
                url: url || 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                isLiveVideo: true,
                isZoomRecording: true,
                liveDate: new Date().toISOString().split('T')[0],
                recordedDate: new Date().toLocaleString()
            };

            videos.push(newVideo);
            localStorage.setItem('germayori_videos', JSON.stringify(videos));
            displayVideos('videos-del-dia');
            updateLiveVideoStats();
            showLiveVideoNotification(title);
            alert('✅ Grabación de Zoom agregada a Videos del Día');
        }

        // Función para marcar video existente como EN VIVO
        function markAsLiveVideo() {
            const videoTitle = prompt('Nombre del video a marcar como EN VIVO:');
            if (!videoTitle) return;

            const video = videos.find(v => v.title.toLowerCase().includes(videoTitle.toLowerCase()));
            if (video) {
                video.category = 'videos-del-dia';
                video.isLiveVideo = true;
                video.liveDate = new Date().toISOString().split('T')[0];
                video.markedAsLive = new Date().toLocaleString();

                localStorage.setItem('germayori_videos', JSON.stringify(videos));
                displayVideos('videos-del-dia');
                updateLiveVideoStats();
                alert(`✅ "${video.title}" marcado como Video del Día EN VIVO`);
            } else {
                alert('❌ Video no encontrado');
            }
        }

        // Función para eliminar todos los videos del día
        function deleteAllLiveVideos() {
            const liveVideos = videos.filter(v => v.category === 'videos-del-dia');

            if (liveVideos.length === 0) {
                alert('No hay videos del día para eliminar');
                return;
            }

            const confirm1 = confirm(`¿Eliminar TODOS los Videos del Día EN VIVO (${liveVideos.length} videos)?`);
            if (confirm1) {
                const confirm2 = prompt('Escribe "ELIMINAR" para confirmar:');
                if (confirm2 === 'ELIMINAR') {
                    videos = videos.filter(v => v.category !== 'videos-del-dia');
                    localStorage.setItem('germayori_videos', JSON.stringify(videos));
                    displayVideos('videos-del-dia');
                    updateLiveVideoStats();
                    alert('✅ Todos los Videos del Día eliminados');
                } else {
                    alert('❌ Operación cancelada');
                }
            }
        }

        // Función para mostrar notificación de video EN VIVO
        function showLiveVideoNotification(title) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff6600;
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                font-weight: bold;
                z-index: 10000;
                box-shadow: 0 4px 15px rgba(255, 102, 0, 0.3);
            `;
            notification.innerHTML = `🔴 NUEVO VIDEO EN VIVO<br>"${title}"`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Función para resetear conexión de Zoom
        function resetZoomConnection() {
            const zoomContainer = document.getElementById('zoomContainer');
            const status = document.getElementById('liveStatus');

            zoomContainer.innerHTML = `
                <div style="text-align: center; color: #0066cc;">
                    <h3>📹 Transmisión Zoom EN VIVO</h3>
                    <p>Conecta tu reunión de Zoom aquí</p>
                    <button onclick="startZoomLive()" style="background: #0066cc; color: white; border: none; padding: 15px 30px; border-radius: 25px; font-size: 16px; font-weight: bold; cursor: pointer; margin: 10px;">🔴 INICIAR TRANSMISIÓN</button>
                </div>
            `;

            status.innerHTML = '⚪ Desconectado - Listo para transmitir';
            status.style.color = '#0066cc';

            document.getElementById('zoomUrl').value = '';
        }

        // Inicializar fecha de hoy al cargar
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('todayDate').textContent = new Date().toLocaleDateString();
        });
    </script>
</body>
</html>
