<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canal Videos Avanzados - GERMAYORI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #7f1d1d, #991b1b, #dc2626);
            color: #fff;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #f87171;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .categories {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .category-btn {
            background: rgba(248, 113, 113, 0.2);
            color: #f87171;
            border: 2px solid #f87171;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .category-btn.active {
            background: #f87171;
            color: #000;
        }

        .videos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .video-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(248, 113, 113, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .video-card:hover {
            border-color: #f87171;
            transform: translateY(-5px);
        }

        .video-thumbnail {
            width: 100%;
            height: 180px;
            background: #7f1d1d;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .play-icon {
            font-size: 3rem;
            color: #f87171;
        }

        .video-title {
            color: #f87171;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border: 2px dashed #f87171;
        }

        .upload-btn {
            background: #f87171;
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
        }

        /* Modal SIMPLE que SÍ FUNCIONA */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1a1a1a;
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #f87171;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff4444;
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
        }

        .video-player {
            width: 800px;
            height: 450px;
            background: #000;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .subtitles-area {
            background: rgba(0, 0, 0, 0.9);
            color: #f87171;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            border: 2px solid #f87171;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="text-align: left; margin-bottom: 20px;">
                <a href="dashboard-simple.html" style="background: #0066cc; color: white; padding: 12px 20px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block; transition: all 0.3s ease;">
                    ← Volver al Dashboard
                </a>
            </div>
            <h1> Educador jothan Deen</h1>
            <h1>🔴 Canal Videos Avanzados GERMAYORI</h1>
            <p>Contenido educativo avanzado de trading institucional</p>
        </div>

        <div class="categories">
            <button class="category-btn active" onclick="showCategory('basico')">📖 Básico</button>
            <button class="category-btn" onclick="showCategory('intermedio')">📊 Intermedio</button>
            <button class="category-btn" onclick="showCategory('avanzado')">🚀 Avanzado</button>
            <button class="category-btn" onclick="showCategory('videos-del-dia')" style="background: #ff6600; border-color: #ff6600;">🔴 Videos del Día EN VIVO</button>
            <button class="category-btn" onclick="showCategory('zoom-live')" style="background: #0066cc; border-color: #0066cc;">📹 Zoom EN VIVO</button>
        </div>

        <div class="upload-section" id="normalUploadSection">
            <h3>📤 Gestión de Videos Avanzados</h3>
            <input type="file" id="fileInput" accept="video/*" style="display: none;">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">📹 Seleccionar Video</button>
            <button class="upload-btn" onclick="addYouTubeVideo()">🔗 Agregar YouTube</button>
            <button class="upload-btn" onclick="deleteAllVideos()" style="background: #ff4444; color: white;">🗑️ Borrar Todos</button>
        </div>

        <div class="videos-grid" id="videosGrid"></div>
    </div>

    <!-- Modal SIMPLE -->
    <div class="modal" id="videoModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeVideo()">&times;</button>
            <h3 id="modalTitle" style="color: #f87171; text-align: center; margin-bottom: 20px;"></h3>
            <video id="videoPlayer" class="video-player" controls autoplay>
                Tu navegador no soporta video HTML5.
            </video>
            <div class="subtitles-area">
                <div id="subtitleText">Bienvenidos traders avanzados, soy jhon0608 de GERMAYORI</div>
            </div>
        </div>
    </div>

    <script>
        let videos = [];
        let currentCategory = 'avanzado';

        // Inicializar
        document.addEventListener('DOMContentLoaded', function() {
            loadVideos();
            showCategory('avanzado');
        });

        function loadVideos() {
            const saved = localStorage.getItem('germayori_videos_avanzados');
            if (saved) {
                videos = JSON.parse(saved);
            } else {
                videos = [];
                localStorage.setItem('germayori_videos_avanzados', JSON.stringify(videos));
            }
        }

        function showCategory(category) {
            currentCategory = category;
            document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            displayVideos(category);
        }

        function displayVideos(category) {
            const grid = document.getElementById('videosGrid');
            const categoryVideos = videos.filter(v => v.category === category);

            if (categoryVideos.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #ccc; padding: 50px;">No hay videos avanzados en esta categoría</div>';
                return;
            }

            grid.innerHTML = categoryVideos.map(video => `
                <div class="video-card">
                    <div class="video-thumbnail" onclick="playVideo('${video.id}')">
                        <div class="play-icon">▶️</div>
                    </div>
                    <div class="video-title">${video.title}</div>
                    <div style="color: #ccc; margin-bottom: 10px;">${video.description}</div>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="playVideo('${video.id}')" style="background: #f87171; color: #000; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">▶️ Reproducir</button>
                        <button onclick="deleteVideo('${video.id}')" style="background: #ff4444; color: white; border: none; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-weight: bold;">🗑️ Eliminar</button>
                    </div>
                </div>
            `).join('');
        }

        function playVideo(videoId) {
            const video = videos.find(v => v.id === videoId);
            if (!video) return;

            document.getElementById('modalTitle').textContent = video.title;
            const player = document.getElementById('videoPlayer');
            const videoSrc = video.url || video.data || 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
            player.src = videoSrc;
            document.getElementById('videoModal').style.display = 'block';
            startSubtitles();
        }

        function startSubtitles() {
            const subtitles = [
                'Bienvenidos traders avanzados, soy jhon0608 de GERMAYORI',
                'En este canal dominaremos estrategias institucionales',
                'Análisis de liquidez y manipulación del mercado',
                'Order Blocks avanzados y confluencias complejas',
                'Gestión profesional de carteras institucionales',
                'Gracias por ver contenido avanzado GERMAYORI'
            ];

            let index = 0;
            document.getElementById('subtitleText').textContent = subtitles[0];

            setInterval(() => {
                index = (index + 1) % subtitles.length;
                document.getElementById('subtitleText').textContent = subtitles[index];
            }, 5000);
        }

        function closeVideo() {
            document.getElementById('videoModal').style.display = 'none';
            const player = document.getElementById('videoPlayer');
            player.pause();
            player.src = '';
        }

        function deleteVideo(videoId) {
            const video = videos.find(v => v.id === videoId);
            if (!video) return;

            const confirmDelete = confirm(`¿Estás seguro de que quieres eliminar el video "${video.title}"?`);

            if (confirmDelete) {
                videos = videos.filter(v => v.id !== videoId);
                localStorage.setItem('germayori_videos_avanzados', JSON.stringify(videos));
                displayVideos(currentCategory);
                alert('✅ Video avanzado eliminado correctamente');
            }
        }

        function deleteAllVideos() {
            if (videos.length === 0) {
                alert('No hay videos avanzados para eliminar');
                return;
            }

            const confirmDeleteAll = confirm(`¿Estás seguro de que quieres eliminar TODOS los videos avanzados (${videos.length} videos)?`);

            if (confirmDeleteAll) {
                videos = [];
                localStorage.setItem('germayori_videos_avanzados', JSON.stringify(videos));
                displayVideos(currentCategory);
                alert('✅ Todos los videos avanzados han sido eliminados');
            }
        }

        function addYouTubeVideo() {
            const url = prompt('Ingresa la URL de YouTube para video avanzado:');
            if (url) {
                const newVideo = {
                    id: 'yt_avanzado_' + Date.now(),
                    title: 'Video Avanzado de YouTube',
                    description: 'Video educativo avanzado de YouTube',
                    category: currentCategory,
                    url: url
                };
                videos.push(newVideo);
                localStorage.setItem('germayori_videos_avanzados', JSON.stringify(videos));
                displayVideos(currentCategory);
                alert('✅ Video avanzado de YouTube agregado correctamente');
            }
        }

        // Subir archivo
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const newVideo = {
                        id: 'file_avanzado_' + Date.now(),
                        title: file.name.replace(/\.[^/.]+$/, ""),
                        description: 'Video avanzado subido por jhon0608',
                        category: currentCategory,
                        data: e.target.result
                    };
                    videos.push(newVideo);
                    localStorage.setItem('germayori_videos_avanzados', JSON.stringify(videos));
                    displayVideos(currentCategory);
                    alert('✅ Video avanzado subido correctamente');
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
