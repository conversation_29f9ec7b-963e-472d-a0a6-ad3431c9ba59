const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    username: {
        type: String,
        trim: true
    },
    email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true
    },
    password: {
        type: String,
        required: true
    },
    type: {
        type: String,
        enum: ['user', 'admin', 'premium', 'basic'],
        default: 'user'
    },
    avatar: {
        type: String,
        default: 'https://via.placeholder.com/40'
    },
    plan: {
        type: String,
        enum: ['basico', 'intermedio', 'avanzado', 'completo'],
        default: 'basico'
    },
    planPrice: {
        type: Number,
        default: 45
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastLogin: {
        type: Date,
        default: Date.now
    },
    expiryDate: {
        type: Date,
        required: true,
        default: function() {
            const date = new Date();
            date.setDate(date.getDate() + 30);
            return date;
        }
    }
}, {
    timestamps: true // Crea automáticamente createdAt y updatedAt
});

// Índices para optimizar búsquedas
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ type: 1 });
userSchema.index({ plan: 1 });

module.exports = mongoose.model('User', userSchema);
