// Variables globales
let currentTab = 'users';
let usersData = [];
let signalsData = [];
let newsData = [];
let alertsData = [];

// Configuración de la API
const API_BASE = 'http://localhost:3000/api';

// Inicialización
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 GERMAYORI Admin Panel iniciado');
    checkConnection();
    loadAllData();

    // Actualizar datos cada 30 segundos
    setInterval(refreshCurrentTab, 30000);
});

// ==================== GESTIÓN DE TABS ====================
function showTab(tabName) {
    // Ocultar todos los tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remover clase active de todos los botones
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // Mostrar tab seleccionado
    document.getElementById(tabName).classList.add('active');

    // Activar botón correspondiente
    event.target.classList.add('active');

    currentTab = tabName;

    // Cargar datos del tab si es necesario
    switch(tabName) {
        case 'users':
            if (usersData.length === 0) loadUsers();
            break;
        case 'signals':
            if (signalsData.length === 0) loadSignals();
            break;
        case 'news':
            if (newsData.length === 0) loadNews();
            break;
        case 'alerts':
            if (alertsData.length === 0) loadAlerts();
            break;
        case 'stats':
            loadStats();
            break;
    }
}

// ==================== CONEXIÓN Y ESTADO ====================
async function checkConnection() {
    try {
        const response = await fetch(`${API_BASE}/health`);
        if (response.ok) {
            updateConnectionStatus(true);
        } else {
            updateConnectionStatus(false);
        }
    } catch (error) {
        console.error('Error de conexión:', error);
        updateConnectionStatus(false);
    }
}

function updateConnectionStatus(isOnline) {
    const statusElement = document.getElementById('connectionStatus');
    if (isOnline) {
        statusElement.className = 'connection-status connection-online';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> Conectado';
    } else {
        statusElement.className = 'connection-status connection-offline';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> Desconectado';
    }
}

// ==================== CARGA DE DATOS ====================
async function loadAllData() {
    console.log('📊 Cargando todos los datos...');
    await Promise.all([
        loadUsers(),
        loadSignals(),
        loadNews(),
        loadAlerts()
    ]);
    updateStats();
}

async function loadUsers() {
    console.log('👥 Cargando usuarios...');
    showLoading('usersLoading', 'usersTable');

    try {
        // Cargar usuarios desde MongoDB
        const response = await fetch(`${API_BASE}/admin/users`);

        if (response.ok) {
            const data = await response.json();
            usersData = data.users || data.data || data || [];
            console.log('Usuarios cargados:', usersData);
            displayUsers(usersData);
            updateUserStats();
        } else {
            throw new Error('Error cargando usuarios');
        }
    } catch (error) {
        console.error('Error cargando usuarios:', error);
        showError('usersLoading', 'Error cargando usuarios');
    }
}

async function loadSignals() {
    console.log('📈 Cargando señales...');
    showLoading('signalsLoading', 'signalsTable');

    try {
        // Intentar cargar desde AWS primero
        let response = await fetch(`${API_BASE}/aws/signals`);

        if (!response.ok) {
            // Si AWS falla, usar SQLite local
            console.log('AWS no disponible, usando señales locales');
            response = await fetch(`${API_BASE}/signals`);
        }

        if (response.ok) {
            const data = await response.json();
            signalsData = data.data || data || [];
            displaySignals(signalsData);
            updateSignalStats();
        } else {
            throw new Error('Error cargando señales');
        }
    } catch (error) {
        console.error('Error cargando señales:', error);
        showError('signalsLoading', 'Error cargando señales');
    }
}

async function loadNews() {
    console.log('📰 Cargando noticias...');
    showLoading('newsLoading', 'newsTable');

    try {
        const response = await fetch(`${API_BASE}/news`);
        if (response.ok) {
            newsData = await response.json();
            displayNews(newsData);
        } else {
            throw new Error('Error cargando noticias');
        }
    } catch (error) {
        console.error('Error cargando noticias:', error);
        showError('newsLoading', 'Error cargando noticias');
    }
}

async function loadAlerts() {
    console.log('🔔 Cargando alertas...');
    showLoading('alertsLoading', 'alertsTable');

    try {
        const response = await fetch(`${API_BASE}/alerts`);
        if (response.ok) {
            alertsData = await response.json();
            displayAlerts(alertsData);
        } else {
            throw new Error('Error cargando alertas');
        }
    } catch (error) {
        console.error('Error cargando alertas:', error);
        showError('alertsLoading', 'Error cargando alertas');
    }
}

// ==================== DISPLAY DE DATOS ====================
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    tbody.innerHTML = '';

    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.name || user.nombre || 'N/A'}</td>
            <td>${user.email}</td>
            <td><span class="status-badge status-${user.type || 'user'}">${user.type || 'user'}</span></td>
            <td><span class="status-badge status-active">Activo</span></td>
            <td>${formatDate(user.created_at)}</td>
            <td>
                <button class="btn btn-info btn-sm" onclick="viewUser('${user.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteUser('${user.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });

    hideLoading('usersLoading', 'usersTable');
}

function displaySignals(signals) {
    const tbody = document.getElementById('signalsTableBody');
    tbody.innerHTML = '';

    signals.forEach(signal => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${signal.pair}</td>
            <td><span class="status-badge status-${signal.type.toLowerCase()}">${signal.type}</span></td>
            <td>${signal.entry_price}</td>
            <td>${signal.stop_loss}</td>
            <td>${signal.take_profit}</td>
            <td><span class="status-badge status-${signal.status}">${signal.status}</span></td>
            <td>${signal.pips || 0}</td>
            <td>${formatDate(signal.created_at)}</td>
        `;
        tbody.appendChild(row);
    });

    hideLoading('signalsLoading', 'signalsTable');
}

function displayNews(news) {
    const tbody = document.getElementById('newsTableBody');
    tbody.innerHTML = '';

    news.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.title}</td>
            <td>${item.category || 'General'}</td>
            <td><span class="status-badge status-${item.impact}">${item.impact || 'medium'}</span></td>
            <td>${item.currency || 'N/A'}</td>
            <td>${item.source || 'GERMAYORI'}</td>
            <td>${formatDate(item.created_at)}</td>
        `;
        tbody.appendChild(row);
    });

    hideLoading('newsLoading', 'newsTable');
}

function displayAlerts(alerts) {
    const tbody = document.getElementById('alertsTableBody');
    tbody.innerHTML = '';

    alerts.forEach(alert => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${alert.type}</td>
            <td>${alert.symbol || 'N/A'}</td>
            <td>${alert.message}</td>
            <td><span class="status-badge status-${alert.level}">${alert.level}</span></td>
            <td><span class="status-badge status-${alert.status}">${alert.status}</span></td>
            <td>${formatDate(alert.created_at)}</td>
        `;
        tbody.appendChild(row);
    });

    hideLoading('alertsLoading', 'alertsTable');
}

// ==================== ESTADÍSTICAS ====================
function updateUserStats() {
    document.getElementById('totalUsers').textContent = usersData.length;
    document.getElementById('activeUsers').textContent = usersData.filter(u => u.status !== 'inactive').length;

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const newUsers = usersData.filter(u => new Date(u.created_at) > sevenDaysAgo).length;
    document.getElementById('newUsers').textContent = newUsers;
}

function updateSignalStats() {
    document.getElementById('totalSignals').textContent = signalsData.length;
    document.getElementById('activeSignals').textContent = signalsData.filter(s => s.status === 'active').length;
    document.getElementById('profitableSignals').textContent = signalsData.filter(s => s.pips > 0).length;
}

function updateStats() {
    const totalRecords = usersData.length + signalsData.length + newsData.length + alertsData.length;
    document.getElementById('totalRecords').textContent = totalRecords;
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
}

// ==================== UTILIDADES ====================
function showLoading(loadingId, tableId) {
    document.getElementById(loadingId).style.display = 'block';
    document.getElementById(tableId).style.display = 'none';
}

function hideLoading(loadingId, tableId) {
    document.getElementById(loadingId).style.display = 'none';
    document.getElementById(tableId).style.display = 'table';
}

function showError(loadingId, message) {
    const loadingElement = document.getElementById(loadingId);
    loadingElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    loadingElement.style.color = '#dc3545';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES') + ' ' + date.toLocaleTimeString('es-ES');
}

// ==================== ACCIONES ====================
async function refreshUsers() {
    await loadUsers();
}

async function refreshSignals() {
    await loadSignals();
}

async function refreshNews() {
    await loadNews();
}

async function refreshAlerts() {
    await loadAlerts();
}

async function refreshCurrentTab() {
    switch(currentTab) {
        case 'users':
            await refreshUsers();
            break;
        case 'signals':
            await refreshSignals();
            break;
        case 'news':
            await refreshNews();
            break;
        case 'alerts':
            await refreshAlerts();
            break;
        case 'stats':
            await loadStats();
            break;
    }
}

async function refreshAllData() {
    await loadAllData();
}

function loadStats() {
    updateStats();
    document.getElementById('serverStatus').textContent = 'OK';
    document.getElementById('awsStatus').textContent = 'Conectando...';

    // Probar conexión AWS
    testAWSConnection();
}

async function testAWSConnection() {
    try {
        const response = await fetch(`${API_BASE}/aws/test`);
        if (response.ok) {
            document.getElementById('awsStatus').textContent = 'OK';
            document.getElementById('awsStatus').style.color = '#28a745';
        } else {
            document.getElementById('awsStatus').textContent = 'Error';
            document.getElementById('awsStatus').style.color = '#dc3545';
        }
    } catch (error) {
        document.getElementById('awsStatus').textContent = 'Offline';
        document.getElementById('awsStatus').style.color = '#dc3545';
    }
}

async function initializeAWS() {
    try {
        const response = await fetch(`${API_BASE}/aws/init`, { method: 'POST' });
        if (response.ok) {
            alert('✅ AWS inicializado correctamente');
            testAWSConnection();
        } else {
            alert('❌ Error inicializando AWS');
        }
    } catch (error) {
        alert('❌ Error de conexión con AWS');
    }
}

// ==================== FUNCIONES DE PRUEBA ====================
async function addTestUser() {
    const testUser = {
        name: `Usuario Test ${Date.now()}`,
        email: `test${Date.now()}@germayori.com`,
        password: 'test123',
        type: 'user'
    };

    try {
        const response = await fetch(`${API_BASE}/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testUser)
        });

        if (response.ok) {
            alert('✅ Usuario de prueba agregado');
            await refreshUsers();
        } else {
            alert('❌ Error agregando usuario');
        }
    } catch (error) {
        alert('❌ Error de conexión');
    }
}

async function addTestSignal() {
    const testSignal = {
        user_id: 1,
        pair: 'EURUSD',
        type: 'BUY',
        entry_price: 1.0850,
        stop_loss: 1.0800,
        take_profit: 1.0950,
        analysis: 'Señal de prueba generada automáticamente',
        risk_level: 'medium',
        timeframe: 'H1'
    };

    try {
        const response = await fetch(`${API_BASE}/signals`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testSignal)
        });

        if (response.ok) {
            alert('✅ Señal de prueba agregada');
            await refreshSignals();
        } else {
            alert('❌ Error agregando señal');
        }
    } catch (error) {
        alert('❌ Error de conexión');
    }
}

async function addTestNews() {
    const testNews = {
        title: `Noticia de prueba ${new Date().toLocaleString()}`,
        content: 'Esta es una noticia de prueba generada automáticamente',
        category: 'Test',
        impact: 'medium',
        currency: 'USD',
        source: 'GERMAYORI Test'
    };

    try {
        const response = await fetch(`${API_BASE}/news`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testNews)
        });

        if (response.ok) {
            alert('✅ Noticia de prueba agregada');
            await refreshNews();
        } else {
            alert('❌ Error agregando noticia');
        }
    } catch (error) {
        alert('❌ Error de conexión');
    }
}

async function addTestAlert() {
    const testAlert = {
        user_id: 1,
        type: 'price',
        symbol: 'EURUSD',
        message: `Alerta de prueba - ${new Date().toLocaleString()}`,
        level: 'medium'
    };

    try {
        const response = await fetch(`${API_BASE}/alerts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testAlert)
        });

        if (response.ok) {
            alert('✅ Alerta de prueba agregada');
            await refreshAlerts();
        } else {
            alert('❌ Error agregando alerta');
        }
    } catch (error) {
        alert('❌ Error de conexión');
    }
}

// ==================== FILTROS Y BÚSQUEDA ====================
function filterUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const filteredUsers = usersData.filter(user =>
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
    );
    displayUsers(filteredUsers);
}

function exportUsers() {
    const csv = convertToCSV(usersData);
    downloadCSV(csv, 'usuarios_germayori.csv');
}

function convertToCSV(data) {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => row[header] || '').join(','))
    ].join('\n');

    return csvContent;
}

function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', filename);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// Funciones placeholder para acciones de usuario
function viewUser(id) {
    alert(`Ver detalles del usuario: ${id}`);
}

function deleteUser(id) {
    if (confirm('¿Estás seguro de eliminar este usuario?')) {
        alert(`Usuario ${id} eliminado`);
        refreshUsers();
    }
}

// ==================== GESTIÓN DE SEÑALES AVANZADA ====================
// Inicializar formularios
document.addEventListener('DOMContentLoaded', function() {
    // Formulario de señales
    const signalForm = document.getElementById('signalForm');
    if (signalForm) {
        signalForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createSignal();
        });
    }

    // Formulario de videos
    const videoForm = document.getElementById('videoForm');
    if (videoForm) {
        videoForm.addEventListener('submit', function(e) {
            e.preventDefault();
            uploadVideo();
        });
    }
});

function createSignal() {
    const signalData = {
        pair: document.getElementById('signalPair').value,
        type: document.getElementById('signalType').value,
        entryPrice: parseFloat(document.getElementById('signalEntry').value),
        stopLoss: parseFloat(document.getElementById('signalSL').value),
        takeProfit: parseFloat(document.getElementById('signalTP').value),
        timeframe: document.getElementById('signalTimeframe').value,
        analysis: document.getElementById('signalAnalysis').value,
        status: 'ACTIVE',
        createdBy: 'jhon0608'
    };

    fetch(`${API_BASE}/signals`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(signalData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Señal creada exitosamente');
            document.getElementById('signalForm').reset();
            refreshSignals();
        } else {
            alert('❌ Error al crear señal: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Error de conexión al crear señal');
    });
}

function clearAllSignals() {
    if (confirm('¿Estás seguro de eliminar TODAS las señales? Esta acción no se puede deshacer.')) {
        // Limpiar localStorage
        localStorage.removeItem('germayori_signals');
        alert('✅ Todas las señales eliminadas');
        refreshSignals();
    }
}

// ==================== GESTIÓN DE VIDEOS AVANZADA ====================
function uploadVideo() {
    const videoData = {
        title: document.getElementById('videoTitle').value,
        category: document.getElementById('videoCategory').value,
        description: document.getElementById('videoDescription').value,
        url: document.getElementById('videoUrl').value,
        thumbnail: document.getElementById('videoThumbnail').value,
        uploadedBy: 'jhon0608',
        uploadDate: new Date().toISOString()
    };

    // Guardar en localStorage
    let videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
    videoData.id = Date.now().toString();
    videos.push(videoData);
    localStorage.setItem('germayori_videos', JSON.stringify(videos));

    alert('✅ Video subido exitosamente');
    document.getElementById('videoForm').reset();
    refreshVideos();
}

function refreshVideos() {
    const videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
    displayVideos(videos);
    updateVideosStats(videos);
}

function displayVideos(videos) {
    const grid = document.getElementById('videosGrid');
    if (!grid) return;

    grid.innerHTML = '';

    videos.forEach(video => {
        const videoCard = document.createElement('div');
        videoCard.style.cssText = 'background: rgba(255,255,255,0.1); border-radius: 10px; padding: 15px; backdrop-filter: blur(10px);';

        videoCard.innerHTML = `
            <img src="${video.thumbnail || 'https://via.placeholder.com/300x200'}"
                 style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 10px;">
            <h4 style="margin-bottom: 8px; color: #FFD700;">${video.title}</h4>
            <p style="color: #ccc; font-size: 0.9rem; margin-bottom: 10px;">${video.description}</p>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span class="status-badge status-active">${video.category}</span>
                <div>
                    <button class="btn btn-info" onclick="window.open('${video.url}', '_blank')" style="margin-right: 5px;">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteVideo('${video.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        grid.appendChild(videoCard);
    });

    document.getElementById('videosLoading').style.display = 'none';
    document.getElementById('videosGrid').style.display = 'grid';
}

function updateVideosStats(videos) {
    const totalVideosEl = document.getElementById('totalVideos');
    const totalViewsEl = document.getElementById('totalViews');
    const totalDurationEl = document.getElementById('totalDuration');

    if (totalVideosEl) totalVideosEl.textContent = videos.length;
    if (totalViewsEl) totalViewsEl.textContent = videos.reduce((sum, v) => sum + (v.views || 0), 0);
    if (totalDurationEl) totalDurationEl.textContent = videos.reduce((sum, v) => sum + (v.duration || 0), 0);
}

function deleteVideo(videoId) {
    if (confirm('¿Estás seguro de eliminar este video?')) {
        let videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
        videos = videos.filter(v => v.id !== videoId);
        localStorage.setItem('germayori_videos', JSON.stringify(videos));
        alert('✅ Video eliminado');
        refreshVideos();
    }
}

function importYouTubePlaylist() {
    const playlistUrl = prompt('Ingresa la URL de la playlist de YouTube:');
    if (playlistUrl) {
        alert('🔄 Función de importación en desarrollo...');
    }
}
