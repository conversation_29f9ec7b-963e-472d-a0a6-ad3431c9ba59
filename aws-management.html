<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>☁️ GERMAYORI - Gestión AWS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .aws-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .info-card h3 {
            margin-bottom: 15px;
            color: #FFD700;
        }

        .info-item {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }

        .signal-form {
            background: rgba(0, 0, 0, 0.2);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #FFD700;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1rem;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-aws {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        .status-section {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .status-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FFD700;
        }

        .status-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .recent-signals {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .signal-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #FFD700;
        }

        .signal-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .signal-pair {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FFD700;
        }

        .signal-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .signal-buy {
            background: #28a745;
        }

        .signal-sell {
            background: #dc3545;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }

        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>☁️ GERMAYORI - Gestión AWS</h1>
            <p>Panel de control para envío de señales desde AWS a canales</p>
        </div>

        <!-- Información AWS -->
        <div class="aws-info">
            <div class="info-card">
                <h3>🔑 Credenciales AWS</h3>
                <div class="info-item"><strong>Usuario:</strong> germayori08</div>
                <div class="info-item"><strong>Contraseña:</strong> djD47R&1</div>
                <div class="info-item"><strong>Región:</strong> us-east-1</div>
                <button class="btn btn-aws" onclick="openAWSConsole()">🌐 Abrir Consola AWS</button>
            </div>

            <div class="info-card">
                <h3>📊 Estado del Sistema</h3>
                <div class="info-item" id="connectionStatus">🔄 Verificando conexión...</div>
                <div class="info-item" id="databaseStatus">🔄 Verificando base de datos...</div>
                <div class="info-item" id="lastUpdate">🔄 Cargando...</div>
                <button class="btn" onclick="checkStatus()">🔄 Verificar Estado</button>
            </div>
        </div>

        <!-- Estadísticas -->
        <div class="status-section">
            <h3>📈 Estadísticas en Tiempo Real</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-number" id="totalUsers">-</div>
                    <div class="status-label">👥 Usuarios</div>
                </div>
                <div class="status-item">
                    <div class="status-number" id="totalSignals">-</div>
                    <div class="status-label">📈 Señales</div>
                </div>
                <div class="status-item">
                    <div class="status-number" id="activeSignals">-</div>
                    <div class="status-label">🟢 Activas</div>
                </div>
                <div class="status-item">
                    <div class="status-number" id="awsSignals">-</div>
                    <div class="status-label">☁️ Desde AWS</div>
                </div>
            </div>
        </div>

        <!-- Formulario de Señales -->
        <div class="signal-form">
            <h3>📤 Enviar Nueva Señal desde AWS</h3>
            <div id="alertContainer"></div>
            
            <form id="awsSignalForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="pair">Par de Divisas</label>
                        <select id="pair" required>
                            <option value="">Seleccionar par</option>
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="USDCAD">USD/CAD</option>
                            <option value="NZDUSD">NZD/USD</option>
                            <option value="USDCHF">USD/CHF</option>
                            <option value="EURGBP">EUR/GBP</option>
                            <option value="EURJPY">EUR/JPY</option>
                            <option value="GBPJPY">GBP/JPY</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="type">Tipo de Operación</label>
                        <select id="type" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="BUY">BUY (Compra)</option>
                            <option value="SELL">SELL (Venta)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="entryPrice">Precio de Entrada</label>
                        <input type="number" id="entryPrice" step="0.00001" placeholder="1.08500" required>
                    </div>

                    <div class="form-group">
                        <label for="stopLoss">Stop Loss</label>
                        <input type="number" id="stopLoss" step="0.00001" placeholder="1.08000" required>
                    </div>

                    <div class="form-group">
                        <label for="takeProfit">Take Profit</label>
                        <input type="number" id="takeProfit" step="0.00001" placeholder="1.09500" required>
                    </div>

                    <div class="form-group">
                        <label for="timeframe">Timeframe</label>
                        <select id="timeframe" required>
                            <option value="M5">M5 (5 minutos)</option>
                            <option value="M15">M15 (15 minutos)</option>
                            <option value="M30">M30 (30 minutos)</option>
                            <option value="H1">H1 (1 hora)</option>
                            <option value="H4" selected>H4 (4 horas)</option>
                            <option value="D1">D1 (Diario)</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="analysis">Análisis de la Señal</label>
                    <textarea id="analysis" placeholder="Describe el análisis técnico y fundamental que respalda esta señal..." required></textarea>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn">📤 Enviar Señal a Canal</button>
                    <button type="button" class="btn btn-danger" onclick="clearForm()">🗑️ Limpiar</button>
                </div>
            </form>
        </div>

        <!-- Señales Recientes -->
        <div class="recent-signals">
            <h3>📋 Señales Recientes</h3>
            <div id="recentSignalsContainer">
                <div style="text-align: center; padding: 20px; opacity: 0.7;">
                    🔄 Cargando señales recientes...
                </div>
            </div>
        </div>
    </div>

    <script>
        // Cargar datos al inicio
        window.onload = function() {
            checkStatus();
            loadRecentSignals();
        };

        function openAWSConsole() {
            window.open('https://082565699654.signin.aws.amazon.com/console', '_blank');
        }

        async function checkStatus() {
            try {
                const response = await fetch('/api/aws/status');
                const status = await response.json();
                
                if (status.success) {
                    document.getElementById('connectionStatus').innerHTML = '✅ Conexión AWS activa';
                    document.getElementById('databaseStatus').innerHTML = '✅ Base de datos conectada';
                    document.getElementById('lastUpdate').innerHTML = `🕒 Última actualización: ${new Date().toLocaleString('es-ES')}`;
                    
                    document.getElementById('totalUsers').textContent = status.total_users;
                    document.getElementById('totalSignals').textContent = status.total_signals;
                    
                    // Cargar estadísticas adicionales
                    await loadAdditionalStats();
                } else {
                    document.getElementById('connectionStatus').innerHTML = '❌ Error de conexión';
                    document.getElementById('databaseStatus').innerHTML = '❌ Error en base de datos';
                }
            } catch (error) {
                document.getElementById('connectionStatus').innerHTML = '❌ Error de conexión';
                document.getElementById('databaseStatus').innerHTML = '❌ Error en base de datos';
                console.error('Error verificando estado:', error);
            }
        }

        async function loadAdditionalStats() {
            try {
                const response = await fetch('/api/signals');
                const signals = await response.json();
                
                const activeSignals = signals.filter(s => s.status === 'active').length;
                const awsSignals = signals.filter(s => s.source === 'AWS').length;
                
                document.getElementById('activeSignals').textContent = activeSignals;
                document.getElementById('awsSignals').textContent = awsSignals;
            } catch (error) {
                console.error('Error cargando estadísticas:', error);
            }
        }

        async function loadRecentSignals() {
            try {
                const response = await fetch('/api/signals');
                const signals = await response.json();
                
                const recentSignals = signals.slice(0, 5); // Últimas 5 señales
                let html = '';
                
                if (recentSignals.length === 0) {
                    html = '<div style="text-align: center; padding: 20px; opacity: 0.7;">📭 No hay señales recientes</div>';
                } else {
                    recentSignals.forEach(signal => {
                        const typeClass = signal.type === 'BUY' ? 'signal-buy' : 'signal-sell';
                        const date = new Date(signal.created_at).toLocaleString('es-ES');
                        const source = signal.source === 'AWS' ? '☁️ AWS' : '🖥️ Local';
                        
                        html += `
                            <div class="signal-item">
                                <div class="signal-header">
                                    <span class="signal-pair">${signal.pair}</span>
                                    <span class="signal-type ${typeClass}">${signal.type}</span>
                                </div>
                                <div>📍 Entrada: ${signal.entry_price} | 🛑 SL: ${signal.stop_loss} | 🎯 TP: ${signal.take_profit}</div>
                                <div>🕒 ${date} | ${source}</div>
                                <div style="margin-top: 8px; font-size: 0.9rem; opacity: 0.8;">${signal.analysis}</div>
                            </div>
                        `;
                    });
                }
                
                document.getElementById('recentSignalsContainer').innerHTML = html;
            } catch (error) {
                document.getElementById('recentSignalsContainer').innerHTML = '<div style="text-align: center; padding: 20px; color: #dc3545;">❌ Error cargando señales</div>';
                console.error('Error cargando señales:', error);
            }
        }

        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            
            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
            
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function clearForm() {
            document.getElementById('awsSignalForm').reset();
        }

        // Manejar envío del formulario
        document.getElementById('awsSignalForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                user_id: 1, // Admin user
                pair: document.getElementById('pair').value,
                type: document.getElementById('type').value,
                entry_price: parseFloat(document.getElementById('entryPrice').value),
                stop_loss: parseFloat(document.getElementById('stopLoss').value),
                take_profit: parseFloat(document.getElementById('takeProfit').value),
                analysis: document.getElementById('analysis').value,
                timeframe: document.getElementById('timeframe').value,
                risk_level: 'medium'
            };
            
            try {
                const response = await fetch('/api/aws/signals', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`✅ Señal ${formData.pair} ${formData.type} enviada exitosamente al canal!`, 'success');
                    clearForm();
                    checkStatus();
                    loadRecentSignals();
                } else {
                    showAlert(`❌ Error enviando señal: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`❌ Error de conexión: ${error.message}`, 'error');
            }
        });

        // Auto-refresh cada 30 segundos
        setInterval(() => {
            checkStatus();
            loadRecentSignals();
        }, 30000);
    </script>
</body>
</html>
