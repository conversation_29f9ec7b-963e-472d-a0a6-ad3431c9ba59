import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { MessageCircle, Send, Bot, User, Crown, AlertTriangle } from 'lucide-react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../context/NotificationContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';
import PaywallModal from '../components/Paywall/PaywallModal';

const ChatEducativo = () => {
  const { user } = useAuth();
  const { success, error } = useNotifications();
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: '¡Hola! Soy tu asistente educativo de trading. Puedo ayudarte a entender conceptos, estrategias y análisis técnico. ¿En qué te gustaría que te ayude hoy?',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPaywall, setShowPaywall] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Verificar si el usuario tiene acceso
  useEffect(() => {
    if (!user?.isPaid) {
      setShowPaywall(true);
    }
  }, [user]);

  const predefinedQuestions = [
    "¿Qué es el análisis técnico?",
    "¿Cómo identificar soportes y resistencias?",
    "¿Qué son los patrones de velas japonesas?",
    "¿Cómo gestionar el riesgo en trading?",
    "¿Qué es el RSI y cómo usarlo?",
    "¿Cuáles son las mejores horas para hacer trading?"
  ];

  const tradingResources = [
    {
      name: "Calculadora de Profit",
      url: "https://www.myfxbook.com/forex-calculators/profit-calculator",
      description: "Calcula pips y risk-reward ratios"
    },
    {
      name: "Calendario Económico",
      url: "https://www.myfxbook.com/forex-economic-calendar",
      description: "Noticias y eventos del mercado"
    }
  ];

  // Función para enviar mensaje al backend con OpenAI
  const sendMessageToAPI = async (message) => {
    try {
      const token = localStorage.getItem('germayori_token');

      if (!token) {
        console.log('No hay token, usando respuesta local');
        return getFallbackResponse(message);
      }

      console.log('Enviando mensaje al backend:', message);

      const response = await axios.post(
        'http://localhost:5000/api/chat/message',
        { message },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000 // 30 segundos
        }
      );

      console.log('Respuesta del backend:', response.data);
      return response.data.response;
    } catch (error) {
      console.error('Error al enviar mensaje:', error);

      // Si hay error de autenticación o red, usar fallback
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('Error de autenticación, usando respuesta local');
      } else {
        console.log('Error de red o servidor, usando respuesta local');
      }

      return getFallbackResponse(message);
    }
  };

  // Respuestas de fallback si falla OpenAI
  const getFallbackResponse = (question) => {
    const responses = {
      "análisis técnico": "El análisis técnico es el estudio de los movimientos de precios históricos para predecir futuros movimientos. Se basa en gráficos, patrones e indicadores técnicos. Los principios fundamentales incluyen: 1) Los precios descuentan todo, 2) Los precios se mueven en tendencias, 3) La historia se repite.",

      "soportes y resistencias": "Los soportes son niveles de precio donde la demanda es lo suficientemente fuerte para detener una caída. Las resistencias son niveles donde la oferta es fuerte para detener una subida. Para identificarlos: busca niveles donde el precio ha rebotado múltiples veces, usa máximos y mínimos anteriores, y observa niveles psicológicos redondos.",

      "velas japonesas": "Las velas japonesas muestran 4 precios: apertura, cierre, máximo y mínimo. Patrones importantes incluyen: Doji (indecisión), Martillo (posible reversión alcista), Estrella fugaz (posible reversión bajista), Envolvente alcista/bajista. Cada patrón debe confirmarse con el contexto del mercado.",

      "gestión de riesgo": "La gestión de riesgo es fundamental en trading. Reglas clave: 1) Nunca arriesgues más del 1-2% de tu capital por operación, 2) Usa stop loss siempre, 3) Mantén una relación riesgo/beneficio de al menos 1:2, 4) Diversifica tus operaciones, 5) No hagas trading emocional.",

      "RSI": "El RSI (Relative Strength Index) mide la velocidad y magnitud de los cambios de precio. Va de 0 a 100. Valores sobre 70 indican sobrecompra (posible venta), bajo 30 indican sobreventa (posible compra). También busca divergencias entre precio y RSI para señales de reversión.",

      "default": "Esa es una excelente pregunta sobre trading. Te recomiendo estudiar los conceptos básicos primero: análisis técnico, gestión de riesgo, y psicología del trading. Recuerda que el trading requiere práctica constante y educación continua. ¿Hay algún concepto específico que te gustaría explorar más?"
    };

    const lowerQuestion = question.toLowerCase();
    let response = responses.default;

    for (const [key, value] of Object.entries(responses)) {
      if (lowerQuestion.includes(key)) {
        response = value;
        break;
      }
    }

    return response + "\n\n*Nota: Respuesta generada localmente (OpenAI no disponible en este momento)*";
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    // Verificar acceso premium
    if (!user?.isPaid) {
      setShowPaywall(true);
      return;
    }

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Enviar mensaje al backend con OpenAI
      const aiResponse = await sendMessageToAPI(inputMessage);

      const botMessage = {
        id: Date.now() + 1,
        type: 'bot',
        content: aiResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
      success('Respuesta recibida del asistente educativo');
    } catch (error) {
      console.error('Error en chat:', error);
      error('Error al procesar tu pregunta. Inténtalo de nuevo.');
      const errorMessage = {
        id: Date.now() + 1,
        type: 'bot',
        content: 'Lo siento, hubo un error al procesar tu pregunta. Por favor, inténtalo de nuevo.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuestionClick = (question) => {
    setInputMessage(question);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!user?.isPaid) {
    return (
      <>
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="w-24 h-24 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="text-warning" size={48} />
            </div>
            <h1 className="text-3xl font-bold text-text mb-4">Chat Educativo Premium</h1>
            <p className="text-gray-400 max-w-2xl mx-auto mb-8">
              Accede a nuestro asistente de IA especializado en trading educativo.
              Obtén respuestas detalladas sobre estrategias, análisis técnico y gestión de riesgo.
            </p>
            <Button variant="primary" onClick={() => setShowPaywall(true)}>
              Desbloquear Chat Premium
            </Button>
          </motion.div>
        </div>
        <PaywallModal
          isOpen={showPaywall}
          onClose={() => setShowPaywall(false)}
          feature="Chat Educativo"
        />
      </>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <MessageCircle className="mr-3 text-danger" size={32} />
            Chat Educativo
            <Crown className="ml-2 text-warning" size={24} />
          </h1>
          <p className="text-gray-400">
            Asistente de IA especializado en educación de trading
          </p>
        </div>
      </motion.div>

      {/* Warning */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="border-warning bg-warning bg-opacity-10">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="text-warning mt-1" size={20} />
            <div>
              <h3 className="font-semibold text-warning mb-1">Aviso Importante</h3>
              <p className="text-sm text-gray-300">
                Este chat es solo para fines educativos. No proporciono señales de trading ni consejos de inversión.
                Siempre haz tu propia investigación antes de tomar decisiones financieras.
              </p>
            </div>
          </div>
        </Card>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Preguntas Sugeridas */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="lg:col-span-1"
        >
          <Card>
            <h3 className="font-semibold text-text mb-4">Preguntas Sugeridas</h3>
            <div className="space-y-2">
              {predefinedQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => handleQuestionClick(question)}
                  className="w-full text-left p-3 rounded-lg border border-accent hover:border-danger hover:bg-danger hover:bg-opacity-10 transition-all duration-200 text-sm text-gray-300 hover:text-white"
                >
                  {question}
                </button>
              ))}
            </div>
          </Card>

          {/* Recursos de Trading */}
          <Card className="mt-6">
            <h3 className="font-semibold text-text mb-4">Recursos Útiles</h3>
            <div className="space-y-3">
              {tradingResources.map((resource, index) => (
                <a
                  key={index}
                  href={resource.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-3 rounded-lg border border-accent hover:border-danger hover:bg-danger hover:bg-opacity-10 transition-all duration-200"
                >
                  <h4 className="font-medium text-text text-sm">{resource.name}</h4>
                  <p className="text-xs text-gray-400 mt-1">{resource.description}</p>
                </a>
              ))}
            </div>
          </Card>
        </motion.div>

        {/* Chat Area */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="lg:col-span-3"
        >
          <Card className="h-[600px] flex flex-col p-0">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-6 space-y-4">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-3 max-w-[80%] ${
                    message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      message.type === 'user' ? 'bg-danger' : 'bg-accent'
                    }`}>
                      {message.type === 'user' ? <User size={16} /> : <Bot size={16} />}
                    </div>
                    <div className={`p-4 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-danger text-white'
                        : 'bg-primary border border-accent text-gray-300'
                    }`}>
                      <p className="whitespace-pre-wrap">{message.content}</p>
                      <p className={`text-xs mt-2 ${
                        message.type === 'user' ? 'text-red-200' : 'text-gray-500'
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}

              {isLoading && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center">
                      <Bot size={16} />
                    </div>
                    <div className="bg-primary border border-accent p-4 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="spinner"></div>
                        <span className="text-gray-400">Pensando...</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="border-t border-accent p-4">
              <div className="flex space-x-4">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Escribe tu pregunta sobre trading..."
                  className="flex-1 bg-primary border border-accent rounded-lg px-4 py-3 text-text placeholder-gray-400 focus:outline-none focus:border-danger focus:ring-2 focus:ring-danger focus:ring-opacity-50"
                  disabled={isLoading}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  variant="primary"
                >
                  <Send size={16} />
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default ChatEducativo;
