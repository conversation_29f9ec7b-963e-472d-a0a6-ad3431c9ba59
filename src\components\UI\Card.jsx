import React from 'react';
import { motion } from 'framer-motion';

const Card = ({ 
  children, 
  className = '', 
  hover = true,
  padding = 'p-6',
  shadow = 'shadow-lg',
  ...props 
}) => {
  const baseClasses = `bg-secondary border border-accent rounded-xl transition-all duration-200 ${padding} ${shadow}`;
  const hoverClasses = hover ? 'hover:shadow-xl hover:border-gray-500 transform hover:scale-105' : '';
  
  return (
    <motion.div
      className={`${baseClasses} ${hoverClasses} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={hover ? { y: -5 } : {}}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default Card;
