import React from 'react';
import { motion } from 'framer-motion';
import { Crown, Check, Star, CreditCard } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';

const Planes = () => {
  const { user, upgradeToPaid } = useAuth();

  const plans = [
    {
      name: 'Gratuito',
      price: '$0',
      period: '/mes',
      description: 'Perfecto para comenzar',
      features: [
        'Acceso al logo GERMAYORI',
        'Registro de cuenta',
        'TradingView básico',
        'Academia limitada',
        'Comunidad básica'
      ],
      current: !user?.isPaid,
      buttonText: user?.isPaid ? 'Plan Anterior' : 'Plan Actual',
      buttonVariant: 'outline',
      popular: false
    },
    {
      name: 'Premium',
      price: '$29',
      period: '/mes',
      description: 'Para traders serios',
      features: [
        'Todo lo del plan gratuito',
        'Chat educativo con IA',
        'Análisis OCR avanzado',
        'Sesiones de trading',
        'Estadísticas detalladas',
        'Soporte prioritario',
        'Academia completa',
        'Alertas personalizadas',
        'Herramientas avanzadas'
      ],
      current: user?.isPaid,
      buttonText: user?.isPaid ? 'Plan Actual' : 'Actualizar Ahora',
      buttonVariant: user?.isPaid ? 'outline' : 'primary',
      popular: true
    },
    {
      name: 'Pro',
      price: '$59',
      period: '/mes',
      description: 'Para profesionales',
      features: [
        'Todo lo del plan Premium',
        'Análisis técnico avanzado',
        'Señales de trading premium',
        'Mentorías 1:1 mensuales',
        'Acceso a webinars exclusivos',
        'Herramientas de backtesting',
        'API personalizada',
        'Soporte 24/7',
        'Acceso beta a nuevas funciones'
      ],
      current: false,
      buttonText: 'Próximamente',
      buttonVariant: 'secondary',
      disabled: true,
      popular: false
    }
  ];

  const handleUpgrade = () => {
    if (!user?.isPaid) {
      upgradeToPaid();
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-4xl font-bold text-text mb-4">
          Planes y Precios
        </h1>
        <p className="text-gray-400 text-lg max-w-2xl mx-auto">
          Elige el plan que mejor se adapte a tus necesidades de trading educativo
        </p>
      </motion.div>

      {/* Current Plan Status */}
      {user && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="text-center border-warning bg-warning bg-opacity-10">
            <div className="flex items-center justify-center mb-4">
              {user.isPaid ? (
                <Crown className="text-warning mr-2" size={24} />
              ) : (
                <CreditCard className="text-warning mr-2" size={24} />
              )}
              <h3 className="text-xl font-semibold text-text">
                Plan Actual: {user.plan}
              </h3>
            </div>
            <p className="text-gray-400">
              {user.isPaid 
                ? '¡Tienes acceso completo a todas las funciones Premium!'
                : 'Actualiza a Premium para desbloquear todas las funciones'
              }
            </p>
          </Card>
        </motion.div>
      )}

      {/* Plans Grid */}
      <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 + index * 0.1 }}
            className="relative"
          >
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                <div className="bg-gradient-danger text-white px-6 py-2 rounded-full text-sm font-medium flex items-center space-x-1">
                  <Star size={14} />
                  <span>Más Popular</span>
                </div>
              </div>
            )}
            
            <Card className={`h-full text-center ${
              plan.popular ? 'border-danger shadow-germayori scale-105' : ''
            } ${plan.current ? 'border-warning bg-warning bg-opacity-5' : ''}`}>
              {plan.current && (
                <div className="absolute top-4 right-4">
                  <div className="bg-warning text-primary px-3 py-1 rounded-full text-xs font-medium">
                    Actual
                  </div>
                </div>
              )}

              <div className="mb-8">
                <h3 className="text-2xl font-bold text-text mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-4">{plan.description}</p>
                <div className="flex items-baseline justify-center mb-2">
                  <span className="text-4xl font-bold text-text">{plan.price}</span>
                  <span className="text-gray-400 ml-1">{plan.period}</span>
                </div>
              </div>

              <ul className="space-y-4 mb-8 text-left">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start space-x-3">
                    <Check className="text-success mt-0.5 flex-shrink-0" size={16} />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                variant={plan.buttonVariant}
                className="w-full"
                disabled={plan.disabled || plan.current}
                onClick={plan.name === 'Premium' && !user?.isPaid ? handleUpgrade : undefined}
              >
                {plan.buttonText}
              </Button>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* FAQ */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <Card>
          <h3 className="text-2xl font-bold text-text mb-6 text-center">
            Preguntas Frecuentes
          </h3>
          <div className="space-y-6">
            <div>
              <h4 className="font-semibold text-text mb-2">
                ¿Puedo cancelar mi suscripción en cualquier momento?
              </h4>
              <p className="text-gray-400">
                Sí, puedes cancelar tu suscripción en cualquier momento desde tu panel de configuración. 
                Mantendrás el acceso hasta el final de tu período de facturación actual.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-text mb-2">
                ¿Ofrecen reembolsos?
              </h4>
              <p className="text-gray-400">
                Ofrecemos una garantía de reembolso de 7 días para nuevos suscriptores Premium. 
                Si no estás satisfecho, contáctanos para un reembolso completo.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-text mb-2">
                ¿El chat educativo realmente no da señales de trading?
              </h4>
              <p className="text-gray-400">
                Correcto. Nuestro chat educativo está diseñado exclusivamente para enseñar conceptos, 
                estrategias y análisis técnico. No proporcionamos señales de trading ni consejos de inversión.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-text mb-2">
                ¿Cuándo estará disponible el plan Pro?
              </h4>
              <p className="text-gray-400">
                El plan Pro está en desarrollo y estará disponible próximamente. 
                Los usuarios Premium tendrán acceso prioritario y descuentos especiales.
              </p>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* CTA */}
      {!user?.isPaid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center"
        >
          <Card className="bg-gradient-danger">
            <div className="text-center text-white">
              <Crown className="mx-auto mb-4" size={48} />
              <h3 className="text-2xl font-bold mb-2">
                ¿Listo para llevar tu trading al siguiente nivel?
              </h3>
              <p className="mb-6 opacity-90">
                Únete a miles de traders que ya están mejorando sus habilidades con GERMAYORI Premium
              </p>
              <Button
                variant="secondary"
                size="lg"
                onClick={handleUpgrade}
                className="bg-white text-danger hover:bg-gray-100"
              >
                Comenzar Ahora
              </Button>
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default Planes;
