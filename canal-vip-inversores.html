<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Canal VIP Inversores</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .investor-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .investor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .gold-gradient {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #000;
        }
        .earnings-positive {
            color: #10b981;
        }
        .status-active {
            background: #10b981;
        }
        .status-pending {
            background: #f59e0b;
        }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="trading-vivo.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-green-400"></i>
                    <span>Trading en Vivo</span>
                </a>

                <a href="canal-inversiones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins mr-3 text-yellow-400"></i>
                            <span>Inversiones</span>
                        </div>
                        <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-bold">VIP</span>
                    </div>
                </a>

                <a href="canal-vip-inversores.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-crown mr-3 text-gold-400"></i>
                            <span>VIP Inversores</span>
                        </div>
                        <span class="text-xs bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full font-bold">ELITE</span>
                    </div>
                </a>

                <a href="videos-educativos.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-signal mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- VIP Investors Content -->
        <div class="flex-1 main-content m-4 rounded-lg overflow-y-auto">

            <!-- Header -->
            <div class="bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 text-white p-6 rounded-t-lg">
                <div class="text-center">
                    <h2 class="text-4xl font-bold mb-2">👑 CANAL VIP INVERSORES</h2>
                    <p class="text-yellow-100 text-lg">Gestión exclusiva de inversiones GERMAYORI</p>
                    <div class="mt-4 bg-white bg-opacity-20 rounded-lg p-4 inline-block">
                        <div class="text-2xl font-bold">ELITE ACCESS</div>
                        <div class="text-yellow-100">Solo para inversores verificados</div>
                    </div>
                </div>
            </div>

            <!-- Statistics Dashboard -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="investor-card rounded-lg p-6 text-center">
                        <div class="text-3xl mb-2">👥</div>
                        <div class="text-2xl font-bold text-white" id="total-investors">0</div>
                        <div class="text-gray-300 text-sm">Inversores Activos</div>
                    </div>
                    <div class="investor-card rounded-lg p-6 text-center">
                        <div class="text-3xl mb-2">💰</div>
                        <div class="text-2xl font-bold text-green-400" id="total-invested">$0</div>
                        <div class="text-gray-300 text-sm">Capital Total</div>
                    </div>
                    <div class="investor-card rounded-lg p-6 text-center">
                        <div class="text-3xl mb-2">📈</div>
                        <div class="text-2xl font-bold text-yellow-400" id="monthly-returns">$0</div>
                        <div class="text-gray-300 text-sm">Retornos Mensuales</div>
                    </div>
                    <div class="investor-card rounded-lg p-6 text-center">
                        <div class="text-3xl mb-2">🔔</div>
                        <div class="text-2xl font-bold text-blue-400" id="pending-withdrawals">0</div>
                        <div class="text-gray-300 text-sm">Retiros Pendientes</div>
                    </div>
                </div>

                <!-- VIP Access Section -->
                <div class="investor-card rounded-lg p-8 mb-6" style="background: linear-gradient(135deg, #8b5cf6, #ec4899, #f59e0b); border: 3px solid #ffd700;">
                    <div class="text-center">
                        <div class="text-6xl mb-4">👑</div>
                        <h3 class="text-3xl font-bold text-white mb-4">ACCESO VIP ELITE</h3>
                        <div class="text-2xl font-bold text-yellow-300 mb-6">$2,500 USD</div>

                        <div class="bg-black bg-opacity-30 rounded-lg p-6 mb-6">
                            <h4 class="text-xl font-bold text-white mb-4">🌟 Beneficios VIP ELITE:</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                                <div class="space-y-2">
                                    <div class="flex items-center text-white">
                                        <i class="fas fa-crown text-yellow-400 mr-3"></i>
                                        <span>Gestión directa de inversiones</span>
                                    </div>
                                    <div class="flex items-center text-white">
                                        <i class="fas fa-chart-line text-green-400 mr-3"></i>
                                        <span>Acceso a estrategias exclusivas</span>
                                    </div>
                                    <div class="flex items-center text-white">
                                        <i class="fas fa-phone text-blue-400 mr-3"></i>
                                        <span>Línea directa con jhon0608</span>
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center text-white">
                                        <i class="fas fa-eye text-purple-400 mr-3"></i>
                                        <span>Transparencia total en trades</span>
                                    </div>
                                    <div class="flex items-center text-white">
                                        <i class="fas fa-money-bill-wave text-yellow-400 mr-3"></i>
                                        <span>Retornos preferenciales</span>
                                    </div>
                                    <div class="flex items-center text-white">
                                        <i class="fas fa-shield-alt text-red-400 mr-3"></i>
                                        <span>Protección de capital garantizada</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button onclick="showVipPaymentGateway()" class="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-12 py-4 rounded-lg text-2xl font-bold hover:shadow-2xl transition-all transform hover:scale-105">
                            <i class="fas fa-crown mr-3"></i>ACCEDER VIP ELITE
                        </button>
                    </div>
                </div>

                <!-- Investors List -->
                <div class="investor-card rounded-lg p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-2xl font-bold text-white">📋 Lista de Inversores (Orden Alfabético)</h3>
                        <button onclick="processMonthlyPayments()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-bold">
                            <i class="fas fa-money-bill-wave mr-2"></i>Procesar Pagos Mensuales
                        </button>
                    </div>

                    <div id="investors-list" class="space-y-4">
                        <!-- Los inversores se cargarán aquí dinámicamente -->
                    </div>
                </div>

                <!-- Withdrawal Requests -->
                <div id="withdrawal-requests" class="investor-card rounded-lg p-6 mt-6 hidden">
                    <h3 class="text-2xl font-bold text-white mb-6">💸 Solicitudes de Retiro</h3>
                    <div id="withdrawals-list" class="space-y-4">
                        <!-- Las solicitudes se cargarán aquí -->
                    </div>
                </div>

            </div>

        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }
            }
        }

        // Cargar estadísticas
        function loadStatistics() {
            const vipInvestors = JSON.parse(localStorage.getItem('germayori_vip_investors') || '[]');

            const totalInvestors = vipInvestors.length;
            const totalInvested = vipInvestors.reduce((sum, inv) => sum + inv.amount, 0);
            const monthlyReturns = vipInvestors.reduce((sum, inv) => sum + inv.monthlyReturn, 0);
            const pendingWithdrawals = vipInvestors.reduce((sum, inv) => sum + inv.withdrawalRequests.filter(req => req.status === 'pending').length, 0);

            document.getElementById('total-investors').textContent = totalInvestors;
            document.getElementById('total-invested').textContent = '$' + totalInvested.toLocaleString();
            document.getElementById('monthly-returns').textContent = '$' + monthlyReturns.toLocaleString();
            document.getElementById('pending-withdrawals').textContent = pendingWithdrawals;
        }

        // Cargar lista de inversores
        function loadInvestorsList() {
            const vipInvestors = JSON.parse(localStorage.getItem('germayori_vip_investors') || '[]');
            const investorsList = document.getElementById('investors-list');

            if (vipInvestors.length === 0) {
                investorsList.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <i class="fas fa-users text-4xl mb-4"></i>
                        <div>No hay inversores registrados aún</div>
                    </div>
                `;
                return;
            }

            investorsList.innerHTML = vipInvestors.map(investor => `
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-20">
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
                        <div class="md:col-span-2">
                            <div class="font-bold text-white text-lg">${investor.name}</div>
                            <div class="text-gray-300 text-sm">${investor.email}</div>
                            <div class="text-gray-400 text-xs">${investor.phone}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-yellow-400 font-bold text-xl">$${investor.amount.toLocaleString()}</div>
                            <div class="text-gray-300 text-xs">Inversión</div>
                        </div>
                        <div class="text-center">
                            <div class="text-green-400 font-bold text-lg">$${investor.monthlyReturn.toFixed(2)}</div>
                            <div class="text-gray-300 text-xs">Mensual</div>
                        </div>
                        <div class="text-center">
                            <div class="text-blue-400 font-bold text-lg">$${investor.totalEarnings.toFixed(2)}</div>
                            <div class="text-gray-300 text-xs">Total Ganado</div>
                        </div>
                        <div class="text-center">
                            <span class="status-${investor.status} text-white px-3 py-1 rounded-full text-xs font-bold">
                                ${investor.status.toUpperCase()}
                            </span>
                            <div class="mt-2">
                                <button onclick="requestWithdrawal('${investor.id}')" class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-xs">
                                    Solicitar Retiro
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-white border-opacity-20">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-300">
                            <div>Inicio: ${new Date(investor.contractStart).toLocaleDateString()}</div>
                            <div>Fin: ${new Date(investor.contractEnd).toLocaleDateString()}</div>
                            <div>Próximo pago: ${new Date(investor.nextPayment).toLocaleDateString()}</div>
                            <div>Método: ${investor.paymentMethod.toUpperCase()}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Procesar pagos mensuales
        function processMonthlyPayments() {
            let vipInvestors = JSON.parse(localStorage.getItem('germayori_vip_investors') || '[]');
            const today = new Date();
            let paymentsProcessed = 0;

            vipInvestors.forEach(investor => {
                const nextPaymentDate = new Date(investor.nextPayment);

                // Si es tiempo de pago (o ya pasó)
                if (today >= nextPaymentDate) {
                    investor.totalEarnings += investor.monthlyReturn;

                    // Programar próximo pago (30 días después)
                    const nextMonth = new Date(nextPaymentDate);
                    nextMonth.setDate(nextMonth.getDate() + 30);
                    investor.nextPayment = nextMonth.toISOString();

                    paymentsProcessed++;

                    console.log(`💰 Pago procesado para ${investor.name}: $${investor.monthlyReturn}`);
                }
            });

            localStorage.setItem('germayori_vip_investors', JSON.stringify(vipInvestors));

            if (paymentsProcessed > 0) {
                alert(`✅ ${paymentsProcessed} pagos mensuales procesados exitosamente!`);
                loadStatistics();
                loadInvestorsList();
            } else {
                alert('ℹ️ No hay pagos pendientes por procesar en este momento.');
            }
        }

        // Solicitar retiro
        function requestWithdrawal(investorId) {
            const amount = prompt('💸 Ingresa el monto a retirar (solo ganancias):');
            if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
                alert('❌ Monto inválido');
                return;
            }

            let vipInvestors = JSON.parse(localStorage.getItem('germayori_vip_investors') || '[]');
            const investor = vipInvestors.find(inv => inv.id == investorId);

            if (!investor) {
                alert('❌ Inversor no encontrado');
                return;
            }

            const withdrawalAmount = parseFloat(amount);

            if (withdrawalAmount > investor.totalEarnings) {
                alert(`❌ No puedes retirar más de tus ganancias disponibles: $${investor.totalEarnings.toFixed(2)}`);
                return;
            }

            // Crear solicitud de retiro (15 días de espera)
            const withdrawalDate = new Date();
            withdrawalDate.setDate(withdrawalDate.getDate() + 15);

            const withdrawalRequest = {
                id: Date.now(),
                amount: withdrawalAmount,
                requestDate: new Date().toISOString(),
                scheduledDate: withdrawalDate.toISOString(),
                status: 'pending',
                investorName: investor.name,
                investorEmail: investor.email
            };

            investor.withdrawalRequests.push(withdrawalRequest);

            // Actualizar localStorage
            localStorage.setItem('germayori_vip_investors', JSON.stringify(vipInvestors));

            alert(`✅ Solicitud de retiro creada!\n\n💰 Monto: $${withdrawalAmount}\n📅 Fecha programada: ${withdrawalDate.toLocaleDateString()}\n⏰ Faltan 15 días para el retiro`);

            loadStatistics();
            loadInvestorsList();
            loadWithdrawalRequests();
        }

        // Cargar solicitudes de retiro
        function loadWithdrawalRequests() {
            const vipInvestors = JSON.parse(localStorage.getItem('germayori_vip_investors') || '[]');
            const allWithdrawals = [];

            vipInvestors.forEach(investor => {
                investor.withdrawalRequests.forEach(request => {
                    allWithdrawals.push({
                        ...request,
                        investorName: investor.name,
                        investorEmail: investor.email
                    });
                });
            });

            if (allWithdrawals.length > 0) {
                document.getElementById('withdrawal-requests').classList.remove('hidden');

                const withdrawalsList = document.getElementById('withdrawals-list');
                withdrawalsList.innerHTML = allWithdrawals.map(withdrawal => {
                    const daysLeft = Math.ceil((new Date(withdrawal.scheduledDate) - new Date()) / (1000 * 60 * 60 * 24));

                    return `
                        <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-20">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
                                <div>
                                    <div class="font-bold text-white">${withdrawal.investorName}</div>
                                    <div class="text-gray-300 text-sm">${withdrawal.investorEmail}</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-yellow-400 font-bold text-xl">$${withdrawal.amount.toFixed(2)}</div>
                                    <div class="text-gray-300 text-xs">Monto</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-blue-400 font-bold">${new Date(withdrawal.requestDate).toLocaleDateString()}</div>
                                    <div class="text-gray-300 text-xs">Solicitado</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-green-400 font-bold">${new Date(withdrawal.scheduledDate).toLocaleDateString()}</div>
                                    <div class="text-gray-300 text-xs">${daysLeft > 0 ? `${daysLeft} días` : 'Listo'}</div>
                                </div>
                                <div class="text-center">
                                    <span class="status-${withdrawal.status} text-white px-3 py-1 rounded-full text-xs font-bold">
                                        ${withdrawal.status.toUpperCase()}
                                    </span>
                                    ${daysLeft <= 0 ? `
                                        <div class="mt-2">
                                            <button onclick="processWithdrawal('${withdrawal.id}')" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs">
                                                Procesar
                                            </button>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            }
        }

        // Procesar retiro
        function processWithdrawal(withdrawalId) {
            let vipInvestors = JSON.parse(localStorage.getItem('germayori_vip_investors') || '[]');

            vipInvestors.forEach(investor => {
                const withdrawal = investor.withdrawalRequests.find(req => req.id == withdrawalId);
                if (withdrawal) {
                    withdrawal.status = 'completed';
                    investor.totalEarnings -= withdrawal.amount;

                    alert(`✅ Retiro procesado!\n\n👤 Inversor: ${investor.name}\n💰 Monto: $${withdrawal.amount}\n📧 Notificación enviada a: ${investor.email}`);
                }
            });

            localStorage.setItem('germayori_vip_investors', JSON.stringify(vipInvestors));

            loadStatistics();
            loadInvestorsList();
            loadWithdrawalRequests();
        }

        // Mostrar pasarela de pago VIP ELITE
        function showVipPaymentGateway() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.95);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            `;

            modal.innerHTML = `
                <div style="background: linear-gradient(135deg, #8b5cf6, #ec4899, #f59e0b); border-radius: 25px; padding: 40px; max-width: 700px; width: 100%; border: 4px solid #ffd700; position: relative; box-shadow: 0 0 50px rgba(255, 215, 0, 0.5);">
                    <button onclick="this.closest('.vip-payment-modal').remove()" style="position: absolute; top: 20px; right: 20px; background: #ff4444; color: white; border: none; width: 40px; height: 40px; border-radius: 50%; cursor: pointer; font-size: 20px; font-weight: bold;">&times;</button>

                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="font-size: 4rem; margin-bottom: 15px;">👑</div>
                        <h2 style="color: white; font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">💎 PAGO VIP ELITE</h2>
                        <div style="color: #ffd700; font-size: 2rem; font-weight: bold; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">$2,500 USD</div>
                        <div style="color: white; font-size: 1rem; opacity: 0.9;">Acceso exclusivo al canal VIP de inversores ELITE</div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                        <!-- Crypto VIP -->
                        <div style="text-align: center; background: rgba(0,0,0,0.3); padding: 25px; border-radius: 20px; border: 3px solid #ffd700;">
                            <h3 style="color: #ffd700; margin-bottom: 15px; font-size: 1.3rem;">₿ CRYPTO ELITE</h3>
                            <div style="background: white; padding: 20px; border-radius: 15px; margin-bottom: 15px;">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ29sZEdyYWRpZW50IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmZDcwMDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZmZhNTAwO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjE1MCIgaGVpZ2h0PSIxNTAiIGZpbGw9InVybCgjZ29sZEdyYWRpZW50KSIvPgogIDx0ZXh0IHg9Ijc1IiB5PSI2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2IiBmaWxsPSJibGFjayIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC13ZWlnaHQ9ImJvbGQiPkNSWVBUTzwvdGV4dD4KICA8dGV4dCB4PSI3NSIgeT0iODAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0iYmxhY2siIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtd2VpZ2h0PSJib2xkIj5FTElURTwvdGV4dD4KICA8dGV4dCB4PSI3NSIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj4kMjUwMDwvdGV4dD4KICA8dGV4dCB4PSI3NSIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5RUiBDb2RlPC90ZXh0Pgo8L3N2Zz4=" style="width: 140px; height: 140px;" alt="Crypto Elite QR">
                            </div>
                            <div style="color: white; font-size: 0.9rem; margin-bottom: 10px; font-weight: bold;">USDT/BTC/ETH</div>
                            <div style="color: #ffd700; font-size: 0.8rem; margin-bottom: 15px;"><EMAIL></div>
                            <button onclick="confirmVipPayment('crypto')" style="background: linear-gradient(45deg, #ffd700, #ffed4e); color: black; border: none; padding: 12px 25px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 0.9rem;">✅ Pagué con Crypto</button>
                        </div>

                        <!-- Wire Transfer VIP -->
                        <div style="text-align: center; background: rgba(0,0,0,0.3); padding: 25px; border-radius: 20px; border: 3px solid #ffd700;">
                            <h3 style="color: #ffd700; margin-bottom: 15px; font-size: 1.3rem;">🏦 WIRE ELITE</h3>
                            <div style="background: white; padding: 20px; border-radius: 15px; margin-bottom: 15px;">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYmFua0dyYWRpZW50IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzAwNzBiYTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMDA0MDgwO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjE1MCIgaGVpZ2h0PSIxNTAiIGZpbGw9InVybCgjYmFua0dyYWRpZW50KSIvPgogIDx0ZXh0IHg9Ijc1IiB5PSI2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC13ZWlnaHQ9ImJvbGQiPldJUkU8L3RleHQ+CiAgPHRleHQgeD0iNzUiIHk9IjgwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXdlaWdodD0iYm9sZCI+RUxJVEU8L3RleHQ+CiAgPHRleHQgeD0iNzUiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+JDI1MDA8L3RleHQ+CiAgPHRleHQgeD0iNzUiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+UVIgQ29kZTwvdGV4dD4KPC9zdmc+" style="width: 140px; height: 140px;" alt="Wire Elite QR">
                            </div>
                            <div style="color: white; font-size: 0.9rem; margin-bottom: 10px; font-weight: bold;">Transferencia Internacional</div>
                            <div style="color: #ffd700; font-size: 0.8rem; margin-bottom: 15px;">SWIFT: GERMAYORI2500</div>
                            <button onclick="confirmVipPayment('wire')" style="background: linear-gradient(45deg, #0070ba, #004080); color: white; border: none; padding: 12px 25px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 0.9rem;">✅ Pagué con Wire</button>
                        </div>
                    </div>

                    <div style="background: rgba(0,0,0,0.5); border: 3px solid #ffd700; border-radius: 20px; padding: 25px; margin-bottom: 25px;">
                        <h4 style="color: #ffd700; margin-bottom: 15px; font-size: 1.2rem; text-align: center;">👑 INSTRUCCIONES VIP ELITE:</h4>
                        <div style="color: white; font-size: 0.95rem; line-height: 1.6;">
                            <div style="margin-bottom: 10px;">🔸 <strong>Crypto:</strong> Envía $2,500 USD en USDT/BTC/ETH a la wallet VIP</div>
                            <div style="margin-bottom: 10px;">🔸 <strong>Wire:</strong> Transferencia bancaria internacional de $2,500 USD</div>
                            <div style="margin-bottom: 10px;">🔸 <strong>Verificación:</strong> Acceso activado en 24-48 horas</div>
                            <div style="color: #ffd700; font-weight: bold;">🔸 <strong>Beneficio:</strong> Acceso inmediato al canal VIP ELITE de inversores</div>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <div style="color: #ffd700; font-size: 0.9rem; margin-bottom: 15px; font-weight: bold;">
                            ⚠️ Solo confirma después de enviar el pago completo de $2,500 USD
                        </div>
                        <button onclick="this.closest('.vip-payment-modal').remove()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 12px 25px; border-radius: 20px; cursor: pointer;">Cancelar</button>
                    </div>
                </div>
            `;

            modal.className = 'vip-payment-modal';
            document.body.appendChild(modal);
        }

        // Confirmar pago VIP
        function confirmVipPayment(method) {
            const methodNames = {
                'crypto': 'Criptomonedas (USDT/BTC/ETH)',
                'wire': 'Transferencia Bancaria Internacional'
            };

            const confirmed = confirm(`¿Confirmas que enviaste $2,500 USD via ${methodNames[method]}?\n\n👑 ACCESO VIP ELITE\n\n⚠️ Solo confirma si ya enviaste el pago completo.\n\nUna vez confirmado:\n• Tu acceso VIP será activado\n• jhon0608 verificará el pago\n• Recibirás acceso en 24-48 horas\n• Acceso al canal ELITE de inversores`);

            if (confirmed) {
                // Cerrar modal
                document.querySelector('.vip-payment-modal').remove();

                // Activar acceso VIP
                activateVipAccess(method);

                // Notificar a jhon0608
                notifyVipPayment(method);

                alert('👑 ¡PAGO VIP ELITE CONFIRMADO!\n\n✅ Tu acceso ha sido registrado\n📧 jhon0608 verificará tu pago\n⏰ Acceso activado en 24-48 horas\n💎 Bienvenido al círculo ELITE de GERMAYORI\n\n¡Prepárate para el nivel más alto de trading institucional!');

                // Recargar página
                setTimeout(() => {
                    location.reload();
                }, 3000);
            }
        }

        // Activar acceso VIP
        function activateVipAccess(paymentMethod) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');

            user.vipElite = {
                hasAccess: true,
                purchaseDate: new Date().toISOString(),
                paymentMethod: paymentMethod,
                paymentAmount: 2500,
                paymentStatus: 'pending_verification',
                accessLevel: 'ELITE'
            };

            localStorage.setItem('germayori_user', JSON.stringify(user));
        }

        // Notificar pago VIP a jhon0608
        function notifyVipPayment(paymentMethod) {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            const methodNames = {
                'crypto': 'Criptomonedas ELITE (USDT/BTC/ETH)',
                'wire': 'Transferencia Bancaria Internacional ELITE'
            };

            // Guardar notificación para jhon0608
            let notifications = JSON.parse(localStorage.getItem('jhon0608_notifications') || '[]');

            notifications.unshift({
                id: 'vip_elite_' + Date.now(),
                type: 'vip_elite_payment',
                user: user,
                amount: 2500,
                method: paymentMethod,
                methodName: methodNames[paymentMethod],
                date: new Date().toISOString(),
                status: 'pending_verification',
                urgent: true,
                priority: 'ELITE'
            });

            localStorage.setItem('jhon0608_notifications', JSON.stringify(notifications));

            console.log('👑 NOTIFICACIÓN VIP ELITE PARA jhon0608:');
            console.log(`💎 NUEVO PAGO VIP ELITE - $2,500 USD`);
            console.log(`👤 Cliente ELITE: ${user.name}`);
            console.log(`📧 Email: ${user.email}`);
            console.log(`💳 Método: ${methodNames[paymentMethod]}`);
            console.log(`📅 Fecha: ${new Date().toLocaleString()}`);
            console.log(`🚨 PRIORIDAD MÁXIMA - VERIFICAR INMEDIATAMENTE`);
        }

        // Inicializar
        window.onload = function() {
            checkAuth();
            loadUserInfo();
            loadStatistics();
            loadInvestorsList();
            loadWithdrawalRequests();
        };
    </script>
</body>
</html>
