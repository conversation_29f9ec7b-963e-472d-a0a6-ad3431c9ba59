<!DOCTYPE html>
<html>
<head>
    <title>GERMAYORI PANEL SIMPLE</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #1e3c72; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        h1 { color: #FFD700; text-align: center; }
        .section { background: rgba(255,255,255,0.1); padding: 20px; margin: 20px 0; border-radius: 10px; }
        input, select, textarea { width: 100%; padding: 10px; margin: 5px 0; border: none; border-radius: 5px; }
        button { background: #FFD700; color: black; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; }
        .user-item { background: rgba(255,255,255,0.1); padding: 10px; margin: 5px 0; border-radius: 5px; }
        #status { position: fixed; top: 10px; right: 10px; padding: 10px; border-radius: 5px; }
        .online { background: green; }
        .offline { background: red; }
    </style>
</head>
<body>
    <div id="status" class="offline">❌ Desconectado</div>

    <div class="container">
        <h1>🚀 GERMAYORI PANEL SIMPLE</h1>

        <!-- AGREGAR USUARIO -->
        <div class="section">
            <h2>👤 AGREGAR USUARIO</h2>
            <input type="text" id="userName" placeholder="Nombre completo">
            <input type="email" id="userEmail" placeholder="Email">
            <input type="password" id="userPassword" placeholder="Contraseña">
            <button onclick="addUser()">AGREGAR USUARIO</button>
        </div>

        <!-- AGREGAR SEÑAL -->
        <div class="section">
            <h2>📈 AGREGAR SEÑAL</h2>
            <select id="signalPair">
                <option value="EURUSD">EUR/USD</option>
                <option value="GBPUSD">GBP/USD</option>
                <option value="XAUUSD">XAU/USD (Oro)</option>
                <option value="NAS100">NAS100</option>
            </select>
            <select id="signalType">
                <option value="BUY">COMPRA</option>
                <option value="SELL">VENTA</option>
            </select>
            <input type="number" id="signalEntry" placeholder="Precio entrada" step="0.00001">
            <input type="number" id="signalSL" placeholder="Stop Loss" step="0.00001">
            <input type="number" id="signalTP" placeholder="Take Profit" step="0.00001">
            <textarea id="signalAnalysis" placeholder="Análisis (opcional)"></textarea>
            <button onclick="addSignal()">AGREGAR SEÑAL</button>
        </div>

        <!-- VER USUARIOS -->
        <div class="section">
            <h2>👥 USUARIOS EN LA BASE DE DATOS</h2>
            <button onclick="loadUsers()">🔄 ACTUALIZAR USUARIOS</button>
            <div id="usersList">
                <p>Cargando usuarios...</p>
            </div>
        </div>

        <!-- SUBIR VIDEO EDUCATIVO -->
        <div class="section">
            <h2>📹 SUBIR VIDEO EDUCATIVO</h2>
            <input type="text" id="videoTitle" placeholder="Título del video">
            <textarea id="videoDescription" placeholder="Descripción del video"></textarea>
            <select id="videoCategory">
                <option value="basico">Básico ($45/mes)</option>
                <option value="intermedio">Intermedio ($70/mes)</option>
                <option value="avanzado">Avanzado ($95/mes)</option>
            </select>
            <input type="url" id="videoUrl" placeholder="URL del video (YouTube, Vimeo, etc.)">
            <input type="file" id="videoFile" accept="video/*" style="margin: 10px 0;">
            <button onclick="uploadVideo()">📹 SUBIR VIDEO</button>
        </div>

        <!-- VER SEÑALES -->
        <div class="section">
            <h2>📊 SEÑALES EN LA BASE DE DATOS</h2>
            <button onclick="loadSignals()">🔄 ACTUALIZAR SEÑALES</button>
            <div id="signalsList">
                <p>Cargando señales...</p>
            </div>
        </div>

        <!-- VER VIDEOS -->
        <div class="section">
            <h2>📹 VIDEOS EN LA BASE DE DATOS</h2>
            <button onclick="loadVideos()">🔄 ACTUALIZAR VIDEOS</button>
            <div id="videosList">
                <p>Cargando videos...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000/api';

        // Verificar conexión
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    document.getElementById('status').innerHTML = '✅ Conectado';
                    document.getElementById('status').className = 'online';
                    loadUsers();
                    loadSignals();
                } else {
                    throw new Error('No conectado');
                }
            } catch (error) {
                document.getElementById('status').innerHTML = '❌ Error: ' + error.message;
                document.getElementById('status').className = 'offline';
            }
        }

        // Agregar usuario
        async function addUser() {
            const name = document.getElementById('userName').value;
            const email = document.getElementById('userEmail').value;
            const password = document.getElementById('userPassword').value;

            if (!name || !email || !password) {
                alert('Completa todos los campos');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name, email, password })
                });

                const result = await response.json();

                if (response.ok) {
                    alert('✅ Usuario agregado: ' + result.user.name);
                    document.getElementById('userName').value = '';
                    document.getElementById('userEmail').value = '';
                    document.getElementById('userPassword').value = '';
                    loadUsers();
                } else {
                    alert('❌ Error: ' + result.error);
                }
            } catch (error) {
                alert('❌ Error de conexión: ' + error.message);
            }
        }

        // Agregar señal
        async function addSignal() {
            const pair = document.getElementById('signalPair').value;
            const type = document.getElementById('signalType').value;
            const entry_price = parseFloat(document.getElementById('signalEntry').value);
            const stop_loss = parseFloat(document.getElementById('signalSL').value);
            const take_profit = parseFloat(document.getElementById('signalTP').value);
            const analysis = document.getElementById('signalAnalysis').value;

            if (!pair || !type || !entry_price || !stop_loss || !take_profit) {
                alert('Completa todos los campos obligatorios');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/signals`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: 1,
                        pair,
                        type,
                        entry_price,
                        stop_loss,
                        take_profit,
                        analysis,
                        risk_level: 'medium',
                        timeframe: 'H1'
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    alert('✅ Señal agregada: ' + pair + ' ' + type);
                    document.getElementById('signalEntry').value = '';
                    document.getElementById('signalSL').value = '';
                    document.getElementById('signalTP').value = '';
                    document.getElementById('signalAnalysis').value = '';
                    loadSignals();
                } else {
                    alert('❌ Error: ' + result.error);
                }
            } catch (error) {
                alert('❌ Error de conexión: ' + error.message);
            }
        }

        // Cargar usuarios
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/users`);
                const users = await response.json();

                const usersList = document.getElementById('usersList');

                if (users.length === 0) {
                    usersList.innerHTML = '<p>No hay usuarios en la base de datos</p>';
                } else {
                    usersList.innerHTML = users.map(user => `
                        <div class="user-item">
                            <strong>${user.name}</strong><br>
                            📧 ${user.email}<br>
                            🏷️ ${user.type}<br>
                            📅 ${new Date(user.created_at).toLocaleDateString()}
                        </div>
                    `).join('');
                }
            } catch (error) {
                document.getElementById('usersList').innerHTML = '<p>❌ Error cargando usuarios: ' + error.message + '</p>';
            }
        }

        // Subir video
        async function uploadVideo() {
            const title = document.getElementById('videoTitle').value;
            const description = document.getElementById('videoDescription').value;
            const category = document.getElementById('videoCategory').value;
            const url = document.getElementById('videoUrl').value;
            const file = document.getElementById('videoFile').files[0];

            if (!title || !category) {
                alert('Completa al menos el título y la categoría');
                return;
            }

            if (!url && !file) {
                alert('Proporciona una URL o selecciona un archivo de video');
                return;
            }

            try {
                const videoData = {
                    title,
                    description,
                    category,
                    url: url || null,
                    filename: file ? file.name : null,
                    size: file ? file.size : null,
                    type: file ? file.type : null,
                    created_at: new Date().toISOString()
                };

                const response = await fetch(`${API_BASE}/videos`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(videoData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert('✅ Video subido: ' + title);
                    document.getElementById('videoTitle').value = '';
                    document.getElementById('videoDescription').value = '';
                    document.getElementById('videoUrl').value = '';
                    document.getElementById('videoFile').value = '';
                    loadVideos();
                } else {
                    alert('❌ Error: ' + result.error);
                }
            } catch (error) {
                alert('❌ Error de conexión: ' + error.message);
            }
        }

        // Cargar señales
        async function loadSignals() {
            try {
                const response = await fetch(`${API_BASE}/signals`);
                const signals = await response.json();

                const signalsList = document.getElementById('signalsList');

                if (signals.length === 0) {
                    signalsList.innerHTML = '<p>No hay señales en la base de datos</p>';
                } else {
                    signalsList.innerHTML = signals.map(signal => `
                        <div class="user-item">
                            <strong>${signal.pair} - ${signal.type}</strong><br>
                            📈 Entrada: ${signal.entry_price}<br>
                            🛑 SL: ${signal.stop_loss} | 🎯 TP: ${signal.take_profit}<br>
                            📅 ${new Date(signal.created_at).toLocaleDateString()}
                            ${signal.analysis ? '<br>📝 ' + signal.analysis : ''}
                        </div>
                    `).join('');
                }
            } catch (error) {
                document.getElementById('signalsList').innerHTML = '<p>❌ Error cargando señales: ' + error.message + '</p>';
            }
        }

        // Cargar videos
        async function loadVideos() {
            try {
                const response = await fetch(`${API_BASE}/videos`);
                const videos = await response.json();

                const videosList = document.getElementById('videosList');

                if (videos.length === 0) {
                    videosList.innerHTML = '<p>No hay videos en la base de datos</p>';
                } else {
                    videosList.innerHTML = videos.map(video => `
                        <div class="user-item">
                            <strong>${video.title}</strong><br>
                            🏷️ Categoría: ${video.category}<br>
                            ${video.url ? '🔗 URL: ' + video.url + '<br>' : ''}
                            ${video.filename ? '📁 Archivo: ' + video.filename + '<br>' : ''}
                            📅 ${new Date(video.created_at).toLocaleDateString()}
                            ${video.description ? '<br>📝 ' + video.description : ''}
                        </div>
                    `).join('');
                }
            } catch (error) {
                document.getElementById('videosList').innerHTML = '<p>❌ Error cargando videos: ' + error.message + '</p>';
            }
        }

        // Inicializar
        checkConnection();
        setInterval(checkConnection, 30000); // Verificar cada 30 segundos

        // Cargar videos al inicializar
        setTimeout(() => {
            loadVideos();
        }, 1000);
    </script>
</body>
</html>
