const { User, Payment } = require('../models');

class ExpirationService {
    constructor() {
        this.intervalId = null;
        this.isRunning = false;
    }

    // Iniciar el servicio de expiración (cada hora)
    start() {
        if (this.isRunning) return;
        
        console.log('🔄 Iniciando servicio de expiración automática...');
        
        // Ejecutar inmediatamente
        this.checkExpiredPayments();
        
        // Ejecutar cada hora (3600000 ms)
        this.intervalId = setInterval(() => {
            this.checkExpiredPayments();
        }, 3600000); // 1 hora
        
        this.isRunning = true;
        console.log('✅ Servicio de expiración iniciado (cada 1 hora)');
    }

    // Detener el servicio
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isRunning = false;
        console.log('✅ Servicio de expiración detenido');
    }

    // Verificar y desactivar pagos expirados
    async checkExpiredPayments() {
        try {
            console.log('🔍 Verificando pagos expirados...');
            
            const now = new Date();
            
            // Buscar pagos que han expirado pero siguen activos
            const expiredPayments = await Payment.find({
                expiryDate: { $lt: now },
                status: 'completed',
                isActive: true
            }).populate('user_id', 'name email');

            if (expiredPayments.length === 0) {
                console.log('✅ No hay pagos expirados');
                return;
            }

            console.log(`⚠️ Encontrados ${expiredPayments.length} pagos expirados`);

            // Desactivar pagos expirados
            for (const payment of expiredPayments) {
                await Payment.findByIdAndUpdate(payment._id, {
                    isActive: false,
                    status: 'cancelled'
                });

                // También actualizar el plan del usuario a 'basic'
                if (payment.user_id) {
                    await User.findByIdAndUpdate(payment.user_id._id, {
                        plan: 'basic',
                        planPrice: 0
                    });

                    console.log(`❌ Usuario ${payment.user_id.name} (${payment.user_id.email}) - Plan expirado: ${payment.plan}`);
                }
            }

            console.log(`✅ ${expiredPayments.length} pagos desactivados automáticamente`);

        } catch (error) {
            console.error('❌ Error verificando pagos expirados:', error);
        }
    }

    // Obtener estadísticas de expiración
    async getExpirationStats() {
        try {
            const now = new Date();
            const in7Days = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

            const stats = {
                expired: await Payment.countDocuments({
                    expiryDate: { $lt: now },
                    status: 'completed'
                }),
                expiringSoon: await Payment.countDocuments({
                    expiryDate: { $gte: now, $lte: in7Days },
                    status: 'completed',
                    isActive: true
                }),
                active: await Payment.countDocuments({
                    expiryDate: { $gt: now },
                    status: 'completed',
                    isActive: true
                }),
                total: await Payment.countDocuments()
            };

            return stats;
        } catch (error) {
            console.error('Error obteniendo estadísticas:', error);
            return null;
        }
    }

    // Obtener usuarios que expiran pronto
    async getUsersExpiringSoon(days = 7) {
        try {
            const now = new Date();
            const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

            const expiringSoon = await Payment.find({
                expiryDate: { $gte: now, $lte: futureDate },
                status: 'completed',
                isActive: true
            }).populate('user_id', 'name email').sort({ expiryDate: 1 });

            return expiringSoon;
        } catch (error) {
            console.error('Error obteniendo usuarios que expiran pronto:', error);
            return [];
        }
    }

    // Verificar si un usuario tiene acceso activo
    async checkUserAccess(userId) {
        try {
            const now = new Date();
            
            const activePayment = await Payment.findOne({
                user_id: userId,
                expiryDate: { $gt: now },
                status: 'completed',
                isActive: true
            }).sort({ expiryDate: -1 });

            return {
                hasAccess: !!activePayment,
                payment: activePayment,
                daysRemaining: activePayment ? 
                    Math.ceil((activePayment.expiryDate - now) / (1000 * 60 * 60 * 24)) : 0
            };
        } catch (error) {
            console.error('Error verificando acceso del usuario:', error);
            return { hasAccess: false, payment: null, daysRemaining: 0 };
        }
    }
}

// Crear instancia única
const expirationService = new ExpirationService();

module.exports = expirationService;
