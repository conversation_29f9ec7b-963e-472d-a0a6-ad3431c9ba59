<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEST - Videos GERMAYORI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .btn {
            background: #FFD700;
            color: #000;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px;
            font-size: 16px;
        }

        .btn:hover {
            background: #FFA500;
        }

        input, select, textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: none;
            border-radius: 5px;
            font-size: 16px;
        }

        .video-card {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border: 1px solid #FFD700;
        }

        .video-title {
            color: #FFD700;
            font-size: 18px;
            font-weight: bold;
        }

        .play-btn {
            background: #FFD700;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 10px;
        }

        .debug-info {
            background: rgba(255, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #ff4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #FFD700;">🧪 TEST VIDEOS GERMAYORI</h1>

        <!-- SECCIÓN 1: SUBIR VIDEO -->
        <div class="section">
            <h2>📹 SUBIR VIDEO</h2>
            <input type="text" id="videoTitle" placeholder="Título del video" />
            <select id="videoCategory">
                <option value="basico">Básico</option>
                <option value="intermedio">Intermedio</option>
                <option value="avanzado">Avanzado</option>
            </select>
            <input type="url" id="videoUrl" placeholder="URL del video (YouTube, etc.)" />
            <textarea id="videoDescription" placeholder="Descripción del video"></textarea>
            <button class="btn" onclick="subirVideo()">📹 SUBIR VIDEO</button>
            <button class="btn" onclick="agregarVideoEjemplo()">🧪 AGREGAR EJEMPLO</button>
        </div>

        <!-- SECCIÓN 2: DEBUG -->
        <div class="section">
            <h2>🔧 DEBUG</h2>
            <button class="btn" onclick="mostrarDebug()">🔍 VER DATOS</button>
            <button class="btn" onclick="limpiarTodo()">🗑️ LIMPIAR TODO</button>
            <div id="debugInfo" class="debug-info" style="display: none;"></div>
        </div>

        <!-- SECCIÓN 3: VIDEOS -->
        <div class="section">
            <h2>📺 VIDEOS GUARDADOS</h2>
            <button class="btn" onclick="cargarVideos()">🔄 RECARGAR VIDEOS</button>
            <div id="videosList"></div>
        </div>
    </div>

    <script>
        // Función para subir video
        function subirVideo() {
            const titulo = document.getElementById('videoTitle').value;
            const categoria = document.getElementById('videoCategory').value;
            const url = document.getElementById('videoUrl').value;
            const descripcion = document.getElementById('videoDescription').value;

            if (!titulo) {
                alert('❌ Falta el título');
                return;
            }

            if (!url) {
                alert('❌ Falta la URL del video');
                return;
            }

            const video = {
                id: 'video_' + Date.now(),
                title: titulo,
                category: categoria,
                url: url,
                description: descripcion,
                date: new Date().toLocaleDateString(),
                duration: '15:30'
            };

            // Obtener videos existentes
            let videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');

            // Agregar nuevo video
            videos.push(video);

            // Guardar
            localStorage.setItem('germayori_videos', JSON.stringify(videos));

            alert(`✅ Video "${titulo}" guardado\n🆔 ID: ${video.id}\n📊 Total: ${videos.length}`);

            // Limpiar formulario
            document.getElementById('videoTitle').value = '';
            document.getElementById('videoUrl').value = '';
            document.getElementById('videoDescription').value = '';

            // Recargar lista
            cargarVideos();
        }

        // Función para agregar video de ejemplo
        function agregarVideoEjemplo() {
            const videoEjemplo = {
                id: 'ejemplo_' + Date.now(),
                title: 'Video de Ejemplo - Trading Básico',
                category: 'basico',
                url: 'https://www.youtube.com/watch?v=jNQXAC9IVRw',
                description: 'Este es un video de ejemplo para probar el sistema',
                date: new Date().toLocaleDateString(),
                duration: '3:32'
            };

            let videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');

            // Agregar varios videos de ejemplo
            const videosEjemplo = [
                {
                    id: 'ejemplo_1_' + Date.now(),
                    title: 'Introducción al Trading Forex',
                    category: 'basico',
                    url: 'https://www.youtube.com/watch?v=jNQXAC9IVRw',
                    description: 'Conceptos básicos del mercado de divisas',
                    date: new Date().toLocaleDateString(),
                    duration: '10:15'
                },
                {
                    id: 'ejemplo_2_' + Date.now(),
                    title: 'Análisis Técnico Básico',
                    category: 'basico',
                    url: 'https://www.youtube.com/watch?v=ScMzIvxBSi4',
                    description: 'Fundamentos del análisis técnico',
                    date: new Date().toLocaleDateString(),
                    duration: '15:30'
                },
                {
                    id: 'ejemplo_3_' + Date.now(),
                    title: 'Estrategias Avanzadas FVG',
                    category: 'intermedio',
                    url: 'https://www.youtube.com/watch?v=9bZkp7q19f0',
                    description: 'Fair Value Gap y Order Blocks',
                    date: new Date().toLocaleDateString(),
                    duration: '25:45'
                }
            ];

            videos.push(...videosEjemplo);
            localStorage.setItem('germayori_videos', JSON.stringify(videos));

            alert(`🧪 ${videosEjemplo.length} videos de ejemplo agregados\n📊 Total videos: ${videos.length}`);
            cargarVideos();
        }

        // Función para mostrar debug
        function mostrarDebug() {
            const videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
            const debugDiv = document.getElementById('debugInfo');

            let debugText = `📊 INFORMACIÓN DEBUG:\n\n`;
            debugText += `Total videos: ${videos.length}\n\n`;

            if (videos.length > 0) {
                debugText += `LISTA DE VIDEOS:\n`;
                videos.forEach((video, index) => {
                    debugText += `\n${index + 1}. ${video.title}\n`;
                    debugText += `   ID: ${video.id}\n`;
                    debugText += `   Categoría: ${video.category}\n`;
                    debugText += `   URL: ${video.url}\n`;
                    debugText += `   Fecha: ${video.date}\n`;
                });
            } else {
                debugText += `❌ NO HAY VIDEOS GUARDADOS`;
            }

            debugDiv.innerHTML = `<pre>${debugText}</pre>`;
            debugDiv.style.display = 'block';
        }

        // Función para cargar videos
        function cargarVideos() {
            const videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
            const videosList = document.getElementById('videosList');

            if (videos.length === 0) {
                videosList.innerHTML = '<p>❌ No hay videos guardados</p>';
                return;
            }

            videosList.innerHTML = videos.map(video => `
                <div class="video-card">
                    <div class="video-title">${video.title}</div>
                    <p>📂 Categoría: ${video.category}</p>
                    <p>📅 Fecha: ${video.date}</p>
                    <p>⏱️ Duración: ${video.duration}</p>
                    <p>🔗 URL: ${video.url}</p>
                    <button class="play-btn" onclick="reproducirVideo('${video.id}')">▶️ REPRODUCIR</button>
                </div>
            `).join('');
        }

        // Función para reproducir video
        function reproducirVideo(videoId) {
            const videos = JSON.parse(localStorage.getItem('germayori_videos') || '[]');
            const video = videos.find(v => v.id === videoId);

            if (!video) {
                alert('❌ Video no encontrado con ID: ' + videoId);
                return;
            }

            if (video.url.includes('youtube.com') || video.url.includes('youtu.be')) {
                // Abrir YouTube en nueva ventana
                window.open(video.url, '_blank');
            } else {
                // Mostrar información del video
                alert(`📹 REPRODUCIENDO:\n\nTítulo: ${video.title}\nURL: ${video.url}\n\n(Se abrirá en nueva ventana)`);
                window.open(video.url, '_blank');
            }
        }

        // Función para limpiar todo
        function limpiarTodo() {
            if (confirm('¿Eliminar TODOS los videos?')) {
                localStorage.removeItem('germayori_videos');
                alert('✅ Todos los videos eliminados');
                cargarVideos();
                document.getElementById('debugInfo').style.display = 'none';
            }
        }

        // Cargar videos al iniciar
        window.onload = function() {
            cargarVideos();
        };
    </script>
</body>
</html>
