const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true
    },
    password: {
        type: String,
        required: true
    },
    type: {
        type: String,
        enum: ['user', 'admin', 'premium', 'basic'],
        default: 'user'
    },
    avatar: {
        type: String,
        default: 'https://via.placeholder.com/40'
    },
    plan: {
        type: String,
        enum: ['basic', 'intermedio', 'avanzado', 'complete'],
        default: 'basic'
    },
    planPrice: {
        type: Number,
        default: 45
    },
    isActive: {
        type: <PERSON><PERSON>an,
        default: true
    },
    lastLogin: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true // Crea automáticamente createdAt y updatedAt
});

// Índices para optimizar búsquedas
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ type: 1 });
userSchema.index({ plan: 1 });

module.exports = mongoose.model('User', userSchema);
