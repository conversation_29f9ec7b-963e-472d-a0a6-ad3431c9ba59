<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Noticias</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="calculadora.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-calculator mr-3 text-green-400"></i>
                    <span>Calculadora</span>
                </a>

                <a href="noticias.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i>
                    <span>Noticias</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="notificaciones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-bell mr-3 text-pink-400"></i>
                    <span>Notificaciones</span>
                </a>

                <a href="alertas.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i>
                    <span>Alertas Mercado</span>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- News Content -->
        <div class="flex-1 main-content m-4 rounded-lg p-6 overflow-y-auto">

            <!-- Header -->
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-white mb-2">📰 Noticias Forex</h1>
                <p class="text-gray-200">Mantente informado con las últimas noticias y eventos económicos</p>
            </div>

            <!-- Noticias en Tiempo Real - Sistema Interno -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-bold mb-4 text-green-600">📰 Noticias Forex en Tiempo Real</h2>
                <div class="bg-gray-100 rounded-lg p-4">
                    <!-- Feed de Noticias en Vivo -->
                    <div id="news-feed" class="space-y-3 max-h-96 overflow-y-auto">
                        <div class="text-center py-8">
                            <i class="fas fa-newspaper text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">Haz clic en "Iniciar Feed" para comenzar las noticias en tiempo real</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button onclick="startNewsFeed()" id="news-btn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                        <i class="fas fa-play mr-2"></i>Iniciar Feed de Noticias
                    </button>
                    <button onclick="stopNewsFeed()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 ml-2">
                        <i class="fas fa-stop mr-2"></i>Detener
                    </button>
                    <p class="text-sm text-gray-600 mt-2">📡 Sistema de noticias integrado en GERMAYORI</p>
                </div>
            </div>

            <!-- Calendar and News Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

                <!-- Economic Calendar -->
                <div class="lg:col-span-2 bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-4 text-yellow-600">📅 Calendario Económico</h2>
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <!-- Widget de Calendario Económico -->
                        <div id="economic-calendar-widget" class="text-center">
                            <div class="mb-4">
                                <i class="fas fa-calendar-alt text-6xl text-yellow-500 mb-4"></i>
                                <h3 class="text-lg font-bold text-yellow-700">Calendario Económico</h3>
                                <p class="text-yellow-600 mb-4">Eventos económicos importantes de la semana</p>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <button onclick="loadEconomicCalendar()" class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700">
                            <i class="fas fa-calendar mr-2"></i>Ver Eventos de la Semana
                        </button>
                        <button onclick="window.open('https://www.investing.com/economic-calendar/', '_blank')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 ml-2">
                            <i class="fas fa-external-link-alt mr-2"></i>Calendario Completo
                        </button>
                    </div>
                </div>

                <!-- News Summary -->
                <div class="space-y-6">

                    <!-- Today's Events -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-bold mb-4 text-red-600">🔥 Eventos de Hoy</h3>
                        <div class="space-y-3">
                            <div class="p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <div class="font-semibold text-red-800">NFP USA</div>
                                        <div class="text-sm text-red-600">15:30 GMT</div>
                                    </div>
                                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded">ALTO</span>
                                </div>
                            </div>

                            <div class="p-3 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <div class="font-semibold text-orange-800">Decisión BCE</div>
                                        <div class="text-sm text-orange-600">12:45 GMT</div>
                                    </div>
                                    <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded">MEDIO</span>
                                </div>
                            </div>

                            <div class="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <div class="font-semibold text-yellow-800">PMI Reino Unido</div>
                                        <div class="text-sm text-yellow-600">09:30 GMT</div>
                                    </div>
                                    <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded">BAJO</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Market Impact -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-bold mb-4 text-blue-600">📊 Impacto en el Mercado</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-green-50 rounded">
                                <span class="text-green-700">EUR/USD</span>
                                <span class="font-bold text-green-700">↑ +0.25%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-red-50 rounded">
                                <span class="text-red-700">GBP/USD</span>
                                <span class="font-bold text-red-700">↓ -0.18%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-blue-50 rounded">
                                <span class="text-blue-700">USD/JPY</span>
                                <span class="font-bold text-blue-700">↑ +0.12%</span>
                            </div>
                        </div>
                    </div>

                    <!-- News Sources -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-bold mb-4 text-purple-600">📡 Fuentes de Noticias</h3>
                        <div class="space-y-3">
                            <div onclick="loadForexFactory()" class="block p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors cursor-pointer">
                                <div class="font-semibold text-purple-800">Forex Factory</div>
                                <div class="text-sm text-purple-600">Calendario y noticias integradas</div>
                            </div>

                            <a href="https://www.investing.com/economic-calendar/" target="_blank"
                               class="block p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                <div class="font-semibold text-blue-800">Investing.com</div>
                                <div class="text-sm text-blue-600">Calendario económico</div>
                            </a>

                            <a href="https://www.myfxbook.com/forex-economic-calendar" target="_blank"
                               class="block p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                                <div class="font-semibold text-yellow-800">MyFXBook</div>
                                <div class="text-sm text-yellow-600">Calendario recomendado</div>
                            </a>
                        </div>
                    </div>

                    <!-- Trading Tips -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-bold mb-4 text-gray-600">💡 Consejos para Noticias</h3>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span>Evita operar 30 min antes de noticias de alto impacto</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span>Usa stop loss más amplios durante eventos</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span>Observa la reacción del mercado, no solo el dato</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span>Mantente informado pero no sobre-operes</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = checkAuth();
            if (user) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type.toUpperCase();
                document.getElementById('user-avatar').src = user.avatar;
            }
        }

        // Inicializar
        // Sistema de Noticias en Tiempo Real
        let newsInterval;
        let newsRunning = false;
        let newsCounter = 0;

        // Base de datos de noticias simuladas
        const newsDatabase = [
            { category: 'Central Banks', title: 'FED mantiene tasas en 5.25%-5.50%', impact: 'high', currency: 'USD', source: 'Reuters' },
            { category: 'Economic Data', title: 'NFP supera expectativas con +275K empleos', impact: 'high', currency: 'USD', source: 'Bloomberg' },
            { category: 'Market Analysis', title: 'EUR/USD rompe resistencia clave en 1.0850', impact: 'medium', currency: 'EUR', source: 'MarketWatch' },
            { category: 'Geopolitics', title: 'Tensiones comerciales afectan al yen japonés', impact: 'medium', currency: 'JPY', source: 'Financial Times' },
            { category: 'Commodities', title: 'Oro alcanza máximos de 6 meses en $2,050', impact: 'medium', currency: 'XAU', source: 'CNBC' },
            { category: 'Central Banks', title: 'BCE considera pausa en subidas de tasas', impact: 'high', currency: 'EUR', source: 'ECB Press' },
            { category: 'Economic Data', title: 'Inflación UK baja al 3.2% en octubre', impact: 'medium', currency: 'GBP', source: 'ONS' },
            { category: 'Market Analysis', title: 'Bitcoin supera los $45,000 por primera vez en 2024', impact: 'low', currency: 'BTC', source: 'CoinDesk' },
            { category: 'Corporate', title: 'Apple reporta ganancias récord en Q4', impact: 'low', currency: 'USD', source: 'Apple Inc' },
            { category: 'Energy', title: 'Petróleo WTI sube 3% tras datos de inventarios', impact: 'medium', currency: 'Oil', source: 'EIA' }
        ];

        // Función para iniciar feed de noticias
        function startNewsFeed() {
            if (newsRunning) return;

            newsRunning = true;
            const btn = document.getElementById('news-btn');
            btn.innerHTML = '<i class="fas fa-broadcast-tower mr-2"></i>Feed Activo';
            btn.className = 'bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700';

            // Limpiar feed
            const feed = document.getElementById('news-feed');
            feed.innerHTML = '<div class="text-center text-blue-600 font-bold mb-4"><i class="fas fa-satellite-dish mr-2"></i>Feed de noticias iniciado...</div>';

            // Generar noticias cada 4-10 segundos
            newsInterval = setInterval(() => {
                generateRandomNews();
            }, Math.random() * 6000 + 4000);

            // Primera noticia inmediata
            setTimeout(() => generateRandomNews(), 1500);
        }

        // Función para detener feed de noticias
        function stopNewsFeed() {
            if (!newsRunning) return;

            newsRunning = false;
            clearInterval(newsInterval);

            const btn = document.getElementById('news-btn');
            btn.innerHTML = '<i class="fas fa-play mr-2"></i>Iniciar Feed de Noticias';
            btn.className = 'bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700';

            const feed = document.getElementById('news-feed');
            const stopMessage = document.createElement('div');
            stopMessage.className = 'text-center text-red-600 font-bold p-4 bg-red-50 rounded-lg border border-red-200';
            stopMessage.innerHTML = '<i class="fas fa-stop-circle mr-2"></i>Feed de noticias detenido';
            feed.insertBefore(stopMessage, feed.firstChild);
        }

        // Función para generar noticia aleatoria
        function generateRandomNews() {
            if (!newsRunning) return;

            const news = newsDatabase[Math.floor(Math.random() * newsDatabase.length)];
            const now = new Date();
            const timeStr = now.toLocaleTimeString('es-ES');

            newsCounter++;

            const impactColors = {
                'high': 'red',
                'medium': 'yellow',
                'low': 'blue'
            };

            const color = impactColors[news.impact];

            const newsElement = document.createElement('div');
            newsElement.className = `p-4 rounded-lg border-l-4 border-${color}-500 bg-${color}-50 animate-pulse`;
            newsElement.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <span class="font-bold text-${color}-800">${getCategoryIcon(news.category)} ${news.category}</span>
                            <span class="ml-2 text-xs bg-${color}-500 text-white px-2 py-1 rounded">${news.impact.toUpperCase()}</span>
                            <span class="ml-2 text-xs bg-gray-500 text-white px-2 py-1 rounded">${news.currency}</span>
                        </div>
                        <h3 class="font-semibold text-${color}-900 mb-1">${news.title}</h3>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-${color}-600">${news.source}</span>
                            <span class="text-xs text-${color}-600">${timeStr} - Noticia #${newsCounter}</span>
                        </div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="text-${color}-400 hover:text-${color}-600 ml-2">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            const feed = document.getElementById('news-feed');
            feed.insertBefore(newsElement, feed.firstChild);

            // Mantener solo las últimas 8 noticias
            while (feed.children.length > 8) {
                feed.removeChild(feed.lastChild);
            }

            // Remover animación después de 3 segundos
            setTimeout(() => {
                newsElement.classList.remove('animate-pulse');
            }, 3000);
        }

        // Función para obtener icono según categoría
        function getCategoryIcon(category) {
            const icons = {
                'Central Banks': '🏦',
                'Economic Data': '📊',
                'Market Analysis': '📈',
                'Geopolitics': '🌍',
                'Commodities': '🥇',
                'Corporate': '🏢',
                'Energy': '⚡'
            };
            return icons[category] || '📰';
        }

        // Función para mostrar calendario económico
        function loadEconomicCalendar() {
            const widget = document.getElementById('economic-calendar-widget');
            widget.innerHTML = `
                <div class="text-left">
                    <h3 class="text-lg font-bold text-yellow-700 mb-4">📅 Eventos Económicos Esta Semana</h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="font-semibold text-red-800">🇺🇸 NFP (Nóminas No Agrícolas)</span>
                                    <div class="text-sm text-red-600">Viernes 13:30 GMT</div>
                                </div>
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded">ALTO</span>
                            </div>
                        </div>
                        <div class="p-3 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="font-semibold text-orange-800">🇪🇺 Decisión BCE</span>
                                    <div class="text-sm text-orange-600">Jueves 12:45 GMT</div>
                                </div>
                                <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded">ALTO</span>
                            </div>
                        </div>
                        <div class="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="font-semibold text-yellow-800">🇺🇸 CPI (Inflación)</span>
                                    <div class="text-sm text-yellow-600">Miércoles 13:30 GMT</div>
                                </div>
                                <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded">ALTO</span>
                            </div>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="font-semibold text-blue-800">🇬🇧 PMI Reino Unido</span>
                                    <div class="text-sm text-blue-600">Martes 09:30 GMT</div>
                                </div>
                                <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded">MEDIO</span>
                            </div>
                        </div>
                        <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="font-semibold text-green-800">🇯🇵 Decisión BOJ</span>
                                    <div class="text-sm text-green-600">Martes 03:00 GMT</div>
                                </div>
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded">MEDIO</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-gray-100 rounded-lg">
                        <h4 class="font-semibold text-gray-700 mb-2">💡 Consejos para Trading con Noticias:</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Evita operar 30 min antes de eventos de alto impacto</li>
                            <li>• Usa stop loss más amplios durante noticias importantes</li>
                            <li>• Observa la reacción del mercado, no solo el dato</li>
                            <li>• Los eventos de alto impacto pueden mover 50-100 pips</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        window.onload = function() {
            loadUserInfo();
        };
    </script>
</body>
</html>
