import React from 'react';
import { motion } from 'framer-motion';
import { NavLink } from 'react-router-dom';
import {
  Home,
  TrendingUp,
  MessageCircle,
  FileText,
  Calendar,
  CreditCard,
  Settings,
  Crown,
  BarChart3,
  BookOpen,
  Target,
  Bell,
  Newspaper,
  Calculator,
  Shield
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

const Sidebar = ({ isOpen, setIsOpen }) => {
  const { user } = useAuth();

  const menuItems = [
    {
      name: 'Dashboard',
      icon: Home,
      path: '/dashboard',
      description: 'Panel principal'
    },
    {
      name: 'TradingView',
      icon: TrendingUp,
      path: '/tradingview',
      description: 'Gráficos en tiempo real',
      premium: false
    },
    {
      name: 'Chat Educativo',
      icon: MessageCircle,
      path: '/chat',
      description: 'Asistente IA',
      premium: true
    },
    {
      name: 'Aná<PERSON><PERSON> OCR',
      icon: FileText,
      path: '/analisis',
      description: 'Análisis de imágenes',
      premium: true
    },
    {
      name: 'Sesiones Trading',
      icon: Calendar,
      path: '/sesiones',
      description: 'Historial de operaciones',
      premium: true
    },
    {
      name: 'Estadísticas',
      icon: BarChart3,
      path: '/estadisticas',
      description: 'Métricas detalladas',
      premium: true
    },
    {
      name: 'Academia',
      icon: BookOpen,
      path: '/academia',
      description: 'Contenido educativo',
      premium: false
    },
    {
      name: 'Señales Educativas',
      icon: Target,
      path: '/senales',
      description: 'Ejemplos de análisis',
      premium: true
    },
    {
      name: 'Noticias',
      icon: Newspaper,
      path: '/noticias',
      description: 'Noticias del mercado',
      premium: false
    },
    {
      name: 'Alertas',
      icon: Bell,
      path: '/alertas',
      description: 'Alertas personalizadas',
      premium: true
    },
    {
      name: 'Calculadora',
      icon: Calculator,
      path: '/calculadora',
      description: 'Calcular pips y profit',
      premium: false
    },
    {
      name: 'Panel Admin',
      icon: Shield,
      path: '/admin',
      description: 'Gestionar contenido',
      premium: false,
      adminOnly: true
    }
  ];

  const bottomMenuItems = [
    {
      name: 'Planes',
      icon: CreditCard,
      path: '/planes',
      description: 'Gestionar suscripción'
    },
    {
      name: 'Configuración',
      icon: Settings,
      path: '/configuracion',
      description: 'Ajustes de cuenta'
    }
  ];

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 40
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 40
      }
    }
  };

  const MenuItem = ({ item, index }) => {
    const isLocked = item.premium && !user?.isPaid;
    const isAdminOnly = item.adminOnly && user?.email !== '<EMAIL>';

    // No mostrar items de admin si no es admin
    if (isAdminOnly) return null;

    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        <NavLink
          to={item.path}
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group relative ${
              isActive && !isLocked
                ? 'bg-danger text-white shadow-lg'
                : isLocked
                ? 'text-gray-500'
                : 'text-gray-300 hover:bg-accent hover:bg-opacity-20 hover:text-white'
            }`
          }
          onClick={(e) => {
            if (isLocked) {
              e.preventDefault();
              // Mostrar modal de paywall aquí si es necesario
            } else if (window.innerWidth < 1024) {
              setIsOpen(false);
            }
          }}
        >
          <item.icon size={20} />
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <span className="font-medium">{item.name}</span>
              {isLocked && <Crown size={16} className="text-warning" />}
              {item.adminOnly && <Shield size={16} className="text-danger" />}
            </div>
            <p className="text-xs opacity-70">{item.description}</p>
          </div>

          {isLocked && (
            <div className="absolute inset-0 bg-gray-900 bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
              <span className="text-xs text-warning font-medium">Premium</span>
            </div>
          )}
        </NavLink>
      </motion.div>
    );
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        variants={sidebarVariants}
        animate={isOpen ? "open" : "closed"}
        className="fixed left-0 top-0 h-full w-64 bg-secondary border-r border-accent z-50 lg:relative lg:translate-x-0 flex flex-col lg:sticky lg:top-0"
      >
        {/* Logo section */}
        <div className="p-6 border-b border-accent">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-danger rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">G</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gradient">GERMAYORI</h1>
              <p className="text-xs text-gray-400">Trading Educativo</p>
            </div>
          </div>
        </div>

        {/* User info */}
        <div className="p-4 border-b border-accent">
          <div className="flex items-center space-x-3">
            <img
              src={user?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'}
              alt={user?.name}
              className="w-10 h-10 rounded-full object-cover"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-text truncate">{user?.name}</p>
              <div className="flex items-center space-x-1">
                {user?.isPaid && <Crown size={12} className="text-warning" />}
                <p className="text-xs text-gray-400">{user?.plan}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {menuItems.map((item, index) => (
            <MenuItem key={item.name} item={item} index={index} />
          ))}
        </nav>

        {/* Bottom menu */}
        <div className="p-4 border-t border-accent space-y-2">
          {bottomMenuItems.map((item, index) => (
            <MenuItem key={item.name} item={item} index={index + menuItems.length} />
          ))}
        </div>

        {/* Upgrade banner for free users */}
        {!user?.isPaid && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="m-4 p-4 bg-gradient-danger rounded-lg text-center"
          >
            <Crown className="mx-auto mb-2 text-white" size={24} />
            <h3 className="text-white font-semibold text-sm mb-1">
              Desbloquea Premium
            </h3>
            <p className="text-white text-xs opacity-90 mb-3">
              Accede a todas las funciones
            </p>
            <NavLink
              to="/planes"
              className="block bg-white text-danger font-semibold text-sm py-2 px-4 rounded-lg hover:bg-gray-100 transition-colors"
              onClick={() => window.innerWidth < 1024 && setIsOpen(false)}
            >
              Ver Planes
            </NavLink>
          </motion.div>
        )}
      </motion.aside>
    </>
  );
};

export default Sidebar;
