import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Newspaper, ExternalLink, Clock, TrendingUp, AlertTriangle, Globe } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';

const Noticias = () => {
  const [selectedCategory, setSelectedCategory] = useState('todas');
  const [noticias, setNoticias] = useState([]);

  const categories = [
    { id: 'todas', name: 'Todas', icon: Globe },
    { id: 'forex', name: 'Forex', icon: TrendingUp },
    { id: 'economia', name: 'Economía', icon: Newspaper },
    { id: 'alertas', name: 'Al<PERSON><PERSON>', icon: AlertTriangle }
  ];

  // Noticias simuladas (en producción conectarías con una API real)
  const noticiasData = [
    {
      id: 1,
      title: 'Fed mantiene tasas de interés sin cambios',
      summary: 'La Reserva Federal de Estados Unidos decidió mantener las tasas de interés en el rango actual de 5.25%-5.50%.',
      category: 'economia',
      impact: 'alto',
      time: '2 horas',
      source: 'Reuters',
      url: 'https://www.reuters.com',
      currency: 'USD'
    },
    {
      id: 2,
      title: 'EUR/USD alcanza nuevos máximos semanales',
      summary: 'El par EUR/USD rompe resistencia clave en 1.0850 tras datos positivos de inflación europea.',
      category: 'forex',
      impact: 'medio',
      time: '4 horas',
      source: 'ForexLive',
      url: 'https://www.forexlive.com',
      currency: 'EUR'
    },
    {
      id: 3,
      title: 'Banco de Inglaterra señala posibles recortes',
      summary: 'El BoE sugiere que podría considerar recortes de tasas si la inflación continúa bajando.',
      category: 'economia',
      impact: 'alto',
      time: '6 horas',
      source: 'Bloomberg',
      url: 'https://www.bloomberg.com',
      currency: 'GBP'
    },
    {
      id: 4,
      title: 'Alerta: Volatilidad esperada en JPY',
      summary: 'Se espera alta volatilidad en el yen japonés debido a la reunión del Banco de Japón mañana.',
      category: 'alertas',
      impact: 'alto',
      time: '1 hora',
      source: 'FXStreet',
      url: 'https://www.fxstreet.com',
      currency: 'JPY'
    },
    {
      id: 5,
      title: 'Datos de empleo de EE.UU. superan expectativas',
      summary: 'Los datos de nóminas no agrícolas muestran 250,000 nuevos empleos, superando las expectativas de 200,000.',
      category: 'economia',
      impact: 'alto',
      time: '8 horas',
      source: 'MarketWatch',
      url: 'https://www.marketwatch.com',
      currency: 'USD'
    },
    {
      id: 6,
      title: 'AUD/USD bajo presión por datos de China',
      summary: 'El dólar australiano cae tras datos débiles de manufactura china, principal socio comercial.',
      category: 'forex',
      impact: 'medio',
      time: '12 horas',
      source: 'DailyFX',
      url: 'https://www.dailyfx.com',
      currency: 'AUD'
    }
  ];

  useEffect(() => {
    // Filtrar noticias por categoría
    if (selectedCategory === 'todas') {
      setNoticias(noticiasData);
    } else {
      setNoticias(noticiasData.filter(noticia => noticia.category === selectedCategory));
    }
  }, [selectedCategory]);

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'alto': return 'text-danger bg-danger bg-opacity-20';
      case 'medio': return 'text-warning bg-warning bg-opacity-20';
      case 'bajo': return 'text-success bg-success bg-opacity-20';
      default: return 'text-gray-400 bg-gray-400 bg-opacity-20';
    }
  };

  const getCurrencyFlag = (currency) => {
    const flags = {
      'USD': '🇺🇸',
      'EUR': '🇪🇺',
      'GBP': '🇬🇧',
      'JPY': '🇯🇵',
      'AUD': '🇦🇺',
      'CAD': '🇨🇦',
      'CHF': '🇨🇭'
    };
    return flags[currency] || '🌍';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <Newspaper className="mr-3 text-danger" size={32} />
            Noticias del Mercado
          </h1>
          <p className="text-gray-400">
            Mantente actualizado con las últimas noticias que mueven los mercados
          </p>
        </div>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Actualizar
        </Button>
      </motion.div>

      {/* Categories */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <div className="flex flex-wrap gap-4">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-danger text-white'
                      : 'bg-accent text-gray-300 hover:bg-danger hover:text-white'
                  }`}
                >
                  <Icon size={18} />
                  <span>{category.name}</span>
                </button>
              );
            })}
          </div>
        </Card>
      </motion.div>

      {/* Enlaces Externos Útiles */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <h3 className="font-semibold text-text mb-4">Recursos Externos</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a
              href="https://www.myfxbook.com/forex-economic-calendar"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-between p-3 bg-primary rounded-lg hover:bg-accent transition-colors"
            >
              <div>
                <h4 className="font-medium text-text">Calendario Económico</h4>
                <p className="text-sm text-gray-400">MyFXBook - Eventos importantes</p>
              </div>
              <ExternalLink size={16} className="text-gray-400" />
            </a>
            <a
              href="https://www.myfxbook.com/forex-calculators/profit-calculator"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-between p-3 bg-primary rounded-lg hover:bg-accent transition-colors"
            >
              <div>
                <h4 className="font-medium text-text">Calculadora de Profit</h4>
                <p className="text-sm text-gray-400">MyFXBook - Calcular ganancias</p>
              </div>
              <ExternalLink size={16} className="text-gray-400" />
            </a>
          </div>
        </Card>
      </motion.div>

      {/* News List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="space-y-4"
      >
        {noticias.map((noticia, index) => (
          <motion.div
            key={noticia.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 + index * 0.1 }}
          >
            <Card className="hover:border-danger transition-colors cursor-pointer">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getCurrencyFlag(noticia.currency)}</span>
                  <div>
                    <h3 className="font-semibold text-text mb-1">{noticia.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Clock size={14} />
                        <span>Hace {noticia.time}</span>
                      </div>
                      <span>•</span>
                      <span>{noticia.source}</span>
                      <span>•</span>
                      <span className={`px-2 py-1 rounded text-xs ${getImpactColor(noticia.impact)}`}>
                        Impacto {noticia.impact}
                      </span>
                    </div>
                  </div>
                </div>
                <a
                  href={noticia.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-danger transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ExternalLink size={18} />
                </a>
              </div>
              
              <p className="text-gray-300 text-sm leading-relaxed">
                {noticia.summary}
              </p>
              
              <div className="mt-4 flex items-center justify-between">
                <span className="text-xs text-gray-500 uppercase tracking-wide">
                  {noticia.category}
                </span>
                <Button variant="outline" size="sm">
                  Leer más
                </Button>
              </div>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Market Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card>
          <h3 className="font-semibold text-text mb-4">Estado del Mercado</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-3 h-3 bg-success rounded-full mx-auto mb-2"></div>
              <h4 className="font-medium text-text">Mercado Abierto</h4>
              <p className="text-sm text-gray-400">Londres, Nueva York</p>
            </div>
            <div className="text-center">
              <div className="w-3 h-3 bg-warning rounded-full mx-auto mb-2"></div>
              <h4 className="font-medium text-text">Volatilidad</h4>
              <p className="text-sm text-gray-400">Media-Alta</p>
            </div>
            <div className="text-center">
              <div className="w-3 h-3 bg-danger rounded-full mx-auto mb-2"></div>
              <h4 className="font-medium text-text">Próximo Evento</h4>
              <p className="text-sm text-gray-400">Fed en 2 horas</p>
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default Noticias;
