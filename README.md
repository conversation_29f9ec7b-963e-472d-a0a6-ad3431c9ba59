# 🚀 GERMAYORI - Plataforma de Trading Forex

Una aplicación web completa para trading forex con herramientas educativas, análisis en tiempo real y gestión de señales.

![GERMAYORI](https://img.shields.io/badge/GERMAYORI-Trading%20Platform-blue?style=for-the-badge&logo=chart-line)
![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=for-the-badge&logo=html5&logoColor=white)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=for-the-badge&logo=javascript&logoColor=black)
![TailwindCSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)

## ✨ Características

### 📊 **TradingView Integrado**
- Gráficos profesionales en tiempo real
- Búsqueda de símbolos (forex, criptos, índices, materias primas)
- Herramientas de análisis técnico completas
- Líneas de tendencia, indicadores y patrones

### 💬 **Chat Educativo con IA**
- Asistente especializado en trading forex
- Integración con OpenAI GPT-3.5
- Noticias de mercado en tiempo real
- Precios actualizados automáticamente

### 📰 **Noticias en Tiempo Real**
- Feed de noticias forex actualizado
- Calendario económico integrado
- Eventos de alto impacto identificados
- Consejos para trading con noticias

### 🚨 **Sistema de Alertas**
- Alertas de mercado en tiempo real
- Diferentes tipos: precios, noticias, técnicas
- Niveles de impacto (alto, medio, bajo)
- Sistema de notificaciones integrado

### 🧮 **Calculadora de Trading**
- Calculadora de pips
- Gestión de riesgo
- Cálculo de profit/loss
- Herramientas de tamaño de posición

### 📈 **Señales de Trading**
- Señales educativas para aprendizaje
- Análisis técnico detallado
- Gestión de riesgo incluida

## 🛠️ Tecnologías

- **Frontend:** HTML5, CSS3, JavaScript (ES6+)
- **Styling:** Tailwind CSS
- **Charts:** TradingView Widgets
- **AI:** OpenAI GPT-3.5 Turbo
- **Icons:** Font Awesome
- **Server:** Node.js (desarrollo)

## 🚀 Instalación y Uso

### Opción 1: Servidor Simple (Recomendado)
```bash
# Clonar el repositorio
git clone https://github.com/jhon0608/GERMAYORI.git
cd GERMAYORI

# Iniciar servidor con Node.js
node server.cjs
```

### Opción 2: Servidor Python
```bash
# Clonar el repositorio
git clone https://github.com/jhon0608/GERMAYORI.git
cd GERMAYORI

# Iniciar servidor con Python
python -m http.server 8000
```

## 🔐 Credenciales de Acceso

**Usuario de prueba:**
- **Email:** `<EMAIL>`
- **Contraseña:** `123456`

O crear una cuenta nueva desde la página de registro.

## 📱 Estructura del Proyecto

```
GERMAYORI/
├── index.html              # Página de login/registro
├── dashboard-simple.html   # Dashboard principal
├── chat.html              # Chat educativo con IA
├── calculadora.html       # Calculadora de trading
├── noticias.html          # Noticias y calendario
├── senales.html           # Señales de trading
├── notificaciones.html    # Centro de notificaciones
├── alertas.html           # Alertas de mercado
├── tradingview.html       # Gráficos TradingView
├── server.cjs             # Servidor Node.js
└── README.md              # Este archivo
```

## 🎯 Características Principales

### 🔄 **Sistema de Navegación**
- Sidebar con todos los canales
- Navegación fluida entre secciones
- Diseño responsive para móviles

### 🎨 **Diseño Profesional**
- Tema azul gradiente
- Efectos glassmorphism
- Animaciones suaves
- Interfaz moderna y limpia

### 📊 **Datos en Tiempo Real**
- Precios de divisas actualizados
- Noticias de mercado en vivo
- Alertas automáticas
- Calendario económico

## 🔧 Configuración

### OpenAI API Key
Para usar el chat educativo, configura tu API key de OpenAI en `chat.html`

## 🌐 Demo en Vivo

🔗 **[Ver Demo](https://jhon0608.github.io/GERMAYORI)**

## 📸 Screenshots

### Dashboard Principal
![Dashboard](https://via.placeholder.com/800x400/1e3c72/ffffff?text=GERMAYORI+Dashboard)

### Chat Educativo
![Chat](https://via.placeholder.com/800x400/2a5298/ffffff?text=Chat+Educativo+con+IA)

### TradingView
![TradingView](https://via.placeholder.com/800x400/0f3460/ffffff?text=TradingView+Integrado)

## 👨‍💻 Autor

**jhon0608**
- GitHub: [@jhon0608](https://github.com/jhon0608)

## 📄 Licencia

Este proyecto está bajo la Licencia MIT.

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

---

⭐ **¡Dale una estrella al proyecto si te gusta!** ⭐
