import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Bell, Crown, Plus, TrendingUp, TrendingDown, Target, Clock, Settings } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import PaywallModal from '../components/Paywall/PaywallModal';

const Alertas = () => {
  const { user } = useAuth();
  const [showPaywall, setShowPaywall] = useState(!user?.isPaid);
  const [activeTab, setActiveTab] = useState('activas');

  const alertasActivas = [
    {
      id: 1,
      pair: 'EUR/USD',
      type: 'price',
      condition: 'above',
      value: 1.0850,
      currentPrice: 1.0845,
      status: 'active',
      created: '2024-01-15 10:30'
    },
    {
      id: 2,
      pair: 'GBP/JPY',
      type: 'rsi',
      condition: 'below',
      value: 30,
      currentValue: 35,
      status: 'active',
      created: '2024-01-15 09:15'
    },
    {
      id: 3,
      pair: 'USD/CAD',
      type: 'price',
      condition: 'below',
      value: 1.3500,
      currentPrice: 1.3567,
      status: 'triggered',
      triggered: '2024-01-15 11:45'
    }
  ];

  const tiposAlerta = [
    { id: 'price', name: 'Precio', icon: Target, description: 'Alerta cuando el precio alcance un nivel' },
    { id: 'rsi', name: 'RSI', icon: TrendingUp, description: 'Alerta basada en el indicador RSI' },
    { id: 'volume', name: 'Volumen', icon: TrendingDown, description: 'Alerta por cambios en volumen' },
    { id: 'news', name: 'Noticias', icon: Bell, description: 'Alerta por eventos económicos importantes' }
  ];

  if (!user?.isPaid) {
    return (
      <>
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="w-24 h-24 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="text-warning" size={48} />
            </div>
            <h1 className="text-3xl font-bold text-text mb-4">Sistema de Alertas Premium</h1>
            <p className="text-gray-400 max-w-2xl mx-auto mb-8">
              Configura alertas personalizadas para precios, indicadores técnicos y eventos económicos. 
              Nunca te pierdas una oportunidad importante del mercado.
            </p>
            <Button variant="primary" onClick={() => setShowPaywall(true)}>
              Desbloquear Alertas
            </Button>
          </motion.div>
        </div>
        <PaywallModal 
          isOpen={showPaywall} 
          onClose={() => setShowPaywall(false)} 
          feature="Sistema de Alertas"
        />
      </>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <Bell className="mr-3 text-danger" size={32} />
            Sistema de Alertas
            <Crown className="ml-2 text-warning" size={24} />
          </h1>
          <p className="text-gray-400">
            Configura alertas personalizadas para el mercado
          </p>
        </div>
        <Button variant="primary">
          <Plus size={16} className="mr-2" />
          Nueva Alerta
        </Button>
      </motion.div>

      {/* Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <div className="flex space-x-4">
            <button
              onClick={() => setActiveTab('activas')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'activas'
                  ? 'bg-danger text-white'
                  : 'text-gray-400 hover:text-white hover:bg-accent'
              }`}
            >
              Alertas Activas ({alertasActivas.filter(a => a.status === 'active').length})
            </button>
            <button
              onClick={() => setActiveTab('historial')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'historial'
                  ? 'bg-danger text-white'
                  : 'text-gray-400 hover:text-white hover:bg-accent'
              }`}
            >
              Historial
            </button>
            <button
              onClick={() => setActiveTab('configurar')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'configurar'
                  ? 'bg-danger text-white'
                  : 'text-gray-400 hover:text-white hover:bg-accent'
              }`}
            >
              Configurar
            </button>
          </div>
        </Card>
      </motion.div>

      {/* Content */}
      {activeTab === 'activas' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-4"
        >
          {alertasActivas.filter(alerta => alerta.status === 'active').map((alerta, index) => (
            <motion.div
              key={alerta.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <Card className="border-accent">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-warning bg-opacity-20 rounded-lg flex items-center justify-center">
                      <Bell className="text-warning" size={24} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-text">{alerta.pair}</h3>
                      <p className="text-sm text-gray-400">
                        {alerta.type === 'price' ? 'Precio' : 'RSI'} {alerta.condition === 'above' ? 'por encima de' : 'por debajo de'} {alerta.value}
                      </p>
                      <p className="text-xs text-gray-500">
                        Creada: {alerta.created}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-400">Valor actual</p>
                    <p className="font-semibold text-text">
                      {alerta.type === 'price' ? alerta.currentPrice : alerta.currentValue}
                    </p>
                    <div className="flex space-x-2 mt-2">
                      <Button variant="outline" size="sm">
                        <Settings size={14} />
                      </Button>
                      <Button variant="outline" size="sm">
                        Eliminar
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}

      {activeTab === 'historial' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-4"
        >
          {alertasActivas.filter(alerta => alerta.status === 'triggered').map((alerta, index) => (
            <motion.div
              key={alerta.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <Card className="border-success bg-success bg-opacity-5">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-success bg-opacity-20 rounded-lg flex items-center justify-center">
                      <Bell className="text-success" size={24} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-text">{alerta.pair}</h3>
                      <p className="text-sm text-gray-400">
                        Alerta activada - {alerta.type === 'price' ? 'Precio' : 'RSI'} {alerta.condition === 'above' ? 'superó' : 'bajó de'} {alerta.value}
                      </p>
                      <p className="text-xs text-success">
                        Activada: {alerta.triggered}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="px-3 py-1 bg-success text-white rounded-full text-sm">
                      Activada
                    </span>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}

      {activeTab === 'configurar' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <h3 className="text-lg font-semibold text-text mb-6">Tipos de Alertas Disponibles</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {tiposAlerta.map((tipo, index) => {
                const Icon = tipo.icon;
                return (
                  <motion.div
                    key={tipo.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    className="p-4 border border-accent rounded-lg hover:border-danger transition-colors cursor-pointer"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="w-10 h-10 bg-accent bg-opacity-20 rounded-lg flex items-center justify-center">
                        <Icon className="text-accent" size={20} />
                      </div>
                      <div>
                        <h4 className="font-semibold text-text mb-1">{tipo.name}</h4>
                        <p className="text-sm text-gray-400">{tipo.description}</p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </Card>
        </motion.div>
      )}

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <h3 className="text-lg font-semibold text-text mb-4">Estadísticas de Alertas</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-warning bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Bell className="text-warning" size={24} />
              </div>
              <h4 className="text-2xl font-bold text-text mb-1">
                {alertasActivas.filter(a => a.status === 'active').length}
              </h4>
              <p className="text-gray-400 text-sm">Alertas Activas</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-success bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Target className="text-success" size={24} />
              </div>
              <h4 className="text-2xl font-bold text-text mb-1">
                {alertasActivas.filter(a => a.status === 'triggered').length}
              </h4>
              <p className="text-gray-400 text-sm">Alertas Activadas</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-accent bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Clock className="text-accent" size={24} />
              </div>
              <h4 className="text-2xl font-bold text-text mb-1">95%</h4>
              <p className="text-gray-400 text-sm">Precisión</p>
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default Alertas;
