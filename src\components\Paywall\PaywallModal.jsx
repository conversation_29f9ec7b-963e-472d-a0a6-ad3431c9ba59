import React from 'react';
import { motion } from 'framer-motion';
import { X, Crown, Check, Star } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import Button from '../UI/Button';
import Card from '../UI/Card';

const PaywallModal = ({ isOpen, onClose, feature }) => {
  const { upgradeToPaid } = useAuth();

  const plans = [
    {
      name: 'Gratuito',
      price: '$0',
      period: '/mes',
      features: [
        'Acceso al logo GERMAYORI',
        'Registro de cuenta',
        'TradingView básico',
        'Academia limitada'
      ],
      current: true,
      buttonText: 'Plan Actual',
      buttonVariant: 'outline'
    },
    {
      name: 'Premium',
      price: '$29',
      period: '/mes',
      features: [
        'Todo lo del plan gratuito',
        'Chat educativo con IA',
        'Análisis OCR avanzado',
        'Sesiones de trading',
        'Estadísticas detalladas',
        'Soporte prioritario',
        'Academia completa',
        'Alertas personalizadas'
      ],
      popular: true,
      buttonText: 'Actualizar Ahora',
      buttonVariant: 'primary'
    },
    {
      name: 'Pro',
      price: '$59',
      period: '/mes',
      features: [
        'Todo lo del plan Premium',
        'Análisis técnico avanzado',
        'Señales de trading',
        'Mentorías 1:1',
        'Acceso a webinars',
        'Herramientas exclusivas',
        'API personalizada'
      ],
      buttonText: 'Próximamente',
      buttonVariant: 'secondary',
      disabled: true
    }
  ];

  const handleUpgrade = () => {
    // Simular proceso de pago
    setTimeout(() => {
      upgradeToPaid();
      onClose();
    }, 1000);
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-secondary rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="p-6 border-b border-accent flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Crown className="text-warning" size={24} />
            <div>
              <h2 className="text-xl font-bold text-text">Desbloquea {feature}</h2>
              <p className="text-gray-400 text-sm">Actualiza a Premium para acceder a esta función</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Feature highlight */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              className="w-16 h-16 bg-gradient-danger rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <Crown className="text-white" size={32} />
            </motion.div>
            <h3 className="text-2xl font-bold text-text mb-2">
              Función Premium Requerida
            </h3>
            <p className="text-gray-400 max-w-md mx-auto">
              Esta función está disponible solo para usuarios Premium. 
              Actualiza tu plan para desbloquear todas las herramientas de trading educativo.
            </p>
          </div>

          {/* Plans */}
          <div className="grid md:grid-cols-3 gap-6">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative"
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-danger text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                      <Star size={14} />
                      <span>Más Popular</span>
                    </div>
                  </div>
                )}
                
                <Card className={`h-full ${plan.popular ? 'border-danger shadow-germayori' : ''}`}>
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-text mb-2">{plan.name}</h3>
                    <div className="flex items-baseline justify-center">
                      <span className="text-3xl font-bold text-text">{plan.price}</span>
                      <span className="text-gray-400 ml-1">{plan.period}</span>
                    </div>
                  </div>

                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start space-x-2">
                        <Check className="text-success mt-0.5 flex-shrink-0" size={16} />
                        <span className="text-gray-300 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    variant={plan.buttonVariant}
                    className="w-full"
                    disabled={plan.disabled || plan.current}
                    onClick={plan.name === 'Premium' ? handleUpgrade : undefined}
                  >
                    {plan.buttonText}
                  </Button>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Benefits */}
          <div className="mt-8 text-center">
            <h4 className="text-lg font-semibold text-text mb-4">
              ¿Por qué elegir Premium?
            </h4>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-success bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Crown className="text-success" size={24} />
                </div>
                <h5 className="font-medium text-text mb-1">Acceso Completo</h5>
                <p className="text-sm text-gray-400">Todas las funciones desbloqueadas</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-warning bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Star className="text-warning" size={24} />
                </div>
                <h5 className="font-medium text-text mb-1">Soporte Prioritario</h5>
                <p className="text-sm text-gray-400">Ayuda cuando la necesites</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-danger bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Check className="text-danger" size={24} />
                </div>
                <h5 className="font-medium text-text mb-1">Sin Compromisos</h5>
                <p className="text-sm text-gray-400">Cancela cuando quieras</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PaywallModal;
