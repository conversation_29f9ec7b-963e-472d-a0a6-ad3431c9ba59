<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Registro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.1.24.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">

    <div class="max-w-md w-full">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">🚀 GERMAYORI</h1>
            <p class="text-white/80">Registro Automático</p>
        </div>

        <!-- Registration Form -->
        <div class="glass rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-white mb-6 text-center">📝 Crear Cuenta</h2>
            
            <form id="registration-form" class="space-y-4">
                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Nombre Completo</label>
                    <input type="text" id="fullName" placeholder="Juan Pérez" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Email</label>
                    <input type="email" id="email" placeholder="<EMAIL>" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Contraseña</label>
                    <input type="password" id="password" placeholder="••••••••" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Teléfono</label>
                    <input type="tel" id="phone" placeholder="+507 6000-0000" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" required>
                </div>

                <div>
                    <label class="block text-white/80 text-sm font-medium mb-2">Plan</label>
                    <select id="plan" class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400">
                        <option value="basico">Básico - $25/mes</option>
                        <option value="intermedio">Intermedio - $50/mes</option>
                        <option value="avanzado">Avanzado - $75/mes</option>
                        <option value="completo">Completo - $140/mes</option>
                    </select>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="terms" class="mr-2" required>
                    <label for="terms" class="text-white/80 text-sm">Acepto los términos y condiciones</label>
                </div>

                <button type="submit" id="register-btn" class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-bold py-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-all">
                    <i class="fas fa-user-plus mr-2"></i>REGISTRARSE AHORA
                </button>
            </form>

            <div class="text-center mt-6">
                <p class="text-white/60">¿Ya tienes cuenta?</p>
                <a href="index.html" class="text-cyan-400 hover:text-cyan-300">Iniciar Sesión</a>
            </div>
        </div>

        <!-- Success Message -->
        <div id="success-message" class="hidden mt-6 glass rounded-lg p-4 text-center">
            <i class="fas fa-check-circle text-green-400 text-3xl mb-2"></i>
            <h3 class="text-white font-bold">¡Registro Exitoso!</h3>
            <p class="text-white/80">Tu cuenta ha sido creada automáticamente</p>
        </div>
    </div>

    <script>
        // Configuración AWS
        AWS.config.update({
            region: 'us-east-1',
            accessKeyId: 'AKIAZI6IXQHQHQHQHQHQ',
            secretAccessKey: 'tu-secret-key-aqui'
        });

        const dynamodb = new AWS.DynamoDB.DocumentClient();

        // Función para registrar usuario automáticamente
        document.getElementById('registration-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const registerBtn = document.getElementById('register-btn');
            registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Registrando...';
            registerBtn.disabled = true;

            try {
                const userData = {
                    id: 'user_' + Date.now(),
                    fullName: document.getElementById('fullName').value,
                    email: document.getElementById('email').value,
                    password: document.getElementById('password').value, // En producción, hashear
                    phone: document.getElementById('phone').value,
                    plan: document.getElementById('plan').value,
                    registrationDate: new Date().toISOString(),
                    status: 'active',
                    avatar: 'https://ui-avatars.com/api/?name=' + encodeURIComponent(document.getElementById('fullName').value)
                };

                // Guardar en AWS DynamoDB
                const params = {
                    TableName: 'germayori-users',
                    Item: userData
                };

                await dynamodb.put(params).promise();

                // También guardar en localStorage para acceso inmediato
                const localUser = {
                    id: userData.id,
                    name: userData.fullName,
                    email: userData.email,
                    type: userData.plan,
                    avatar: userData.avatar
                };
                localStorage.setItem('germayori_user', JSON.stringify(localUser));

                // Mostrar mensaje de éxito
                document.getElementById('registration-form').classList.add('hidden');
                document.getElementById('success-message').classList.remove('hidden');

                // Redirigir después de 3 segundos
                setTimeout(() => {
                    window.location.href = 'dashboard-simple.html';
                }, 3000);

            } catch (error) {
                console.error('Error registrando usuario:', error);
                
                // Si falla AWS, guardar solo en localStorage
                const localUser = {
                    id: 'user_' + Date.now(),
                    name: document.getElementById('fullName').value,
                    email: document.getElementById('email').value,
                    type: document.getElementById('plan').value,
                    avatar: 'https://ui-avatars.com/api/?name=' + encodeURIComponent(document.getElementById('fullName').value)
                };
                localStorage.setItem('germayori_user', JSON.stringify(localUser));

                // Mostrar mensaje de éxito
                document.getElementById('registration-form').classList.add('hidden');
                document.getElementById('success-message').classList.remove('hidden');

                setTimeout(() => {
                    window.location.href = 'dashboard-simple.html';
                }, 3000);
            }

            registerBtn.innerHTML = '<i class="fas fa-user-plus mr-2"></i>REGISTRARSE AHORA';
            registerBtn.disabled = false;
        });
    </script>
</body>
</html>
