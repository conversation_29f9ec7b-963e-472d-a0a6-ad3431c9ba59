import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  Calendar,
  Clock,
  Target,
  Award,
  Users,
  BookOpen,
  MessageCircle,
  Crown,
  Calculator
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';

const Dashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalTrades: 156,
    winRate: 68.5,
    profit: 2450.75,
    todayPnL: 125.30
  });

  // Datos simulados para los gráficos
  const chartData = [
    { name: '<PERSON><PERSON>', profit: 400, trades: 24 },
    { name: 'Feb', profit: 300, trades: 18 },
    { name: '<PERSON>', profit: 600, trades: 32 },
    { name: 'Abr', profit: 800, trades: 28 },
    { name: 'May', profit: 500, trades: 22 },
    { name: 'Jun', profit: 900, trades: 35 },
  ];

  const recentTrades = [
    { id: 1, pair: 'EUR/USD', type: 'BUY', profit: 45.20, time: '10:30', status: 'win' },
    { id: 2, pair: 'GBP/JPY', type: 'SELL', profit: -23.50, time: '09:15', status: 'loss' },
    { id: 3, pair: 'USD/CAD', type: 'BUY', profit: 67.80, time: '08:45', status: 'win' },
    { id: 4, pair: 'AUD/USD', type: 'SELL', profit: 34.10, time: '08:20', status: 'win' },
  ];

  const quickActions = [
    {
      title: 'Chat Educativo',
      description: 'Pregunta sobre estrategias',
      icon: MessageCircle,
      color: 'bg-blue-500',
      path: '/chat',
      premium: true
    },
    {
      title: 'TradingView',
      description: 'Analizar gráficos',
      icon: BarChart3,
      color: 'bg-green-500',
      path: '/tradingview',
      premium: false
    },
    {
      title: 'Academia',
      description: 'Aprender trading',
      icon: BookOpen,
      color: 'bg-purple-500',
      path: '/academia',
      premium: false
    },
    {
      title: 'Calculadora',
      description: 'Calcular pips',
      icon: Calculator,
      color: 'bg-green-500',
      path: '/calculadora',
      premium: false
    },
    {
      title: 'Panel Admin',
      description: 'Gestionar contenido',
      icon: Settings,
      color: 'bg-red-500',
      path: '/admin',
      premium: false,
      adminOnly: true
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2">
            ¡Bienvenido, {user?.name}! 👋
          </h1>
          <p className="text-gray-400">
            Aquí tienes un resumen de tu actividad de trading
          </p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          {user?.isPaid && (
            <div className="flex items-center space-x-1 bg-warning bg-opacity-20 text-warning px-3 py-1 rounded-full text-sm">
              <Crown size={16} />
              <span>Premium</span>
            </div>
          )}
          <Button variant="primary" size="sm">
            Nueva Sesión
          </Button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-success bg-opacity-20 rounded-lg mx-auto mb-3">
              <DollarSign className="text-success" size={24} />
            </div>
            <h3 className="text-2xl font-bold text-text mb-1">
              ${stats.profit.toLocaleString()}
            </h3>
            <p className="text-gray-400 text-sm">Ganancia Total</p>
            <div className="flex items-center justify-center mt-2 text-success text-sm">
              <TrendingUp size={16} className="mr-1" />
              +12.5% este mes
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-warning bg-opacity-20 rounded-lg mx-auto mb-3">
              <Target className="text-warning" size={24} />
            </div>
            <h3 className="text-2xl font-bold text-text mb-1">
              {stats.winRate}%
            </h3>
            <p className="text-gray-400 text-sm">Tasa de Éxito</p>
            <div className="flex items-center justify-center mt-2 text-success text-sm">
              <TrendingUp size={16} className="mr-1" />
              +3.2% vs mes anterior
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-danger bg-opacity-20 rounded-lg mx-auto mb-3">
              <BarChart3 className="text-danger" size={24} />
            </div>
            <h3 className="text-2xl font-bold text-text mb-1">
              {stats.totalTrades}
            </h3>
            <p className="text-gray-400 text-sm">Total Operaciones</p>
            <div className="flex items-center justify-center mt-2 text-success text-sm">
              <TrendingUp size={16} className="mr-1" />
              +8 esta semana
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-accent bg-opacity-20 rounded-lg mx-auto mb-3">
              <Clock className="text-accent" size={24} />
            </div>
            <h3 className="text-2xl font-bold text-text mb-1">
              ${stats.todayPnL > 0 ? '+' : ''}{stats.todayPnL}
            </h3>
            <p className="text-gray-400 text-sm">P&L Hoy</p>
            <div className={`flex items-center justify-center mt-2 text-sm ${stats.todayPnL > 0 ? 'text-success' : 'text-danger'}`}>
              {stats.todayPnL > 0 ? <TrendingUp size={16} className="mr-1" /> : <TrendingDown size={16} className="mr-1" />}
              {stats.todayPnL > 0 ? 'Ganando' : 'Perdiendo'} hoy
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profit Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-text">Evolución de Ganancias</h3>
              <Button variant="outline" size="sm">
                Ver Detalles
              </Button>
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="name" stroke="#9CA3AF" />
                <YAxis stroke="#9CA3AF" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#16213e',
                    border: '1px solid #0f3460',
                    borderRadius: '8px'
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="profit"
                  stroke="#e94560"
                  fill="#e94560"
                  fillOpacity={0.2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </motion.div>

        {/* Recent Trades */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-text">Operaciones Recientes</h3>
              <Button variant="outline" size="sm">
                Ver Todas
              </Button>
            </div>
            <div className="space-y-4">
              {recentTrades.map((trade) => (
                <div key={trade.id} className="flex items-center justify-between p-3 bg-primary rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${trade.status === 'win' ? 'bg-success' : 'bg-danger'}`}></div>
                    <div>
                      <p className="font-medium text-text">{trade.pair}</p>
                      <p className="text-sm text-gray-400">{trade.type} • {trade.time}</p>
                    </div>
                  </div>
                  <div className={`text-right ${trade.profit > 0 ? 'text-success' : 'text-danger'}`}>
                    <p className="font-semibold">
                      {trade.profit > 0 ? '+' : ''}${trade.profit}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <Card>
          <h3 className="text-lg font-semibold text-text mb-6">Acciones Rápidas</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => {
              const isLocked = action.premium && !user?.isPaid;
              const isAdminOnly = action.adminOnly && user?.email !== '<EMAIL>';

              // No mostrar items de admin si no es admin
              if (isAdminOnly) return null;

              return (
                <motion.div
                  key={action.title}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8 + index * 0.1 }}
                  className={`relative group ${isLocked ? 'opacity-60' : ''}`}
                >
                  <div
                    className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${
                      isLocked
                        ? 'border-gray-600 bg-gray-800'
                        : 'border-accent bg-secondary hover:border-danger hover:shadow-lg'
                    }`}
                    onClick={() => {
                      if (!isLocked) {
                        navigate(action.path);
                      }
                    }}
                  >
                    <div className={`w-12 h-12 ${action.color} bg-opacity-20 rounded-lg flex items-center justify-center mb-3`}>
                      <action.icon className={isLocked ? 'text-gray-500' : 'text-white'} size={24} />
                    </div>
                    <h4 className={`font-semibold mb-1 ${isLocked ? 'text-gray-500' : 'text-text'}`}>
                      {action.title}
                    </h4>
                    <p className={`text-sm ${isLocked ? 'text-gray-600' : 'text-gray-400'}`}>
                      {action.description}
                    </p>
                    {isLocked && (
                      <div className="absolute top-2 right-2">
                        <Crown className="text-warning" size={16} />
                      </div>
                    )}
                  </div>
                  {isLocked && (
                    <div className="absolute inset-0 bg-gray-900 bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <span className="text-warning font-medium text-sm">Premium</span>
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </Card>
      </motion.div>

      {/* Market Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Card>
          <h3 className="text-lg font-semibold text-text mb-6">Resumen del Mercado</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-success bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="text-success" size={32} />
              </div>
              <h4 className="text-xl font-bold text-text mb-1">Alcista</h4>
              <p className="text-gray-400 text-sm">Tendencia del mercado</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                <BarChart3 className="text-warning" size={32} />
              </div>
              <h4 className="text-xl font-bold text-text mb-1">Moderada</h4>
              <p className="text-gray-400 text-sm">Volatilidad</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-danger bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="text-danger" size={32} />
              </div>
              <h4 className="text-xl font-bold text-text mb-1">1,247</h4>
              <p className="text-gray-400 text-sm">Traders activos</p>
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default Dashboard;
