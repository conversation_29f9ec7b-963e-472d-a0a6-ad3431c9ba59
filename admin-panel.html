<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Panel de Administración</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #fff;
        }

        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .admin-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .admin-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 10px;
            flex-wrap: wrap;
        }

        .tab-button {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 25px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }

        .tab-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .tab-button.active {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #ccc;
            font-size: 0.9rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .data-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
            color: #FFD700;
        }

        .data-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            font-weight: bold;
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: #fff;
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: #fff;
        }

        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #3498db);
            color: #fff;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
            color: #FFD700;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: #28a745;
            color: #fff;
        }

        .status-inactive {
            background: #6c757d;
            color: #fff;
        }

        .status-pending {
            background: #ffc107;
            color: #000;
        }

        .search-box {
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .search-box::placeholder {
            color: #ccc;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: bold;
            z-index: 1000;
        }

        .connection-online {
            background: #28a745;
            color: #fff;
        }

        .connection-offline {
            background: #dc3545;
            color: #fff;
        }

        @media (max-width: 768px) {
            .admin-container {
                padding: 10px;
            }

            .admin-header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .data-table {
                font-size: 0.8rem;
            }

            .data-table th,
            .data-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <i class="fas fa-circle"></i> Conectando...
    </div>

    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-crown"></i> GERMAYORI Admin Panel</h1>
            <p>Panel de administración y gestión de datos</p>
        </div>

        <div class="admin-tabs">
            <button class="tab-button active" onclick="showTab('signals')">
                <i class="fas fa-chart-line"></i> Gestión Señales
            </button>
            <button class="tab-button" onclick="showTab('videos')">
                <i class="fas fa-video"></i> Gestión Videos
            </button>
        </div>

        <!-- Tab de Señales -->
        <div id="signals" class="tab-content active">
            <h2><i class="fas fa-chart-line"></i> Gestión de Señales GERMAYORI</h2>

            <!-- Formulario para crear señales -->
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3>Crear Nueva Señal</h3>
                <form id="signalForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Par de Divisas:</label>
                        <select id="signalPair" style="width: 100%; padding: 8px; border-radius: 5px; border: none;">
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="USDCAD">USD/CAD</option>
                            <option value="XAUUSD">XAU/USD (Gold)</option>
                            <option value="NAS100">NAS100</option>
                            <option value="US30">US30</option>
                            <option value="SPX500">SPX500</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Tipo:</label>
                        <select id="signalType" style="width: 100%; padding: 8px; border-radius: 5px; border: none;">
                            <option value="BUY">BUY</option>
                            <option value="SELL">SELL</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Precio Entrada:</label>
                        <input type="number" id="signalEntry" step="0.00001" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="1.08500">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Stop Loss:</label>
                        <input type="number" id="signalSL" step="0.00001" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="1.08000">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Take Profit:</label>
                        <input type="number" id="signalTP" step="0.00001" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="1.09000">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Timeframe:</label>
                        <select id="signalTimeframe" style="width: 100%; padding: 8px; border-radius: 5px; border: none;">
                            <option value="M1">1 Minuto</option>
                            <option value="M5">5 Minutos</option>
                            <option value="M15">15 Minutos</option>
                            <option value="M30">30 Minutos</option>
                            <option value="H1">1 Hora</option>
                            <option value="H4">4 Horas</option>
                            <option value="D1">Diario</option>
                        </select>
                    </div>
                    <div style="grid-column: 1 / -1;">
                        <label style="display: block; margin-bottom: 5px;">Análisis/Descripción:</label>
                        <textarea id="signalAnalysis" rows="3" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="Descripción del análisis y razón de la señal..."></textarea>
                    </div>
                    <div style="grid-column: 1 / -1;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus"></i> Crear Señal
                        </button>
                    </div>
                </form>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshSignals()">
                    <i class="fas fa-sync-alt"></i> Actualizar Señales
                </button>
                <button class="btn btn-danger" onclick="clearAllSignals()">
                    <i class="fas fa-trash"></i> Limpiar Todas
                </button>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-chart-line"></i>
                    <div class="stat-number" id="totalSignals">0</div>
                    <div class="stat-label">Total Señales</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-check-circle"></i>
                    <div class="stat-number" id="activeSignals">0</div>
                    <div class="stat-label">Señales Activas</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-trophy"></i>
                    <div class="stat-number" id="profitableSignals">0</div>
                    <div class="stat-label">Rentables</div>
                </div>
            </div>

            <div id="signalsLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando señales...
            </div>

            <table class="data-table" id="signalsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Par</th>
                        <th>Tipo</th>
                        <th>Precio Entrada</th>
                        <th>Stop Loss</th>
                        <th>Take Profit</th>
                        <th>Timeframe</th>
                        <th>Estado</th>
                        <th>Pips</th>
                        <th>Fecha</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody id="signalsTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Videos -->
        <div id="videos" class="tab-content">
            <h2><i class="fas fa-video"></i> Gestión de Videos GERMAYORI</h2>

            <!-- Formulario para subir videos -->
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3>Subir Nuevo Video</h3>
                <form id="videoForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Título del Video:</label>
                        <input type="text" id="videoTitle" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="Estrategia FVG Avanzada">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Categoría:</label>
                        <select id="videoCategory" style="width: 100%; padding: 8px; border-radius: 5px; border: none;">
                            <option value="basico">Videos Básicos</option>
                            <option value="intermedio">Videos Intermedios</option>
                            <option value="avanzado">Videos Avanzados</option>
                            <option value="educativo">Videos Educativos</option>
                            <option value="marketing">Marketing</option>
                            <option value="zoom">Zoom Sessions</option>
                        </select>
                    </div>
                    <div style="grid-column: 1 / -1;">
                        <label style="display: block; margin-bottom: 5px;">Descripción:</label>
                        <textarea id="videoDescription" rows="3" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="Descripción del contenido del video..."></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">URL del Video:</label>
                        <input type="url" id="videoUrl" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="https://youtube.com/watch?v=...">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px;">Miniatura (URL):</label>
                        <input type="url" id="videoThumbnail" style="width: 100%; padding: 8px; border-radius: 5px; border: none;" placeholder="https://img.youtube.com/vi/...">
                    </div>
                    <div style="grid-column: 1 / -1;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload"></i> Subir Video
                        </button>
                    </div>
                </form>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshVideos()">
                    <i class="fas fa-sync-alt"></i> Actualizar Videos
                </button>
                <button class="btn btn-info" onclick="importYouTubePlaylist()">
                    <i class="fab fa-youtube"></i> Importar Playlist
                </button>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-video"></i>
                    <div class="stat-number" id="totalVideos">0</div>
                    <div class="stat-label">Total Videos</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-eye"></i>
                    <div class="stat-number" id="totalViews">0</div>
                    <div class="stat-label">Total Visualizaciones</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-clock"></i>
                    <div class="stat-number" id="totalDuration">0</div>
                    <div class="stat-label">Duración Total (min)</div>
                </div>
            </div>

            <div id="videosLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando videos...
            </div>

            <div id="videosGrid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                <!-- Videos will be loaded here -->
            </div>
        </div>

        <!-- Tab de Usuarios -->
        <div id="users" class="tab-content">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshUsers()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestUser()">
                    <i class="fas fa-user-plus"></i> Agregar Usuario de Prueba
                </button>
                <button class="btn btn-info" onclick="exportUsers()">
                    <i class="fas fa-download"></i> Exportar
                </button>
            </div>

            <input type="text" class="search-box" placeholder="Buscar usuarios..." id="userSearch" onkeyup="filterUsers()">

            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-users"></i>
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">Total Usuarios</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-user-check"></i>
                    <div class="stat-number" id="activeUsers">0</div>
                    <div class="stat-label">Usuarios Activos</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-user-clock"></i>
                    <div class="stat-number" id="newUsers">0</div>
                    <div class="stat-label">Nuevos (7 días)</div>
                </div>
            </div>

            <div id="usersLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando usuarios...
            </div>

            <table class="data-table" id="usersTable" style="display: none;">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Email</th>
                        <th>Tipo</th>
                        <th>Estado</th>
                        <th>Fecha Registro</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Señales -->
        <div id="signals" class="tab-content">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshSignals()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestSignal()">
                    <i class="fas fa-plus"></i> Agregar Señal de Prueba
                </button>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-chart-line"></i>
                    <div class="stat-number" id="totalSignals">0</div>
                    <div class="stat-label">Total Señales</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-check-circle"></i>
                    <div class="stat-number" id="activeSignals">0</div>
                    <div class="stat-label">Señales Activas</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-trophy"></i>
                    <div class="stat-number" id="profitableSignals">0</div>
                    <div class="stat-label">Rentables</div>
                </div>
            </div>

            <div id="signalsLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando señales...
            </div>

            <table class="data-table" id="signalsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Par</th>
                        <th>Tipo</th>
                        <th>Precio Entrada</th>
                        <th>Stop Loss</th>
                        <th>Take Profit</th>
                        <th>Estado</th>
                        <th>Pips</th>
                        <th>Fecha</th>
                    </tr>
                </thead>
                <tbody id="signalsTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Noticias -->
        <div id="news" class="tab-content">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshNews()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestNews()">
                    <i class="fas fa-plus"></i> Agregar Noticia de Prueba
                </button>
            </div>

            <div id="newsLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando noticias...
            </div>

            <table class="data-table" id="newsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Título</th>
                        <th>Categoría</th>
                        <th>Impacto</th>
                        <th>Moneda</th>
                        <th>Fuente</th>
                        <th>Fecha</th>
                    </tr>
                </thead>
                <tbody id="newsTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Alertas -->
        <div id="alerts" class="tab-content">
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshAlerts()">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>
                <button class="btn btn-success" onclick="addTestAlert()">
                    <i class="fas fa-plus"></i> Agregar Alerta de Prueba
                </button>
            </div>

            <div id="alertsLoading" class="loading">
                <i class="fas fa-spinner"></i> Cargando alertas...
            </div>

            <table class="data-table" id="alertsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Tipo</th>
                        <th>Símbolo</th>
                        <th>Mensaje</th>
                        <th>Nivel</th>
                        <th>Estado</th>
                        <th>Fecha</th>
                    </tr>
                </thead>
                <tbody id="alertsTableBody">
                </tbody>
            </table>
        </div>

        <!-- Tab de Estadísticas -->
        <div id="stats" class="tab-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-database"></i>
                    <div class="stat-number" id="totalRecords">0</div>
                    <div class="stat-label">Total Registros</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-server"></i>
                    <div class="stat-number" id="serverStatus">OK</div>
                    <div class="stat-label">Estado Servidor</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-cloud"></i>
                    <div class="stat-number" id="awsStatus">OK</div>
                    <div class="stat-label">Estado AWS</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-clock"></i>
                    <div class="stat-number" id="lastUpdate">--</div>
                    <div class="stat-label">Última Actualización</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-info" onclick="testAWSConnection()">
                    <i class="fas fa-plug"></i> Probar Conexión AWS
                </button>
                <button class="btn btn-success" onclick="initializeAWS()">
                    <i class="fas fa-rocket"></i> Inicializar AWS
                </button>
                <button class="btn btn-primary" onclick="refreshAllData()">
                    <i class="fas fa-sync-alt"></i> Actualizar Todo
                </button>
            </div>
        </div>
    </div>

    <script src="admin-panel.js"></script>
</body>
</html>
