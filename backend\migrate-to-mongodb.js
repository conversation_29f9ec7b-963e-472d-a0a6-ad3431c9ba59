const sqlite3 = require('sqlite3').verbose();
const { connectDB, closeDB } = require('./config/mongodb');
const { User, Signal, News, Alert } = require('./models');

// Script de migración de SQLite a MongoDB
async function migrateData() {
    console.log('🔄 Iniciando migración de SQLite a MongoDB...');
    
    try {
        // Conectar a MongoDB
        console.log('🔄 Conectando a MongoDB...');
        await connectDB();
        console.log('✅ MongoDB conectado');

        // Conectar a SQLite
        console.log('🔄 Conectando a SQLite...');
        const sqliteDb = new sqlite3.Database('./germayori.db');
        console.log('✅ SQLite conectado');

        // Migrar usuarios
        await migrateUsers(sqliteDb);
        
        // Migrar señales
        await migrateSignals(sqliteDb);
        
        // Migrar noticias
        await migrateNews(sqliteDb);
        
        // Migrar alertas
        await migrateAlerts(sqliteDb);

        // Cerrar conexiones
        sqliteDb.close();
        await closeDB();
        
        console.log('✅ Migración completada exitosamente');
        
    } catch (error) {
        console.error('❌ Error en la migración:', error);
        process.exit(1);
    }
}

// Migrar usuarios
function migrateUsers(sqliteDb) {
    return new Promise((resolve, reject) => {
        console.log('🔄 Migrando usuarios...');
        
        sqliteDb.all('SELECT * FROM users', async (err, users) => {
            if (err) {
                reject(err);
                return;
            }

            try {
                let migratedCount = 0;
                
                for (const user of users) {
                    // Verificar si el usuario ya existe en MongoDB
                    const existingUser = await User.findOne({ email: user.email });
                    
                    if (!existingUser) {
                        const newUser = new User({
                            name: user.name,
                            email: user.email,
                            password: user.password,
                            type: user.type || 'user',
                            avatar: user.avatar || 'https://via.placeholder.com/40',
                            plan: 'basic',
                            planPrice: 45,
                            isActive: true,
                            createdAt: user.created_at ? new Date(user.created_at) : new Date(),
                            updatedAt: new Date()
                        });

                        await newUser.save();
                        migratedCount++;
                    }
                }
                
                console.log(`✅ Usuarios migrados: ${migratedCount}/${users.length}`);
                resolve();
                
            } catch (error) {
                reject(error);
            }
        });
    });
}

// Migrar señales
function migrateSignals(sqliteDb) {
    return new Promise((resolve, reject) => {
        console.log('🔄 Migrando señales...');
        
        sqliteDb.all('SELECT * FROM signals', async (err, signals) => {
            if (err) {
                reject(err);
                return;
            }

            try {
                let migratedCount = 0;
                
                for (const signal of signals) {
                    // Buscar el usuario correspondiente en MongoDB
                    const user = await User.findOne({ email: { $exists: true } }).skip(signal.user_id - 1).limit(1);
                    
                    if (user) {
                        const newSignal = new Signal({
                            user_id: user._id,
                            pair: signal.pair,
                            type: signal.type,
                            entry_price: signal.entry_price,
                            stop_loss: signal.stop_loss,
                            take_profit: signal.take_profit,
                            analysis: signal.analysis || '',
                            risk_level: signal.risk_level || 'medium',
                            timeframe: signal.timeframe || 'H1',
                            status: signal.status || 'active',
                            pips: signal.pips || 0,
                            profit: 0,
                            source: 'migrated',
                            confidence: 75,
                            createdAt: signal.created_at ? new Date(signal.created_at) : new Date(),
                            updatedAt: signal.updated_at ? new Date(signal.updated_at) : new Date()
                        });

                        await newSignal.save();
                        migratedCount++;
                    }
                }
                
                console.log(`✅ Señales migradas: ${migratedCount}/${signals.length}`);
                resolve();
                
            } catch (error) {
                reject(error);
            }
        });
    });
}

// Migrar noticias
function migrateNews(sqliteDb) {
    return new Promise((resolve, reject) => {
        console.log('🔄 Migrando noticias...');
        
        sqliteDb.all('SELECT * FROM news', async (err, news) => {
            if (err) {
                reject(err);
                return;
            }

            try {
                let migratedCount = 0;
                
                for (const newsItem of news) {
                    const newNews = new News({
                        title: newsItem.title,
                        content: newsItem.content || '',
                        category: newsItem.category || 'forex',
                        impact: newsItem.impact || 'medium',
                        currency: newsItem.currency || '',
                        source: newsItem.source || 'ForexFactory',
                        url: '',
                        imageUrl: '',
                        isActive: true,
                        publishedAt: newsItem.created_at ? new Date(newsItem.created_at) : new Date(),
                        createdAt: newsItem.created_at ? new Date(newsItem.created_at) : new Date(),
                        updatedAt: new Date()
                    });

                    await newNews.save();
                    migratedCount++;
                }
                
                console.log(`✅ Noticias migradas: ${migratedCount}/${news.length}`);
                resolve();
                
            } catch (error) {
                reject(error);
            }
        });
    });
}

// Migrar alertas
function migrateAlerts(sqliteDb) {
    return new Promise((resolve, reject) => {
        console.log('🔄 Migrando alertas...');
        
        sqliteDb.all('SELECT * FROM alerts', async (err, alerts) => {
            if (err) {
                reject(err);
                return;
            }

            try {
                let migratedCount = 0;
                
                for (const alert of alerts) {
                    // Buscar el usuario correspondiente en MongoDB
                    const user = await User.findOne({ email: { $exists: true } }).skip(alert.user_id - 1).limit(1);
                    
                    if (user) {
                        const newAlert = new Alert({
                            user_id: user._id,
                            type: alert.type,
                            symbol: alert.symbol || '',
                            message: alert.message,
                            level: alert.level || 'medium',
                            status: alert.status || 'active',
                            source: 'migrated',
                            isRead: false,
                            createdAt: alert.created_at ? new Date(alert.created_at) : new Date(),
                            updatedAt: new Date()
                        });

                        await newAlert.save();
                        migratedCount++;
                    }
                }
                
                console.log(`✅ Alertas migradas: ${migratedCount}/${alerts.length}`);
                resolve();
                
            } catch (error) {
                reject(error);
            }
        });
    });
}

// Ejecutar migración si se llama directamente
if (require.main === module) {
    migrateData();
}

module.exports = { migrateData };
