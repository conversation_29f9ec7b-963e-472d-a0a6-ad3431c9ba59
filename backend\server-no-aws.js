const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware básico
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, '..')));

// Crear/conectar base de datos
const db = new sqlite3.Database('./germayori.db', (err) => {
    if (err) {
        console.error('Error al conectar con la base de datos:', err);
    } else {
        console.log('✅ Conectado a la base de datos SQLite');
        initDatabase();
    }
});

// Inicializar tablas
function initDatabase() {
    // Tabla de usuarios
    db.run(`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        type TEXT DEFAULT 'user',
        avatar TEXT DEFAULT 'https://via.placeholder.com/40',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Tabla de señales
    db.run(`CREATE TABLE IF NOT EXISTS signals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        pair TEXT NOT NULL,
        type TEXT NOT NULL,
        entry_price REAL NOT NULL,
        stop_loss REAL NOT NULL,
        take_profit REAL NOT NULL,
        analysis TEXT,
        risk_level TEXT DEFAULT 'medium',
        timeframe TEXT DEFAULT 'H1',
        status TEXT DEFAULT 'active',
        pips REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`);

    // Tabla de noticias
    db.run(`CREATE TABLE IF NOT EXISTS news (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        category TEXT,
        impact TEXT DEFAULT 'medium',
        currency TEXT,
        source TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Tabla de alertas
    db.run(`CREATE TABLE IF NOT EXISTS alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        type TEXT NOT NULL,
        symbol TEXT,
        message TEXT NOT NULL,
        level TEXT DEFAULT 'medium',
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`);

    // Tabla de videos
    db.run(`CREATE TABLE IF NOT EXISTS videos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        url TEXT,
        filename TEXT,
        size INTEGER,
        type TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    console.log('✅ Tablas de base de datos inicializadas');
}

// RUTAS DE SALUD
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// RUTAS DE USUARIOS
app.get('/api/users', (req, res) => {
    db.all('SELECT * FROM users ORDER BY created_at DESC', (err, users) => {
        if (err) {
            console.error('Error obteniendo usuarios:', err);
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            console.log(`📊 Enviando ${users.length} usuarios`);
            res.json(users);
        }
    });
});

app.get('/api/aws/users', (req, res) => {
    // Fallback a SQLite cuando AWS no funciona
    db.all('SELECT * FROM users ORDER BY created_at DESC', (err, users) => {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json({ success: true, data: users });
        }
    });
});

app.post('/api/register', (req, res) => {
    const { name, email, password } = req.body;

    db.run('INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
        [name, email, password],
        function(err) {
            if (err) {
                if (err.message.includes('UNIQUE constraint failed')) {
                    res.status(400).json({ error: 'El email ya está registrado' });
                } else {
                    res.status(500).json({ error: 'Error del servidor' });
                }
            } else {
                res.json({
                    success: true,
                    user: {
                        id: this.lastID,
                        name,
                        email,
                        type: 'user',
                        avatar: 'https://via.placeholder.com/40'
                    }
                });
            }
        }
    );
});

// RUTAS DE SEÑALES
app.get('/api/signals', (req, res) => {
    db.all(`SELECT s.*, u.name as user_name
            FROM signals s
            LEFT JOIN users u ON s.user_id = u.id
            ORDER BY s.created_at DESC`,
        (err, signals) => {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json(signals);
            }
        }
    );
});

app.get('/api/aws/signals', (req, res) => {
    // Fallback a SQLite
    db.all(`SELECT s.*, u.name as user_name
            FROM signals s
            LEFT JOIN users u ON s.user_id = u.id
            ORDER BY s.created_at DESC`,
        (err, signals) => {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, data: signals });
            }
        }
    );
});

app.post('/api/signals', (req, res) => {
    const { user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe } = req.body;

    db.run(`INSERT INTO signals (user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

// RUTAS DE NOTICIAS
app.get('/api/news', (req, res) => {
    db.all('SELECT * FROM news ORDER BY created_at DESC LIMIT 20', (err, news) => {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json(news);
        }
    });
});

app.post('/api/news', (req, res) => {
    const { title, content, category, impact, currency, source } = req.body;

    db.run('INSERT INTO news (title, content, category, impact, currency, source) VALUES (?, ?, ?, ?, ?, ?)',
        [title, content, category, impact, currency, source],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

// RUTAS DE ALERTAS
app.get('/api/alerts', (req, res) => {
    db.all('SELECT * FROM alerts ORDER BY created_at DESC LIMIT 50', (err, alerts) => {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json(alerts);
        }
    });
});

app.post('/api/alerts', (req, res) => {
    const { user_id, type, symbol, message, level } = req.body;

    db.run('INSERT INTO alerts (user_id, type, symbol, message, level) VALUES (?, ?, ?, ?, ?)',
        [user_id, type, symbol, message, level],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

// RUTAS DE VIDEOS
app.get('/api/videos', (req, res) => {
    db.all('SELECT * FROM videos ORDER BY created_at DESC', (err, videos) => {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json(videos);
        }
    });
});

app.post('/api/videos', (req, res) => {
    const { title, description, category, url, filename, size, type } = req.body;

    db.run('INSERT INTO videos (title, description, category, url, filename, size, type) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [title, description, category, url, filename, size, type],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

// RUTAS MOCK PARA AWS
app.get('/api/aws/test', (req, res) => {
    res.json({ success: false, aws: 'disabled', message: 'AWS deshabilitado temporalmente' });
});

app.post('/api/aws/init', (req, res) => {
    res.json({ success: false, message: 'AWS no disponible en modo simple' });
});

// Ruta para servir archivos HTML
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index.html'));
});

// Iniciar servidor
app.listen(PORT, () => {
    console.log(`🚀 Servidor GERMAYORI SIMPLE corriendo en http://localhost:${PORT}`);
    console.log(`📊 Panel Admin: http://localhost:${PORT}/admin-panel.html`);
    console.log(`💾 Usando SQLite (AWS deshabilitado)`);
});

// Manejar cierre graceful
process.on('SIGINT', () => {
    console.log('🔄 Cerrando servidor GERMAYORI...');

    db.close((err) => {
        if (err) {
            console.error(err.message);
        } else {
            console.log('✅ Conexión a base de datos cerrada');
        }
        console.log('✅ Servidor GERMAYORI cerrado correctamente');
        process.exit(0);
    });
});
