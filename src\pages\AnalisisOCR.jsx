import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Upload, Crown, Camera, Image } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import PaywallModal from '../components/Paywall/PaywallModal';

const AnalisisOCR = () => {
  const { user } = useAuth();
  const [showPaywall, setShowPaywall] = useState(!user?.isPaid);

  if (!user?.isPaid) {
    return (
      <>
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="w-24 h-24 bg-warning bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="text-warning" size={48} />
            </div>
            <h1 className="text-3xl font-bold text-text mb-4">Análisis OCR Premium</h1>
            <p className="text-gray-400 max-w-2xl mx-auto mb-8">
              Sube capturas de pantalla de gráficos y obtén análisis técnico automático 
              usando tecnología de reconocimiento óptico de caracteres avanzada.
            </p>
            <Button variant="primary" onClick={() => setShowPaywall(true)}>
              Desbloquear Análisis OCR
            </Button>
          </motion.div>
        </div>
        <PaywallModal 
          isOpen={showPaywall} 
          onClose={() => setShowPaywall(false)} 
          feature="Análisis OCR"
        />
      </>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-text mb-2 flex items-center">
            <FileText className="mr-3 text-danger" size={32} />
            Análisis OCR
            <Crown className="ml-2 text-warning" size={24} />
          </h1>
          <p className="text-gray-400">
            Análisis automático de gráficos mediante OCR
          </p>
        </div>
      </motion.div>

      <Card>
        <div className="text-center py-12">
          <Upload className="mx-auto mb-4 text-gray-400" size={64} />
          <h3 className="text-xl font-semibold text-text mb-2">
            Sube tu captura de pantalla
          </h3>
          <p className="text-gray-400 mb-6">
            Arrastra y suelta o haz clic para seleccionar una imagen
          </p>
          <Button variant="primary">
            Seleccionar Imagen
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default AnalisisOCR;
