import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Usuarios de prueba
  const testUsers = [
    {
      id: 1,
      email: '<EMAIL>',
      password: '123456',
      name: 'Usuario GERMAYORI',
      isPaid: true,
      plan: 'Premium',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 2,
      email: '<EMAIL>',
      password: '123456',
      name: '<PERSON><PERSON><PERSON>uit<PERSON>',
      isPaid: false,
      plan: '<PERSON><PERSON><PERSON><PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    }
  ];

  // Verificar si hay un usuario logueado al cargar
  useEffect(() => {
    const token = localStorage.getItem('germayori_token');
    const userData = localStorage.getItem('germayori_user');
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Error parsing user data:', error);
        localStorage.removeItem('germayori_token');
        localStorage.removeItem('germayori_user');
      }
    }
    setLoading(false);
  }, []);

  // Función de login
  const login = async (email, password) => {
    try {
      setLoading(true);
      
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Buscar usuario en la lista de prueba
      const foundUser = testUsers.find(u => u.email === email && u.password === password);
      
      if (!foundUser) {
        throw new Error('Credenciales incorrectas');
      }

      // Generar token simulado
      const token = `germayori_token_${foundUser.id}_${Date.now()}`;
      
      // Guardar en localStorage
      localStorage.setItem('germayori_token', token);
      localStorage.setItem('germayori_user', JSON.stringify(foundUser));
      
      // Actualizar estado
      setUser(foundUser);
      setIsAuthenticated(true);
      
      return { success: true, user: foundUser };
    } catch (error) {
      console.error('Error en login:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Función de registro
  const register = async (userData) => {
    try {
      setLoading(true);
      
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Verificar si el email ya existe
      const existingUser = testUsers.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('El email ya está registrado');
      }

      // Crear nuevo usuario
      const newUser = {
        id: testUsers.length + 1,
        email: userData.email,
        name: userData.name,
        isPaid: false, // Los nuevos usuarios empiezan como gratuitos
        plan: 'Gratuito',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face'
      };

      // Agregar a la lista (en una app real, esto se haría en el backend)
      testUsers.push(newUser);
      
      // Generar token
      const token = `germayori_token_${newUser.id}_${Date.now()}`;
      
      // Guardar en localStorage
      localStorage.setItem('germayori_token', token);
      localStorage.setItem('germayori_user', JSON.stringify(newUser));
      
      // Actualizar estado
      setUser(newUser);
      setIsAuthenticated(true);
      
      return { success: true, user: newUser };
    } catch (error) {
      console.error('Error en registro:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Función de logout
  const logout = () => {
    localStorage.removeItem('germayori_token');
    localStorage.removeItem('germayori_user');
    setUser(null);
    setIsAuthenticated(false);
  };

  // Función para actualizar plan del usuario
  const upgradeToPaid = () => {
    if (user) {
      const updatedUser = { ...user, isPaid: true, plan: 'Premium' };
      setUser(updatedUser);
      localStorage.setItem('germayori_user', JSON.stringify(updatedUser));
    }
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout,
    upgradeToPaid
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
